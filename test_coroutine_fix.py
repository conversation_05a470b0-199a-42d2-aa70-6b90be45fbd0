#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试协程未等待问题的修复
"""

import asyncio
import warnings
import sys
import os
import time
from unittest.mock import Mock, AsyncMock

# 添加项目路径
sys.path.append('.')

# 设置警告过滤器来捕获协程未等待的警告
warnings.filterwarnings('error', category=RuntimeWarning, message='.*coroutine.*was never awaited.*')

def test_basic_async():
    """测试基本异步功能"""
    print("🔍 测试基本异步功能...")
    
    async def basic_test():
        await asyncio.sleep(0.1)
        return "success"
    
    try:
        result = asyncio.run(basic_test())
        print(f"✅ 基本异步测试通过: {result}")
        return True
    except RuntimeWarning as e:
        print(f"❌ 基本异步测试失败: {e}")
        return False
    except Exception as e:
        print(f"⚠️ 基本异步测试出现其他错误: {e}")
        return False

def test_gather_pattern():
    """测试 asyncio.gather 模式"""
    print("🔍 测试 asyncio.gather 模式...")
    
    async def mock_task(name, delay=0.1):
        await asyncio.sleep(delay)
        return f"{name}_result"
    
    async def gather_test():
        # 模拟修复后的模式
        async def run_task1():
            return await mock_task("task1")
        
        async def run_task2():
            return await mock_task("task2")
        
        async def run_task3():
            return await mock_task("task3")
        
        results = await asyncio.gather(
            run_task1(),
            run_task2(),
            run_task3(),
        )
        return results
    
    try:
        results = asyncio.run(gather_test())
        print(f"✅ gather 模式测试通过: {len(results)} 个任务完成")
        return True
    except RuntimeWarning as e:
        print(f"❌ gather 模式测试失败: {e}")
        return False
    except Exception as e:
        print(f"⚠️ gather 模式测试出现其他错误: {e}")
        return False

def test_task_cancellation():
    """测试任务取消"""
    print("🔍 测试任务取消...")
    
    async def long_running_task():
        try:
            await asyncio.sleep(10)  # 长时间运行的任务
            return "completed"
        except asyncio.CancelledError:
            print("  任务被正确取消")
            raise
    
    async def cancellation_test():
        # 创建任务
        task = asyncio.create_task(long_running_task())
        
        # 等待一小段时间
        await asyncio.sleep(0.1)
        
        # 取消任务
        task.cancel()
        
        try:
            await task
        except asyncio.CancelledError:
            pass  # 预期的取消异常
        
        return "cancellation_test_passed"
    
    try:
        result = asyncio.run(cancellation_test())
        print(f"✅ 任务取消测试通过: {result}")
        return True
    except RuntimeWarning as e:
        print(f"❌ 任务取消测试失败: {e}")
        return False
    except Exception as e:
        print(f"⚠️ 任务取消测试出现其他错误: {e}")
        return False

def test_event_loop_cleanup():
    """测试事件循环清理"""
    print("🔍 测试事件循环清理...")
    
    async def cleanup_test():
        # 创建一些任务
        tasks = []
        
        async def dummy_task(task_id):
            try:
                await asyncio.sleep(1)
                return f"task_{task_id}_completed"
            except asyncio.CancelledError:
                print(f"  任务 {task_id} 被取消")
                raise
        
        # 创建多个任务
        for i in range(3):
            task = asyncio.create_task(dummy_task(i))
            tasks.append(task)
        
        # 等待一小段时间
        await asyncio.sleep(0.1)
        
        # 取消所有任务
        for task in tasks:
            if not task.done():
                task.cancel()
        
        # 等待所有任务完成（包括取消）
        try:
            await asyncio.gather(*tasks, return_exceptions=True)
        except Exception:
            pass  # 忽略取消异常
        
        return "cleanup_test_passed"
    
    try:
        result = asyncio.run(cleanup_test())
        print(f"✅ 事件循环清理测试通过: {result}")
        return True
    except RuntimeWarning as e:
        print(f"❌ 事件循环清理测试失败: {e}")
        return False
    except Exception as e:
        print(f"⚠️ 事件循环清理测试出现其他错误: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 50)
    print("🧪 开始协程未等待问题修复测试")
    print("=" * 50)
    
    test_results = []
    
    # 运行各项测试
    test_results.append(test_basic_async())
    test_results.append(test_gather_pattern())
    test_results.append(test_task_cancellation())
    test_results.append(test_event_loop_cleanup())
    
    # 统计结果
    passed = sum(test_results)
    total = len(test_results)
    
    print("=" * 50)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！协程未等待问题已修复")
        return True
    else:
        print("❌ 部分测试失败，可能仍存在协程未等待问题")
        return False

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⚠️ 测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 测试过程中发生未预期的错误: {e}")
        sys.exit(1)
