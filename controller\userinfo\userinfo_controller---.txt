J:\ai\�������\ai-yinmei\controller\userinfo\userinfo_controller.c      userinfo_controller.py  get     userinfo_controller.UserList.get        userinfo_controller.ChatList.get        userinfo_controller.ScoreRecord.get     userinfo_controller             Interpreter change detected - this module can only be loaded into one interpreter per process.  name    __loader__  loader      __file__    origin      __package__ parent      __path__        submodule_search_locations      Module 'userinfo_controller' has already been imported. Re-initialisation is not supported.     builtins        cython_runtime  __builtins__    __orig_bases__  init userinfo_controller        %s() got multiple values for keyword argument '%U'      %.200s() keywords must be strings       %s() got an unexpected keyword argument '%U'    exactly %.200s() takes %.8s %zd positional argument%.1s (%zd given)     name '%U' is not defined         while calling a Python object  NULL result without error in PyObject_Call      cannot import name %S   __mro_entries__ must return a tuple             metaclass conflict: the metaclass of a derived class must be a (non-strict) subclass of the metaclasses of all its bases        _cython_3_0_11  Shared Cython type %.200s is not a type object  Shared Cython type %.200s has the wrong size, try recompiling   keywords must be strings        __name__ must be set to a string object __qualname__ must be set to a string object     function's dictionary may not be deleted        setting function's dictionary to a non-dict     __defaults__ must be set to a tuple object              changes to cyfunction.__defaults__ will not currently affect the values used in function calls  __kwdefaults__ must be set to a dict object     changes to cyfunction.__kwdefaults__ will not currently affect the values used in function calls        __annotations__ must be set to a dict object    func_doc        __doc__ func_name       __name__        __qualname__    func_dict       __dict__        func_globals    __globals__     func_closure    __closure__     func_code       __code__        func_defaults   __defaults__    __kwdefaults__  __annotations__ _is_coroutine   __module__      __reduce__      Bad call flags for CyFunction   <cyfunction %U at %p>   %.200s() takes no arguments (%zd given) %.200s() takes exactly one argument (%zd given) %.200s() takes no keyword arguments     unbound method %.200S() needs an argument       %.200s() needs an argument      _cython_3_0_11.cython_function_or_method        %s (%s:%d)      does not match          compile time Python version %d.%d of module '%.100s' %s runtime version %d.%d   cline_in_traceback      func.user.user_db   oper        (� �   __prepare__     find_score_record_page  .   chatDB      __main__        request __qualname__    __module__      __mro_entries__ self    username        __dict__        � �   prompt  jsonify page_size       UserList.get    ChatDB  _is_coroutine   super   intent  __import__      __doc__ J:\ai\吟美打包\ai-yinmei\controller\userinfo\userinfo_controller.py __set_name__    status  userinfo_controller     UserList        find_user_list_page     __name__        ChatList.get    __init_subclass__   get data    BaseView        __test__        func.score.score_db pgnum       func.llm.chat_db    pgsize  UserDB      ScoreRecord.get ChatList        __metaclass__   scoreDB flask   args    成功  page_number     asyncio.coroutines      find_chat_list_page     controller.baseview ?   userDB  ScoreDB ScoreRecord