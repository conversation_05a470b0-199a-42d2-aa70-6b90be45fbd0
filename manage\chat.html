<!DOCTYPE html>
<html lang="cn">
<head>
    <meta charset="utf-8">
    <title>聊天对话</title>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="description" content="Ai吟美管理后台">
    <meta name="author" content="keke">

    <!-- The styles -->
    <link id="bs-css" href="css/bootstrap-cerulean.min.css" rel="stylesheet">

    <link href="css/charisma-app.css" rel="stylesheet">
    <link href='bower_components/fullcalendar/dist/fullcalendar.css' rel='stylesheet'>
    <link href='bower_components/fullcalendar/dist/fullcalendar.print.css' rel='stylesheet' media='print'>
    <link href='bower_components/chosen/chosen.min.css' rel='stylesheet'>
    <link href='bower_components/colorbox/example3/colorbox.css' rel='stylesheet'>
    <link href='bower_components/responsive-tables/responsive-tables.css' rel='stylesheet'>
    <link href='bower_components/bootstrap-tour/build/css/bootstrap-tour.min.css' rel='stylesheet'>
    <link href='css/jquery.noty.css' rel='stylesheet'>
    <link href='css/noty_theme_default.css' rel='stylesheet'>
    <link href='css/elfinder.min.css' rel='stylesheet'>
    <link href='css/elfinder.theme.css' rel='stylesheet'>
    <link href='css/jquery.iphone.toggle.css' rel='stylesheet'>
    <link href='css/uploadify.css' rel='stylesheet'>
    <link href='css/animate.min.css' rel='stylesheet'>
    <link href='css/extend.css' rel='stylesheet'>
    <link href='css/chat.css' rel='stylesheet'>
    <!-- jQuery -->
    <script src="bower_components/jquery/jquery.min.js"></script>
    <!--jbox-->
    <link id="skin" rel="stylesheet" href="bower_components/jBox/Skins/Red/jbox.css" />
    <script type="text/javascript" src="bower_components/jBox/jquery.jBox-2.3.min.js"></script>
    <script type="text/javascript" src="bower_components/jBox/i18n/jquery.jBox-zh-CN.js"></script>
    <!--vue-->
    <script src="js/vue.3.5.13.js"></script>
    <script src="js/axios.min.js"></script>
    <script src="config.js"></script>
    <!-- The fav icon -->
    <link rel="shortcut icon" href="img/favicon.ico">

    <script type="text/javascript">
        async function loadMenu() {
            const response = await fetch('menu.html', {cache: 'force-cache'});
            const menuHtml = await response.text();
            document.getElementById('menu').innerHTML = menuHtml;
        }

        async function loadFooter() {
            const response = await fetch('footer.html', {cache: 'force-cache'});
            const footerHtml = await response.text();
            document.getElementById('footer').innerHTML = footerHtml;
        }

        async function loadTopbar() {
            const response = await fetch('topbar.html', {cache: 'force-cache'});
            const topbarHtml = await response.text();
            document.getElementById('topbar').innerHTML = topbarHtml;

            // 菜单加载完成后，加载其他JavaScript文件
            const script = document.createElement('script');
            script.src = 'js/charisma.js';
            document.head.appendChild(script);
        }

        // DOMContentLoaded 事件确保 DOM 已经加载完成
        document.addEventListener('DOMContentLoaded', () => {
            loadMenu();
            loadTopbar();
            loadFooter();
        });
    </script>
</head>

<body>
<!-- topbar starts -->
<div id="topbar"></div>
<!-- topbar ends -->
<div class="ch-container">
    <div class="row">

        <!-- left menu starts -->
        <div id="menu"></div>
        <!-- left menu ends -->


        <div id="content" class="col-lg-10 col-sm-10">
            <!-- content starts -->
            <div>
                <ul class="breadcrumb">
                    <li>
                        <a href="index.html">Home</a>
                    </li>
                    <li>
                        <a href="emote.html">聊天对话</a>
                    </li>
                </ul>
            </div>

            <div class="row">
                <div class="box col-md-6" style="width: 70%;float: left">
                    <div class="box-inner">
                        <div class="box-header well" data-original-title="">
                            <h2><i class="glyphicon glyphicon-th"></i> 聊天对话</h2>

                            <div class="box-icon">
                                <a href="#" class="btn btn-setting btn-round btn-default"><i
                                        class="glyphicon glyphicon-cog"></i></a>
                                <a href="#" class="btn btn-minimize btn-round btn-default"><i
                                        class="glyphicon glyphicon-chevron-up"></i></a>
                                <a href="#" class="btn btn-close btn-round btn-default"><i
                                        class="glyphicon glyphicon-remove"></i></a>
                            </div>
                        </div>
                        <div class="box-content">
                            <!-- 连接状态指示器 -->
                            <div class="connection-status" :class="connectionStatus">
                                {{ connectionStatusText }}
                            </div>

                            <!-- 聊天区域 -->
                            <div class="row">
                                <div id="outer">
                                    <div id="move-area">
                                        <div id="chatArea">
                                            <!-- 聊天消息将在这里动态添加 -->
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 输入区域 -->
                            <div class="input-section" style="margin-top: 20px;">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="input-group">
                                            <label>用户ID:</label>
                                            <input id="uid" type="text" v-model="request.uid" placeholder="请输入用户ID">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="input-group">
                                            <label>用户名:</label>
                                            <input id="username" type="text" v-model="request.username" placeholder="请输入用户名">
                                        </div>
                                    </div>
                                </div>

                                <div class="input-group">
                                    <label>消息内容:</label>
                                    <textarea
                                        id="msg"
                                        class="autogrow"
                                        v-model="request.msg"
                                        placeholder="请输入您要发送的消息..."
                                        @keydown.enter.ctrl="send"
                                        @keydown.enter.exact.prevent="send"
                                        rows="4"
                                    ></textarea>
                                    <small class="text-muted">按 Enter 发送，Ctrl+Enter 换行</small>
                                </div>

                                <div class="button-group" style="margin-top: 15px;">
                                    <button
                                        id="send"
                                        type="button"
                                        class="btn-chat"
                                        @click="send"
                                        :disabled="!request.msg.trim() || connectionStatus !== 'connected'"
                                    >
                                        <i class="glyphicon glyphicon-send"></i> 发送消息
                                    </button>
                                    <button
                                        id="read"
                                        type="button"
                                        class="btn-chat secondary"
                                        @click="read"
                                        :disabled="!request.msg.trim()"
                                    >
                                        <i class="glyphicon glyphicon-volume-up"></i> 朗读
                                    </button>
                                    <button
                                        type="button"
                                        class="btn-chat secondary"
                                        @click="clearChat"
                                    >
                                        <i class="glyphicon glyphicon-trash"></i> 清空聊天
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="box col-md-6" style="width: 30%; height: 933px;float: left">
                    <iframe id="chatFrame" :src="iframeSrc"
                            style="width: 100%; height: 933px; border: #949494 1px solid;">

                    </iframe>
                </div>
            </div>
            <!-- content ends -->
        </div><!--/#content.col-md-0-->
    </div><!--/fluid-row-->

    <div class="modal fade" id="tipModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true" >
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal">×</button>
                    <h3>提示</h3>
                </div>
                <div class="modal-body">
                    Here settings can be configured...
                </div>
            </div>
        </div>
    </div>

    <hr>
    <!-- 底部 -->
    <div id="footer"></div>
    <!-- end -->

</div><!--/.fluid-container-->

<!-- external javascript -->

<script src="bower_components/bootstrap/dist/js/bootstrap.min.js"></script>

<!-- library for cookie management -->
<script src="js/jquery.cookie.js"></script>
<!-- calender plugin -->
<script src='bower_components/moment/min/moment.min.js'></script>
<script src='bower_components/fullcalendar/dist/fullcalendar.min.js'></script>
<!-- data table plugin -->
<script src='js/jquery.dataTables.min.js'></script>

<!-- select or dropdown enhancer -->
<script src="bower_components/chosen/chosen.jquery.min.js"></script>
<!-- plugin for gallery image view -->
<script src="bower_components/colorbox/jquery.colorbox-min.js"></script>
<!-- notification plugin -->
<script src="js/jquery.noty.js"></script>
<!-- library for making tables responsive -->
<script src="bower_components/responsive-tables/responsive-tables.js"></script>
<!-- tour plugin -->
<script src="bower_components/bootstrap-tour/build/js/bootstrap-tour.min.js"></script>
<!-- star rating plugin -->
<script src="js/jquery.raty.min.js"></script>
<!-- for iOS style toggle switch -->
<script src="js/jquery.iphone.toggle.js"></script>
<!-- autogrowing textarea plugin -->
<script src="js/jquery.autogrow-textarea.js"></script>
<!-- multiple file upload plugin -->
<script src="js/jquery.uploadify-3.1.min.js"></script>
<!-- history.js for cross-browser state change on ajax -->
<script src="js/jquery.history.js"></script>
<!-- application script for Charisma demo -->
<!--<script src="js/charisma.js"></script>-->

<script>
    function getFormattedCurrentTime() {
        const now = new Date();

        // 获取各个时间部分
        const year = now.getFullYear();
        const month = String(now.getMonth() + 1).padStart(2, '0'); // 月份从0开始，所以需要加1
        const day = String(now.getDate()).padStart(2, '0');

        const hours = String(now.getHours()).padStart(2, '0');
        const minutes = String(now.getMinutes()).padStart(2, '0');

        // 格式化为 "YYYY-MM-DD HH:mm"
        const formattedTime = `${year}-${month}-${day} ${hours}:${minutes}`;

        return formattedTime;
    }

    const { createApp, reactive, onMounted, ref } = Vue;

    const app = createApp({
        methods: {
            refreshPage() {
                location.reload();
            },
            async restart() {
                try {
                    await axios.post(window.AppConfig.apiUrl+'/restart');
                    this.showNotification('系统重启命令已发送', 'success');
                } catch (error) {
                    this.showNotification('重启失败: ' + error.message, 'error');
                }
            },

            // 聊天发送
            async send() {
                if (!this.request.msg.trim()) {
                    this.showNotification('请输入消息内容', 'warning');
                    return;
                }

                if (this.connectionStatus !== 'connected') {
                    this.showNotification('WebSocket未连接，无法发送消息', 'error');
                    return;
                }

                try {
                    // 添加用户消息到聊天区域
                    this.addUserMessage(this.request.msg);

                    const payload = {
                        uid: this.request.uid,
                        username: this.request.username,
                        msg: this.request.msg,
                        uface: ""
                    };

                    // 发起聊天请求
                    const response = await axios.post(
                        window.AppConfig.apiUrl + '/msg',
                        payload,
                        {
                            headers: {'Content-Type': 'application/json'},
                            timeout: 10000
                        }
                    );

                    console.log('发送成功:', response.data);

                    // 清空输入框
                    this.request.msg = '';

                } catch (error) {
                    console.error('发送失败:', error);
                    this.showNotification('发送失败: ' + error.message, 'error');
                }
            },
            // 朗读功能
            async read() {
                if (!this.request.msg.trim()) {
                    this.showNotification('请输入要朗读的内容', 'warning');
                    return;
                }

                try {
                    const response = await axios.post(
                        window.AppConfig.apiUrl + '/say',
                        this.request.msg,
                        {
                            headers: {'Content-Type': 'text/plain'},
                            timeout: 5000
                        }
                    );
                    this.showNotification('朗读请求已发送', 'success');
                    console.log('朗读成功:', response.data);
                } catch (error) {
                    console.error('朗读失败:', error);
                    this.showNotification('朗读失败: ' + error.message, 'error');
                }
            },

            // 清空聊天记录
            clearChat() {
                if (confirm('确定要清空所有聊天记录吗？')) {
                    $("#chatArea").empty();
                    this.showNotification('聊天记录已清空', 'success');
                }
            },

            // 添加用户消息
            addUserMessage(message) {
                const div = `
                    <div class="message right">
                        <div class="content">
                            <p>${this.escapeHtml(message)}</p>
                            <div class="time right">${getFormattedCurrentTime()}</div>
                        </div>
                        <img src="./img/user-head.png" alt="avatar" class="avatar">
                    </div>
                `;
                $("#chatArea").append(div);
                this.scrollToBottom();
            },

            // 添加AI回复消息
            addAIMessage(message, isFirst = false) {
                if (isFirst) {
                    const div = `
                        <div class="message left">
                            <img src="./img/yinmei-head.png" alt="avatar" class="avatar">
                            <div class="content">
                                <p></p>
                                <div class="time left">${getFormattedCurrentTime()}</div>
                            </div>
                        </div>
                    `;
                    $("#chatArea").append(div);
                }

                // 追加文本到最后一个AI消息
                $(".message.left:last .content p").append(this.escapeHtml(message));
                this.scrollToBottom();
            },

            // 滚动到底部
            scrollToBottom() {
                const outer = $("#outer");
                outer.scrollTop($("#move-area").height());
            },

            // HTML转义
            escapeHtml(text) {
                const div = document.createElement('div');
                div.textContent = text;
                return div.innerHTML;
            },

            // 显示通知
            showNotification(message, type = 'info') {
                // 这里可以使用更好的通知组件，暂时使用alert
                if (type === 'error') {
                    console.error(message);
                } else {
                    console.log(message);
                }
                // 可以集成toast通知组件
            },

            tipClick(event){
                console.log('Event Target:', event.currentTarget);
                const buttonId = event.currentTarget.getAttribute('id');
                const buttonAlt = event.currentTarget.getAttribute('alt');
                $('.modal-body').text(buttonAlt);
                $('#tipModal').modal('show');
            }
        },
        setup() {
            const info = reactive({});
            const iframeSrc = ref('');
            const request = reactive({
                username: "keke",
                uid: "2005675a2cfc46a89e56d78193365ff9",
                msg: ""
            });

            // WebSocket连接状态
            const connectionStatus = ref('disconnected');
            const connectionStatusText = ref('未连接');
            let ws = null;
            let reconnectTimer = null;
            let reconnectAttempts = 0;
            const maxReconnectAttempts = 5;

            // 更新连接状态
            const updateConnectionStatus = (status) => {
                connectionStatus.value = status;
                switch(status) {
                    case 'connected':
                        connectionStatusText.value = '已连接';
                        reconnectAttempts = 0;
                        break;
                    case 'connecting':
                        connectionStatusText.value = '连接中...';
                        break;
                    case 'disconnected':
                        connectionStatusText.value = '未连接';
                        break;
                    case 'reconnecting':
                        connectionStatusText.value = `重连中... (${reconnectAttempts}/${maxReconnectAttempts})`;
                        break;
                }
            };

            // WebSocket连接函数
            const connectWebSocket = () => {
                try {
                    updateConnectionStatus('connecting');
                    ws = new WebSocket(window.AppConfig.ws);

                    ws.onopen = function () {
                        console.log("WebSocket连接成功");
                        updateConnectionStatus('connected');
                    };

                    ws.onmessage = function (event) {
                        try {
                            console.log("收到消息:", event.data);
                            const jsonData = JSON.parse(event.data);
                            Object.assign(info, jsonData);

                            if (jsonData.type === "聊天回复") {
                                console.log("处理聊天回复:", jsonData);

                                // 第一个片段，创建新的AI消息区域
                                if (jsonData.index === 0) {
                                    const div = `
                                        <div class="message left">
                                            <img src="./img/yinmei-head.png" alt="avatar" class="avatar">
                                            <div class="content">
                                                <p></p>
                                                <div class="time left">${getFormattedCurrentTime()}</div>
                                            </div>
                                        </div>
                                    `;
                                    $("#chatArea").append(div);
                                }

                                // 追加文本到最后一个AI消息
                                $(".message.left:last .content p").append(jsonData.text || '');

                                // 滚动到底部
                                $("#outer").scrollTop($("#move-area").height());
                            }
                        } catch (error) {
                            console.error("处理WebSocket消息失败:", error);
                        }
                    };

                    ws.onclose = function (event) {
                        console.log('WebSocket连接已关闭', event.code, event.reason);
                        updateConnectionStatus('disconnected');

                        // 自动重连
                        if (reconnectAttempts < maxReconnectAttempts) {
                            reconnectAttempts++;
                            updateConnectionStatus('reconnecting');
                            reconnectTimer = setTimeout(() => {
                                console.log(`尝试重连WebSocket... (${reconnectAttempts}/${maxReconnectAttempts})`);
                                connectWebSocket();
                            }, 3000 * reconnectAttempts); // 递增延迟
                        } else {
                            console.error("WebSocket重连次数已达上限");
                            updateConnectionStatus('disconnected');
                        }
                    };

                    ws.onerror = function (error) {
                        console.error('WebSocket错误:', error);
                        updateConnectionStatus('disconnected');
                    };

                } catch (error) {
                    console.error('WebSocket连接失败:', error);
                    updateConnectionStatus('disconnected');
                }
            };

            const fetchInfo = async () => {
                try {
                    // 获取直播间信息
                    const inputNames = ['danmaku.blivedm.ROOM_OWNER_AUTH_CODE'];
                    const response = await axios.post(window.AppConfig.apiUrl + '/oper/read_yml', inputNames);
                    console.log('获取配置信息:', response.data);

                    const roomCode = response.data["danmaku.blivedm.ROOM_OWNER_AUTH_CODE"];
                    if (roomCode) {
                        iframeSrc.value = window.AppConfig.blive + 'room/' + roomCode +
                            '?roomKeyType=2&showGiftName=true&showDebugMessages=true&lang=zh';
                    }

                    // 连接WebSocket
                    connectWebSocket();

                } catch (error) {
                    console.error('获取信息失败:', error);
                    // 即使获取配置失败，也尝试连接WebSocket
                    connectWebSocket();
                }
            };

            onMounted(() => {
                fetchInfo();
            });

            // 组件卸载时清理
            const cleanup = () => {
                if (ws && ws.readyState === WebSocket.OPEN) {
                    ws.close();
                }
                if (reconnectTimer) {
                    clearTimeout(reconnectTimer);
                }
            };

            // 手动重连
            const manualReconnect = () => {
                cleanup();
                reconnectAttempts = 0;
                connectWebSocket();
            };

            const handleSubmit = async () => {
                try {
                    // 这里可以添加其他提交逻辑
                } catch (error) {
                    console.error('提交失败', error);
                }
            };

            return {
                info,
                request,
                handleSubmit,
                iframeSrc,
                connectionStatus,
                connectionStatusText,
                manualReconnect,
                cleanup
            };
        }
    }).mount('#content');
</script>


</body>
</html>
