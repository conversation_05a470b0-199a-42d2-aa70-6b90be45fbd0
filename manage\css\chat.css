#outer{
    width: 95%;
    height: 600px;
    overflow-y: auto;
    margin: 0 auto;
    box-shadow: inset -1px -1px 2px #888, 0 2px 8px rgba(0,0,0,0.1);
    background: linear-gradient(to bottom, #ffffff, #f8f8f8);
    padding: 15px 10px;
    border-radius: 8px;
    border: 1px solid #e0e0e0;
}

#move-area{
    width: auto;
    height: auto;
    min-height: 100%;
}

#chatArea{
    width: 100%;
    font-size: 16px;
    word-wrap: break-word;
    overflow-y: auto;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.5;
    padding: 10px 0;
}

.message {
    display: flex;
    align-items: flex-end;
    margin-bottom: 15px;
    animation: fadeInUp 0.3s ease-out;
    opacity: 0;
    animation-fill-mode: forwards;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.message.left {
    justify-content: flex-start;
    margin-right: 15px;
}

.message.right {
    justify-content: flex-end;
    margin-left: 15px;
}

.avatar {
    width: 45px;
    height: 45px;
    border-radius: 50%;
    margin: 0 12px;
    box-shadow: 0 2px 6px rgba(0,0,0,0.15);
    border: 2px solid #fff;
    transition: transform 0.2s ease;
}

.avatar:hover {
    transform: scale(1.05);
}

.content {
    max-width: 70%;
    position: relative;
}

.content p {
    max-width: 100%;
    padding: 12px 16px;
    border-radius: 18px;
    word-wrap: break-word;
    line-height: 1.5;
    margin: 0;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    transition: all 0.2s ease;
}

.content p:hover {
    box-shadow: 0 2px 6px rgba(0,0,0,0.15);
}

.content .time {
    font-size: 11px;
    color: #999;
    margin-top: 4px;
    font-weight: 400;
}

.content .time.left {
    text-align: left;
}

.content .time.right {
    text-align: right;
}

.message.left .content p {
    background: linear-gradient(135deg, #f5f5f5, #e8e8e8);
    color: #333;
    border-radius: 18px 18px 18px 4px;
    border-left: 3px solid #4CAF50;
}

.message.right .content p {
    background: linear-gradient(135deg, #4CAF50, #45a049);
    color: #fff;
    border-radius: 18px 18px 4px 18px;
    box-shadow: 0 2px 8px rgba(76, 175, 80, 0.3);
}

/* 连接状态指示器 */
.connection-status {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
    z-index: 1000;
    transition: all 0.3s ease;
}

.connection-status.connected {
    background-color: #4CAF50;
    color: white;
    box-shadow: 0 2px 8px rgba(76, 175, 80, 0.3);
}

.connection-status.disconnected {
    background-color: #f44336;
    color: white;
    box-shadow: 0 2px 8px rgba(244, 67, 54, 0.3);
}

.connection-status.connecting {
    background-color: #ff9800;
    color: white;
    box-shadow: 0 2px 8px rgba(255, 152, 0, 0.3);
}

/* 输入区域样式优化 */
.input-section {
    background: #fff;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
    border: 1px solid #e0e0e0;
}

.input-group {
    margin-bottom: 15px;
}

.input-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: #555;
}

.input-group input, .input-group textarea {
    width: 100%;
    padding: 10px 12px;
    border: 2px solid #e0e0e0;
    border-radius: 6px;
    font-size: 14px;
    transition: border-color 0.3s ease;
}

.input-group input:focus, .input-group textarea:focus {
    outline: none;
    border-color: #4CAF50;
    box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.1);
}

/* 按钮样式优化 */
.btn-chat {
    background: linear-gradient(135deg, #4CAF50, #45a049);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-right: 10px;
}

.btn-chat:hover {
    background: linear-gradient(135deg, #45a049, #3d8b40);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
}

.btn-chat:active {
    transform: translateY(0);
}

.btn-chat.secondary {
    background: linear-gradient(135deg, #2196F3, #1976D2);
}

.btn-chat.secondary:hover {
    background: linear-gradient(135deg, #1976D2, #1565C0);
    box-shadow: 0 4px 12px rgba(33, 150, 243, 0.3);
}