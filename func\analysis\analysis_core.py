import requests
import json
from func.tools.singleton_mode import singleton
from func.log.default_log import DefaultLog
from func.gobal.data import SecretData

@singleton
class AnalysisCore:
    def __init__(self):
        """初始化分析核心"""
        self.log = DefaultLog().getLogger()
        self.server_url = "http://localhost:8000"  # 假设的服务器URL
        self.headers = {
            "Content-Type": "application/json"
        }
        self.secretData = SecretData()
        # self.graphdbData = GraphdbData()
        # self.analysisData = AnalysisData()

    async def intent_analysis(self, text: str):
        """
        意图分析
        Args:
            text: 输入文本
        Returns:
            分析结果
        """
        try:
            data = {
                "text": text,
                "key": self.secretData.key_verify()
            }
            
            response = requests.post(
                f"{self.server_url}/intent_classify",
                headers=self.headers,
                json=data,
                timeout=30
            )
            
            if response.status == 200:
                return response.json()
            return {"status": "error", "message": "意图分析请求失败"}
            
        except Exception as e:
            print(e)
            self.log.exception("intent_analysis异常")
            return {"status": "error", "message": str(e)}

    async def relation_query(self, text: str, classify: str = "聊天"):
        """
        关系查询
        Args:
            text: 输入文本
            classify: 分类，默认为"聊天"
        Returns:
            查询结果
        """
        try:
            data = {
                "text": text,
                "classify": classify,
                "key": self.secretData.key_verify()
            }
            
            response = requests.post(
                f"{self.server_url}/relation_query",
                headers=self.headers,
                json=data,
                timeout=30
            )
            
            if response.status == 200:
                return response.json()
            return {"status": "error", "message": "关系查询请求失败"}
            
        except Exception as e:
            print(e)
            self.log.exception("relation_query异常")
            return {"status": "error", "message": str(e)}