#coding=UTF-8
"""
测试关键词优化功能
"""
import sys
sys.path.append('.')

from func.sing.sing_core import Sing<PERSON>ore

def test_keyword_optimization():
    """测试关键词优化功能"""
    print("关键词优化测试:")
    print("=" * 50)
    
    try:
        sing_core = SingCore()
        
        test_cases = [
            '《稻香》',
            '"稻香"',
            '稻香的歌',
            '周杰伦：稻香',
            '周杰伦，稻香',
            '稻香 by 周杰伦',
            '稻香—周杰伦',
            '  稻香  ',
            '《周杰伦》—《稻香》',
        ]
        
        for i, original in enumerate(test_cases, 1):
            optimized = sing_core._optimize_search_keywords(original)
            print(f"[{i}] 原始: '{original}' -> 优化: '{optimized}'")
            
    except Exception as e:
        print(f"测试异常: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_keyword_optimization()
