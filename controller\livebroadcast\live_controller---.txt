J:\ai\�������\ai-yinmei\controller\livebroadcast\live_controller.c     live_controller.py  
post        live_controller.HttpRecharge.post   
get live_controller.HttpCmd.get     
live_controller.HttpSay.post    
live_controller.HttpEmote.post  
live_controller.HttpSing.get    
live_controller.HttpDraw.get    
live_controller.HttpScene.get   
live_controller.Msg.post        
live_controller.ChatReply.get   
live_controller.Chat.get        
live_controller.SongList.get    
live_controller Interpreter 
change detected - this module can only be loaded into one interpreter per process.  name    __loader__  loader      __file__    origin      __package__ parent      __path__        submodule_search_locations      Module 'live_controller' has already been imported. Re-initialisation is not supported. builtins        cython_runtime  __builtins__    __orig_bases__  init live_controller    %s() got multiple values for keyword argument '%U'      %.200s() keywords must be strings       %s() got an unexpected keyword argument '%U'    exactly %.200s() takes %.8s %zd positional argument%.1s (%zd given)     name '%U' is not defined        join() result is too long for a Python string    while calling a Python object  NULL result without error in PyObject_Call      cannot import name %S   __mro_entries__ must return a tuple             metaclass conflict: the metaclass of a derived class must be a (non-strict) subclass of the metaclasses of all its bases        _cython_3_0_11  Shared Cython type %.200s is not a type object  Shared Cython type %.200s has the wrong size, try recompiling   keywords must be strings        __name__ must be set to a string object __qualname__ must be set to a string object     function's dictionary may not be deleted        setting function's dictionary to a non-dict     __defaults__ must be set to a tuple object              changes to cyfunction.__defaults__ will not currently affect the values used in function calls  __kwdefaults__ must be set to a dict object     changes to cyfunction.__kwdefaults__ will not currently affect the values used in function calls        __annotations__ must be set to a dict object    func_doc        __doc__ func_name       __name__        __qualname__    func_dict       __dict__        func_globals    __globals__     func_closure    __closure__     func_code       __code__        func_defaults   __defaults__    __kwdefaults__  __annotations__ _is_coroutine   __module__      __reduce__      Bad call flags for CyFunction   <cyfunction %U at %p>   %.200s() takes no arguments (%zd given) %.200s() takes exactly one argument (%zd given) %.200s() takes no keyword arguments     unbound method %.200S() needs an argument       %.200s() needs an argument      _cython_3_0_11.cython_function_or_method        %s (%s:%d)      does not match          compile time Python version %d.%d of module '%.100s' %s runtime version %d.%d   
func.draw.draw_core 0   func.score.oper_score   ChatReply.get   "}) *   func.search.search_core ttsCore HttpCmd.get decode  info        HttpRecharge    actionOper      live_controller jsonstr api     LLmCore drawCore        cline_in_traceback      threading       changeScene     func.sing.sing_core     CmdCore scenename       �U�   singCore        func.tts.tts_core       HttpScene.get   __prepare__     emoteOper       memoryCore  msg 手动充值    ImageCore       func.memory.memory_core http_sing       openId："  cmdstr      http_songlist   func.gobal.data SongList.get    http_chatreply  __main__        request chatId  __qualname__    __module__      __mro_entries__ self    username        Chat.get        func.entrance.entrance_core     __dict__        @U�   _initializing   drawname        danceCore       jsonify HttpSay.post    ","status": "值为空","content": "    func.vtuber.action_oper .               J:\ai\吟美打包\ai-yinmei\controller\livebroadcast\live_controller.py        emote_ws    target      CallBack        HttpSing.get    _is_coroutine   super   __spec__        msg_deal        searchCore      SearchCore  post    start       llmCore all uuid4       __import__  query   log __doc__ ","content": "  DanceCore       tts_say_thread  songname        __set_name__    EntranceCore    status  执行指令："    uid func.log.default_log    DrawCore        SongList        TTsCore __name__        func.vtuber.emote_oper  DefaultLog      __init_subclass__       HttpSing    Chat        HttpCmd ?       HttpScene       func.image.image_core   get     HttpSay HttpEmote.post  data    entranceCore    BaseView    text        __test__        SingCore        VtuberData      Msg.post        CallBackForTest ChatReply   json        func.cmd.cmd_core   uface   utf-8       tts_say Msg funasr      HttpDraw.get    http_cmd        func.dance.dance_core   Thread  openId  __metaclass__   uuid    cmdCore getLogger       operScore   flask       drawcontent args        traceid HttpRecharge.post       http_draw       recharge_score  ({"traceid": "  "       jsonStr asyncio.coroutines      HttpDraw        emote_thread1   MemoryCore      HttpEmote       vtuberData  score       user_name       controller.baseview cmd 成功  EmoteOper       ","status": "   ActionOper      imageCore       func.llm.llm_core       OperScore