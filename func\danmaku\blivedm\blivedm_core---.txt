J:\ai\�������\tt\blivedm_core.c        blivedm_core.py __init__        blivedm_core.BlivedmCore.__init__       blivedm_start   blivedm_core.BlivedmCore.blivedm_start  blivedm_start2  blivedm_core.BlivedmCore.blivedm_start2 init_session    blivedm_core.BlivedmCore.init_session   close   blivedm_core.BlivedmCore.close  run_single_client2      blivedm_core.BlivedmCore.run_single_client2     run_single_client       blivedm_core.BlivedmCore.run_single_client      listen_blivedm_task     blivedm_core.BlivedmCore.listen_blivedm_task    blivedm_core.MyHandler2.__init__        __interact_word_callback        blivedm_core.MyHandler2.__interact_word_callback        command _on_heartbeat   blivedm_core.MyHandler2._on_heartbeat   blivedm_core.MyHandler.__init__ blivedm_core.MyHandler._on_heartbeat    _on_open_live_danmaku   blivedm_core.MyHandler._on_open_live_danmaku    _on_open_live_gift      blivedm_core.MyHandler._on_open_live_gift       _on_open_live_buy_guard blivedm_core.MyHandler._on_open_live_buy_guard  _on_open_live_super_chat        blivedm_core.MyHandler._on_open_live_super_chat _on_open_live_super_chat_delete blivedm_core.MyHandler._on_open_live_super_chat_delete  _on_open_live_like      blivedm_core.MyHandler._on_open_live_like       _on_room_enter  blivedm_core.MyHandler._on_room_enter   enter   blivedm_core.MyHandler.enter    say     blivedm_core.MyHandler.say      new_reg blivedm_core.MyHandler.new_reg  blivedm_core.__pyx_scope_struct__blivedm_start  blivedm_core.__pyx_scope_struct_1_blivedm_start2        blivedm_core.__pyx_scope_struct_2_run_single_client2    blivedm_core.__pyx_scope_struct_3_run_single_client     blivedm_core.__pyx_scope_struct_4_listen_blivedm_task   __pyx_scope_struct_1_blivedm_start2     __pyx_scope_struct_2_run_single_client2 __pyx_scope_struct_3_run_single_client  __pyx_scope_struct_4_listen_blivedm_task        __pyx_scope_struct__blivedm_start       Cannot overwrite C type %s      __all__ __dict__        from-import-* object has no __dict__ and no __all__     blivedm_core    Interpreter change detected - this module can only be loaded into one interpreter per process.  name    __loader__  loader      __file__    origin      __package__ parent      __path__        submodule_search_locations      Module 'blivedm_core' has already been imported. Re-initialisation is not supported.    builtins        cython_runtime  __builtins__    __orig_bases__  init blivedm_core       name '%U' is not defined        %s() got multiple values for keyword argument '%U'      %.200s() keywords must be strings       %s() got an unexpected keyword argument '%U'    exactly s       %.200s() takes %.8s %zd positional argument%.1s (%zd given)      while calling a Python object  NULL result without error in PyObject_Call      _cython_3_0_11  Shared Cython type %.200s is not a type object  Shared Cython type %.200s has the wrong size, try recompiling   raise: arg 3 must be a traceback or None        instance exception may not have a separate value                calling %R should have returned an instance of BaseException, not %R    raise: exception class must be a subclass of BaseException      '%.200s' object has no attribute '%U'   coroutine already executing     generator already executing     can't send non-None value to a just-started coroutine   can't send non-None value to a just-started generator   cannot reuse already awaited coroutine  coroutine ignored GeneratorExit generator ignored GeneratorExit throw   coroutine '%.50S' was never awaited     __name__ must be set to a string object __qualname__ must be set to a string object     _cython_coroutine_type  _cython_generator_type  _module Cython module failed to patch module with custom type   if _cython_generator_type is not None:
    try: Generator = _module.Generator
    except AttributeError: pass
    else: Generator.register(_cython_generator_type)
if _cython_coroutine_type is not None:
    try: Coroutine = _module.Coroutine
    except AttributeError: pass
    else: Coroutine.register(_cython_coroutine_type)
  collections.abc Cython module failed to register with collections.abc module    backports_abc   cannot instantiate type, use 'await coroutine' instead  cannot pickle '%.200s' object   send            send(arg) -> send 'arg' into coroutine,
return next yielded value or raise StopIteration.       throw(typ[,val[,tb]]) -> raise exception in coroutine,
return next yielded value or raise StopIteration.        close() -> raise GeneratorExit inside coroutine.        __reduce_ex__   __reduce__      _cython_3_0_11.coroutine_wrapper        A wrapper object implementing __await__ for coroutines. invalid input, expected coroutine       send(arg) -> send 'arg' into coroutine,
return next iterated value or raise StopIteration.      throw(typ[,val[,tb]]) -> raise exception in coroutine,
return next iterated value or raise StopIteration.       cr_running      cr_await        object being awaited, or None   cr_code __module__      __name__        name of the coroutine   __qualname__    qualified name of the coroutine cr_frame        Frame of the coroutine  _cython_3_0_11.coroutine        'async for' received an invalid object from __anext__: %.200s   __await__() returned non-iterator of type '%.200s'      __await__() returned a coroutine        object %.200s can't be used in 'await' expression       coroutine is being awaited already      generator raised StopIteration  Missing type object     Argument '%.200s' has incorrect type (expected %.200s, got %.200s)      join() result is too long for a Python string   strings are too large to concat base class '%.200s' is not a heap type  extension type '%.200s' has no __dict__ slot, but base type '%.200s' has: either add 'cdef dict __dict__' to the extension type or add '__slots__ = [...]' to the base type     cannot import name %S   .       No module named '%U'    keywords must be strings        function's dictionary may not be deleted        setting function's dictionary to a non-dict     __defaults__ must be set to a tuple object              changes to cyfunction.__defaults__ will not currently affect the values used in function calls  __kwdefaults__ must be set to a dict object     changes to cyfunction.__kwdefaults__ will not currently affect the values used in function calls        __annotations__ must be set to a dict object    func_doc        __doc__ func_name       func_dict       func_globals    __globals__     func_closure    __closure__     func_code       __code__        func_defaults   __defaults__    __kwdefaults__  __annotations__ _is_coroutine   Bad call flags for CyFunction   <cyfunction %U at %p>   %.200s() takes no arguments (%zd given) %.200s() takes exactly one argument (%zd given) %.200s() takes no keyword arguments     unbound method %.200S() needs an argument       %.200s() needs an argument      _cython_3_0_11.cython_function_or_method        __mro_entries__ must return a tuple     metaclass conflict: the metaclass of a derived class must be a (non-strict) subclass of the metaclasses of all its bases        %s (%s:%d)      does not match  compile time Python version %d.%d of module '%.100s' %s runtime version %d.%d   func.score.oper_score   superTools      BlivedmCore.blivedm_start2      open_models.SuperChatDeleteMessage      
        演示监听一个直播间
           warning start_counter   BlivedmCore.MyHandler.say   say open_models.RoomEnterMessage    ttsCore info    _on_open_live_danmaku   close   http    __await__        赠送 welcome_not_allow       BlivedmCore.listen_blivedm_task run_single_client2      cline_in_traceback      LLmData enable  paid    threading       blivedm.BLiveClient     func.tools.singleton_mode       blivedm_start2  ���   ] 心跳2       
        演示监听一个直播间
           message_ids     user_info       func.tts.tts_core       func.tools.super_tools  __prepare__     大小姐最爱你了   accumulate      BlivedmCore.MyHandler.new_reg   BlivedmCore.MyHandler._on_open_live_buy_guard   msg     blivedm.OpenLiveClient  asyncio CommonData       点赞 _MyHandler2__interact_word_callback     __interact_word_callback        func.danmaku.blivedm.models.open_live   total_coin      new_reg cachetools  level       func.gobal.data :粉丝[    enter       __dict__        __main__    client      cookie_jar      open_models.GuardBuyMessage     BLiveClient      购买 大航海等级=        Optional[aiohttp.ClientSession] __qualname__    __module__      赠送的       __mro_entries__ self    username        client2 WelcomeData     func.entrance.entrance_core     create_task dict        ���   web     _initializing   BlivedmCore.init_session    元 None    ’赠送的¥  [       Ai_Name task1   BlivedmCore.MyHandler2  models  strftime    target      ’赠送的    giftname        init_session    BlivedmCore.MyHandler._on_open_live_gift        aiohttp access_key_id   ttl     open_models.SuperChatMessage    run_single_client   math        元,她留言说"       open_live       BiliDanmakuData web_models  APP_ID      ClientSession   _is_coroutine   open_models.GiftMessage super   ACCESS_KEY_ID   __spec__        SimpleCookie    msg_deal        赠金¥    *   now start       handler 大小姐在这里给你跪下了       danmaku all     %Y-%m-%d %H:%M:%S       BlivedmCore.MyHandler.__init__  not_allow_userid    uinfo   append  uuid4       asyncio.tasks   MyHandler2      __import__  log open_models.LikeMessage welcomeData     BlivedmCore.MyHandler._on_heartbeat     __doc__ 非常谢谢‘ update_cookies  tts_say_thread  大航海等级 BlivedmCore.MyHandler._on_room_enter    gc      __set_name__    roomid  BlivedmCore.run_single_client   BlivedmCore.MyHandler._on_open_live_danmaku     _on_open_live_buy_guard BlivedmCore.MyHandler._on_open_live_super_chat  EntranceCore    BlivedmCore.MyHandler2._on_heartbeat            
            新用户注册，24小时刷新一次用户数据
            :param open_id:
            :param user_name:
            :param face:
            :return:
                BlivedmCore.MyHandler._on_open_live_like        oper_score  uid func.log.default_log    SuperTools      blivedm_core    B站    cookies message ROOM_OWNER_AUTH_CODE    room_owner_auth_code    TTsCore __name__        银瓜子       ] 心跳1       DefaultLog      __init_subclass__       llmData open_models.DanmakuMessage      BlivedmCore.MyHandler.enter "   ]       ’点赞,      ACCESS_KEY_SECRET       _on_open_live_super_chat    】 个 send        _CMD_CALLBACK_DICT  rmb stop_and_close  datetime        listen_blivedm_task price       __annotations__  （    bilibili.com    num floor       http.cookies    x       access_key_secret   data        guard_level     entranceCore    BlivedmCore.__init__    BlivedmCore.blivedm_start       _on_open_live_super_chat_delete text    __test__        B站弹幕任务已成功取消 .       isenabled   time1   copy        SESSDATA        ]进入了直播间 ?   ] 醒目留言 ¥       commonData      ] INTERACT_WORD: self_type= uface   join    cached  face        tts_say ]进入了直播间【  BlivedmCore     OpenLiveClient  coin_type   domain  func    app_id      J:\ai\吟美打包\tt\blivedm_core.py   uname   func.danmaku.blivedm.models.web maxsize ）     session typing  CancelledError  BlivedmCore.run_single_client2  blivedm Thread  BlivedmCore.MyHandler2.__interact_word_callback ,       __metaclass__   uuid    set_handler     open_id getLogger       _on_open_live_like      BlivedmCore.MyHandler2.__init__ 谢谢‘       operScore       WelcomeList ]   _on_room_enter  args    command gift_name       func.danmaku.blivedm    disable open_models     TTLCache        user_refresh    __init__        ] 删除醒目留言 message_ids=       user_id traceid BlivedmCore.MyHandler   金瓜子       asyncio.coroutines      web_models.HeartbeatMessage     MyHandler       blivedm_start   BlivedmCore.MyHandler._on_open_live_super_chat_delete   inspect new_user_thread user_name   ： biliDanmakuData , uname=        singleton   throw       BlivedmCore.close       ’购买 大航海等级       _on_open_live_gift      BaseHandler     gift_num        INTERACT_WORD   run     room_id _on_heartbeat   base    OperScore       , room_id=