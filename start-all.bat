@echo off
title 多命令启动器

start "MongoDB服务" cmd /k "H:\soft\mongodb-windows-x86_64-8.0.6\bin\mongod.exe --dbpath=H:\soft\mongodb-windows-x86_64-8.0.6\db --port 27017"
start "酷狗音乐API" cmd /k "cd /d I:\AIV\KuGouMusicApi-main && npm run dev"
start "GPT-SoVITS API" cmd /k "cd /d H:\baidudown\GPT-SoVITS-v2\GPT-SoVITS-v2-240821\ && api.bat"

@REM echo 打开obs
@REM start "" "H:\Program Files\obs-studio\bin\64bit\"

echo 所有命令已启动...
pause