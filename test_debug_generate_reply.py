#!/usr/bin/env python3
"""
测试 generate_reply 方法的调试信息
"""

import asyncio
import sys
import os

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.plugin_system.apis import generator_api
from src.chat.message_receive.chat_stream import ChatStream
from src.chat.message_receive.message import UserInfo, GroupInfo
from src.config.api_ada_configs import TaskConfig
from src.common.logger import get_logger

# 设置日志级别为 INFO 以显示调试信息
logger = get_logger("test_debug")

async def test_generate_reply_debug():
    """测试 generate_reply 方法的调试信息"""
    
    print("=" * 80)
    print("开始测试 generate_reply 方法的调试信息")
    print("=" * 80)
    
    # 创建测试用的 ChatStream
    user_info = UserInfo(
        user_id="test_user_123",
        user_name="测试用户",
        user_displayname="测试用户显示名",
        avatar_url="",
        data={}
    )
    
    group_info = GroupInfo(
        group_id="test_group_456",
        group_name="测试群组",
        data={}
    )
    
    chat_stream = ChatStream(
        stream_id="test_stream_789",
        platform="test_platform",
        user_info=user_info,
        group_info=group_info,
        data={}
    )
    
    # 创建测试用的模型配置
    model_config = TaskConfig(
        model_list=["test_model"],
        temperature=0.7,
        max_tokens=1000
    )
    
    model_set_with_weight = [(model_config, 1.0)]
    
    # 测试参数
    test_params = {
        "chat_stream": chat_stream,
        "chat_id": None,
        "action_data": {
            "reply_to": "用户A:你好，今天天气怎么样？",
            "extra_info": "这是一个关于天气的询问"
        },
        "reply_to": "",
        "extra_info": "",
        "available_actions": {},
        "enable_tool": False,
        "enable_splitter": True,
        "enable_chinese_typo": True,
        "return_prompt": True,
        "model_set_with_weight": model_set_with_weight,
        "request_type": "test_debug"
    }
    
    try:
        print("\n调用 generator_api.generate_reply...")
        print("-" * 60)
        
        # 调用 generate_reply 方法
        success, reply_set, prompt = await generator_api.generate_reply(**test_params)
        
        print("-" * 60)
        print("generate_reply 调用完成")
        print(f"返回结果:")
        print(f"  - success: {success}")
        print(f"  - reply_set 长度: {len(reply_set) if reply_set else 0}")
        if reply_set:
            for i, (msg_type, content) in enumerate(reply_set):
                print(f"    [{i}] 类型: {msg_type}, 内容: '{content[:50]}{'...' if len(content) > 50 else ''}'")
        print(f"  - prompt: {'存在' if prompt else '不存在'} ({'长度: ' + str(len(prompt)) if prompt else ''})")
        
    except Exception as e:
        print(f"测试过程中发生异常: {e}")
        import traceback
        traceback.print_exc()
    
    print("=" * 80)
    print("测试完成")
    print("=" * 80)

async def test_get_replyer_debug():
    """测试 get_replyer 方法的调试信息"""
    
    print("\n" + "=" * 80)
    print("开始测试 get_replyer 方法的调试信息")
    print("=" * 80)
    
    # 创建测试用的 ChatStream
    user_info = UserInfo(
        user_id="test_user_456",
        user_name="测试用户2",
        user_displayname="测试用户2显示名",
        avatar_url="",
        data={}
    )
    
    chat_stream = ChatStream(
        stream_id="test_stream_456",
        platform="test_platform",
        user_info=user_info,
        group_info=None,
        data={}
    )
    
    try:
        print("\n调用 generator_api.get_replyer...")
        print("-" * 60)
        
        # 第一次调用 - 应该创建新实例
        replyer1 = generator_api.get_replyer(
            chat_stream=chat_stream,
            chat_id=None,
            model_set_with_weight=None,
            request_type="test_debug_1"
        )
        
        print(f"第一次调用结果: {type(replyer1).__name__ if replyer1 else 'None'}")
        
        # 第二次调用 - 应该返回缓存实例
        replyer2 = generator_api.get_replyer(
            chat_stream=chat_stream,
            chat_id=None,
            model_set_with_weight=None,
            request_type="test_debug_2"
        )
        
        print(f"第二次调用结果: {type(replyer2).__name__ if replyer2 else 'None'}")
        print(f"两次调用是否返回同一实例: {replyer1 is replyer2}")
        
    except Exception as e:
        print(f"测试过程中发生异常: {e}")
        import traceback
        traceback.print_exc()
    
    print("=" * 80)
    print("get_replyer 测试完成")
    print("=" * 80)

async def main():
    """主测试函数"""
    print("开始调试信息测试")
    
    # 测试 get_replyer 方法
    await test_get_replyer_debug()
    
    # 测试 generate_reply 方法
    await test_generate_reply_debug()
    
    print("\n所有测试完成！")

if __name__ == "__main__":
    # 运行测试
    asyncio.run(main())
