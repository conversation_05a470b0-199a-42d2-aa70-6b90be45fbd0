"""
用户管理模块
负责用户的注册、更新、查询、搜索等功能
"""

import re
from datetime import datetime
from typing import Dict, List, Optional, Any
from pymongo.errors import DuplicateKeyError

from ..core.base_manager import BaseManager, CacheableMixin, ValidatorMixin


class UserManager(BaseManager, CacheableMixin, ValidatorMixin):
    """用户管理器"""

    def _initialize(self):
        """初始化用户管理器"""
        # 初始化缓存相关属性
        if not hasattr(self, '_cache_enabled'):
            self._cache_enabled = True
        if not hasattr(self, '_cache_ttl'):
            self._cache_ttl = 300
        self.log.info("用户管理器初始化完成")

    def create_user(self, openId: str, userName: str, face: str = "") -> Optional[Dict[str, Any]]:
        """
        创建新用户
        :param openId: 用户开放平台id
        :param userName: 用户名
        :param face: 用户头像
        :return: 用户信息
        """
        try:
            # 参数验证
            if not self._validate_openid(openId) or not self._validate_username(userName):
                return None

            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            user_data = {
                "openId": openId,
                "userName": userName,
                "userface": face or "",
                "score": 0,
                "createTime": current_time,
                "updateTime": current_time
            }

            # 插入用户数据
            result = self.db_manager.users_collection.insert_one(user_data)
            if result.inserted_id:
                self._log_operation("创建用户", True, openId=openId, userName=userName)
                # 移除 _id 字段
                user_data.pop('_id', None)

                # 清除相关缓存
                self._clear_user_cache(openId)

                return user_data
            else:
                raise Exception("插入用户数据失败")

        except DuplicateKeyError:
            self.log.warning(f"用户已存在: {openId}")
            return self.get_user(openId)
        except Exception as e:
            self._handle_error(e, "创建用户", openId=openId, userName=userName)
            return None

    def update_user(self, openId: str, userName: str, face: str = "") -> Optional[Dict[str, Any]]:
        """
        更新用户信息（如果不存在则创建）
        :param openId: 用户开放平台id
        :param userName: 用户名
        :param face: 用户头像
        :return: 用户信息
        """
        try:
            # 参数验证
            if not self._validate_openid(openId) or not self._validate_username(userName):
                return None

            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

            # 使用 upsert 操作，一次操作完成新增或更新
            update_data = {
                "$set": {
                    "userName": userName,
                    "userface": face or "",
                    "updateTime": current_time
                },
                "$setOnInsert": {
                    "score": 0,
                    "createTime": current_time
                }
            }

            result = self.db_manager.users_collection.update_one(
                {"openId": openId},
                update_data,
                upsert=True
            )

            # 获取更新后的用户信息
            userinfo = self.get_user(openId, use_cache=False)

            if result.upserted_id:
                self._log_operation("新用户注册", True, openId=openId, userName=userName)
            else:
                self._log_operation("用户信息更新", True, openId=openId, userName=userName)

            # 清除相关缓存
            self._clear_user_cache(openId)

            return userinfo

        except Exception as e:
            self._handle_error(e, "更新用户信息", openId=openId, userName=userName)
            return None

    def get_user(self, openId: str, use_cache: bool = True) -> Optional[Dict[str, Any]]:
        """
        获取用户信息
        :param openId: 用户开放平台id
        :param use_cache: 是否使用缓存
        :return: 用户信息
        """
        if not self._validate_openid(openId):
            return None

        try:
            # 尝试从缓存获取
            if use_cache:
                cache_key = self._get_cache_key("user", openId)
                cached_data = self._get_from_cache(cache_key)
                if cached_data:
                    return cached_data

            # 从数据库查询
            result = self.db_manager.users_collection.find_one(
                {"openId": openId},
                {"_id": 0, "openId": 1, "userName": 1, "userface": 1, "score": 1,
                 "createTime": 1, "updateTime": 1}
            )

            # 缓存结果
            if result and use_cache:
                cache_key = self._get_cache_key("user", openId)
                self._set_to_cache(cache_key, result)

            return result

        except Exception as e:
            self._handle_error(e, "获取用户信息", openId=openId)
            return None

    def search_users(self, keyword: str = "", page_number: int = 1, page_size: int = 20) -> Dict[str, Any]:
        """
        搜索用户
        :param keyword: 搜索关键词（用户名或openId）
        :param page_number: 页码
        :param page_size: 每页大小
        :return: 搜索结果
        """
        try:
            # 验证分页参数
            pagination = self._validate_pagination(page_number, page_size)

            # 构建查询条件
            query_filter = {}
            if keyword and keyword.strip():
                escaped_keyword = re.escape(keyword.strip())
                query_filter = {
                    "$or": [
                        {"userName": {"$regex": escaped_keyword, "$options": "i"}},
                        {"openId": {"$regex": escaped_keyword, "$options": "i"}}
                    ]
                }

            # 查询字段
            projection = {
                "_id": 0,
                "openId": 1,
                "userName": 1,
                "userface": 1,
                "score": 1,
                "createTime": 1,
                "updateTime": 1
            }

            # 执行查询
            cursor = self.db_manager.users_collection.find(query_filter, projection)\
                .sort("score", -1)\
                .skip(pagination['skip_count'])\
                .limit(pagination['page_size'])

            total_documents = self.db_manager.users_collection.count_documents(query_filter)
            total_pages = (total_documents + pagination['page_size'] - 1) // pagination['page_size']

            return {
                "data": list(cursor),
                "total": total_documents,
                "current_page": pagination['page_number'],
                "total_pages": total_pages,
                "page_size": pagination['page_size'],
                "has_next": pagination['page_number'] < total_pages,
                "has_prev": pagination['page_number'] > 1
            }

        except Exception as e:
            self._handle_error(e, "搜索用户", keyword=keyword)
            return {
                "data": [],
                "total": 0,
                "current_page": page_number,
                "total_pages": 0,
                "page_size": page_size,
                "has_next": False,
                "has_prev": False
            }

    def delete_user(self, openId: str) -> bool:
        """
        删除用户及其相关记录
        :param openId: 用户ID
        :return: 是否成功
        """
        try:
            if not self._validate_openid(openId):
                return False

            # 删除用户积分记录
            self.db_manager.records_collection.delete_many({"openId": openId})

            # 删除用户信息
            result = self.db_manager.users_collection.delete_one({"openId": openId})

            if result.deleted_count > 0:
                self._log_operation("删除用户", True, openId=openId)
                # 清除相关缓存
                self._clear_user_cache(openId)
                return True
            else:
                self.log.warning(f"用户不存在或删除失败: {openId}")
                return False

        except Exception as e:
            self._handle_error(e, "删除用户", openId=openId)
            return False

    def get_user_count(self) -> int:
        """获取用户总数"""
        try:
            return self.db_manager.users_collection.count_documents({})
        except Exception as e:
            self._handle_error(e, "获取用户总数")
            return 0

    def get_users_by_score_range(self, min_score: int = 0, max_score: int = None,
                                limit: int = 100) -> List[Dict[str, Any]]:
        """
        根据积分范围获取用户
        :param min_score: 最小积分
        :param max_score: 最大积分
        :param limit: 返回数量限制
        :return: 用户列表
        """
        try:
            query_filter = {"score": {"$gte": min_score}}
            if max_score is not None:
                query_filter["score"]["$lte"] = max_score

            projection = {
                "_id": 0,
                "openId": 1,
                "userName": 1,
                "userface": 1,
                "score": 1,
                "updateTime": 1
            }

            cursor = self.db_manager.users_collection.find(query_filter, projection)\
                .sort("score", -1)\
                .limit(limit)

            return list(cursor)

        except Exception as e:
            self._handle_error(e, "根据积分范围获取用户", min_score=min_score, max_score=max_score)
            return []

    def batch_update_users(self, updates: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        批量更新用户信息
        :param updates: 更新列表
        :return: 更新结果
        """
        try:
            success_count = 0
            failed_count = 0
            failed_users = []

            for update_item in updates:
                try:
                    openId = update_item.get("openId")
                    if not openId:
                        failed_count += 1
                        continue

                    update_data = {}
                    if "userName" in update_item:
                        update_data["userName"] = update_item["userName"]
                    if "userface" in update_item:
                        update_data["userface"] = update_item["userface"]

                    if update_data:
                        update_data["updateTime"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

                        result = self.db_manager.users_collection.update_one(
                            {"openId": openId},
                            {"$set": update_data}
                        )

                        if result.matched_count > 0:
                            success_count += 1
                            # 清除缓存
                            self._clear_user_cache(openId)
                        else:
                            failed_count += 1
                            failed_users.append(openId)

                except Exception as item_error:
                    self.log.error(f"批量更新单项失败 {openId}: {str(item_error)}")
                    failed_count += 1
                    failed_users.append(openId)

            self._log_operation("批量更新用户", success_count > 0,
                              success=success_count, failed=failed_count)

            return {
                "success": success_count > 0,
                "success_count": success_count,
                "failed_count": failed_count,
                "failed_users": failed_users,
                "total_processed": len(updates)
            }

        except Exception as e:
            self._handle_error(e, "批量更新用户")
            return {
                "success": False,
                "success_count": 0,
                "failed_count": len(updates),
                "failed_users": [item.get("openId", "") for item in updates],
                "total_processed": len(updates)
            }

    def _clear_user_cache(self, openId: str) -> None:
        """清除用户相关缓存"""
        try:
            # 清除用户信息缓存
            cache_key = self._get_cache_key("user", openId)
            self._clear_cache(cache_key)

            # 清除用户积分缓存
            score_cache_key = self._get_cache_key("user_score", openId)
            self._clear_cache(score_cache_key)

        except Exception as e:
            self.log.warning(f"清除用户缓存失败: {e}")

    # 兼容性方法，保持与原有API一致
    def user_refresh(self, openId: str, userName: str, face: str = "") -> Optional[Dict[str, Any]]:
        """用户信息刷新（兼容性方法）"""
        return self.update_user(openId, userName, face)

    def get_reg_userinfo(self, openId: str) -> Optional[Dict[str, Any]]:
        """获取用户信息（兼容性方法）"""
        return self.get_user(openId)

    def find_userinfo(self, openId: str) -> Optional[Dict[str, Any]]:
        """查找用户信息（兼容性方法）"""
        return self.get_user(openId)

    def new_user_insert(self, openId: str, userName: str, face: str) -> Dict[str, Any]:
        """新用户注册（兼容性方法）"""
        result = self.create_user(openId, userName, face)
        if result is None:
            raise Exception("创建用户失败")
        return result