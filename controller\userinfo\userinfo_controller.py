from flask import jsonify, request
from controller.baseview import BaseView
from func.user.user_db import UserDB
from func.llm.chat_db import ChatDB
from func.score.score_db import ScoreDB
from func.log.default_log import DefaultLog
from typing import Dict, Any, Optional

import json
import time
from bson import ObjectId

class UserList(BaseView):
    def __init__(self):
        self.userDB = UserDB()
        
    def get(self):
        page_number = request.args.get('pgnum', 1, type=int)
        page_size = request.args.get('pgsize', 10, type=int)
        username = request.args.get('username', '')
        print("UserList-----------username------", username)
        data = self.userDB.find_user_list_page(username, page_number, page_size)
        
        json_data = json.dumps(data["data"], default=object_id_converter)
        print(json_data)

        return jsonify({
            'status': 'success',
            'data': json_data,
            "total_documents": data['total_documents'],
            "total_pages": data['total_pages'],
            "current_page": data['current_page']
        })

class ChatList(BaseView):
    def __init__(self):
        self.chatDB = ChatDB()
        
    def get(self):
        page_number = request.args.get('pgnum', 1, type=int)
        page_size = request.args.get('pgsize', 10, type=int)
        username = request.args.get('username', '')
        print("ChatList--------username----------", username)
        data = self.chatDB.find_chat_list_page(
            username=username,
            page_number=page_number,
            page_size=page_size
        )

        json_data = json.dumps(data["data"], default=object_id_converter)
        print(json_data)

        return jsonify({
            'status': 'success',
            'data': json_data,
            "total_documents": data['total_documents'],
            "total_pages": data['total_pages'],
            "current_page": data['current_page'],
            "page_size": data['page_size']
        })

# 自定义序列化函数
def object_id_converter(o):
    if isinstance(o, ObjectId):
        return str(o)
    raise TypeError(f"Object of type {o.__class__.__name__} is not JSON serializable")

class ScoreRecord(BaseView):
    """积分记录查询控制器"""
    
    def __init__(self):
        self.scoreDB = ScoreDB()
        self.log = DefaultLog().getLogger()
        
    def get(self) -> Dict[str, Any]:
        """
        获取积分操作记录
        :return: 分页的积分记录数据
        """
        try:
            # 参数获取和验证
            page_number = self._get_valid_page_number()
            page_size = self._get_valid_page_size()
            username = self._get_username_filter()
            oper = self._get_oper_filter()
            
            self.log.info(f"查询积分记录: username={username}, oper={oper}, page={page_number}, size={page_size}")
            
            # 构建查询条件
            query_params = self._build_query_params(username, oper)
            
            # 查询数据
            data = self.scoreDB.find_score_record_page(
                username=query_params.get('username', ''),
                page_number=page_number, 
                page_size=page_size
            )
            
            # 如果需要按操作类型过滤，在这里进行二次过滤
            if oper:
                filtered_data = [item for item in data["data"] if oper.lower() in item.get("oper", "").lower()]
                data["data"] = filtered_data
                data["total"] = len(filtered_data)
                data["total_pages"] = (data["total"] + page_size - 1) // page_size
            
            # 序列化数据
            json_data = json.dumps(data["data"], default=object_id_converter)
            
            # 返回标准化响应
            return self._build_success_response(data, json_data)
            
        except Exception as e:
            self.log.error(f"查询积分记录异常: {str(e)}")
            return self._build_error_response(str(e))
    
    def _get_valid_page_number(self) -> int:
        """获取有效的页码"""
        page_number = request.args.get('pgnum', 1, type=int)
        return max(1, page_number)  # 确保页码至少为1
    
    def _get_valid_page_size(self) -> int:
        """获取有效的页面大小"""
        page_size = request.args.get('pgsize', 10, type=int)
        return max(1, min(100, page_size))  # 限制在1-100之间
    
    def _get_username_filter(self) -> str:
        """获取用户名过滤条件"""
        username = request.args.get('username', '').strip()
        return username[:50] if username else ''  # 限制长度
    
    def _get_oper_filter(self) -> str:
        """获取操作类型过滤条件"""
        oper = request.args.get('oper', '').strip()
        return oper[:20] if oper else ''  # 限制长度
    
    def _build_query_params(self, username: str, oper: str) -> Dict[str, str]:
        """构建查询参数"""
        params = {}
        if username:
            params['username'] = username
        if oper:
            params['oper'] = oper
        return params
    
    def _build_success_response(self, data: Dict[str, Any], json_data: str) -> Dict[str, Any]:
        """构建成功响应"""
        return jsonify({
            'status': 'success',
            'message': '查询成功',
            'data': json_data,
            'pagination': {
                'total': data.get('total', 0),
                'current_page': data.get('current_page', 1),
                'total_pages': data.get('total_pages', 0),
                'page_size': data.get('page_size', 10)
            },
            'timestamp': int(time.time()) if 'time' in globals() else None
        })
    
    def _build_error_response(self, error_msg: str) -> Dict[str, Any]:
        """构建错误响应"""
        return jsonify({
            'status': 'error',
            'message': f'查询积分记录失败: {error_msg}',
            'data': [],
            'pagination': {
                'total': 0,
                'current_page': 1,
                'total_pages': 0,
                'page_size': 10
            }
        }), 500

class ScoreRank(BaseView):
    """积分排行榜控制器"""
    
    def __init__(self):
        self.scoreDB = ScoreDB()
        self.log = DefaultLog().getLogger()
    
    def get(self) -> Dict[str, Any]:
        """
        获取积分排行榜
        :return: 排行榜数据
        """
        try:
            limit = request.args.get('limit', 100, type=int)
            limit = max(1, min(1000, limit))  # 限制在1-1000之间
            
            self.log.info(f"查询积分排行榜: limit={limit}")
            
            # 获取排行榜数据
            rank_data = self.scoreDB.find_score_rank(limit)
            
            # 处理数据格式
            formatted_data = []
            for index, user in enumerate(rank_data, 1):
                formatted_data.append({
                    'rank': index,
                    'openId': user.get('openId'),
                    'userName': user.get('userName'),
                    'score': user.get('score', 0),
                    'userface': user.get('userface', '')
                })
            
            return jsonify({
                'status': 'success',
                'message': '查询成功',
                'data': formatted_data,
                'total': len(formatted_data),
                'limit': limit
            })
            
        except Exception as e:
            self.log.error(f"查询积分排行榜异常: {str(e)}")
            return jsonify({
                'status': 'error',
                'message': f'查询排行榜失败: {str(e)}',
                'data': [],
                'total': 0
            }), 500

class ScoreStats(BaseView):
    """积分统计控制器"""
    
    def __init__(self):
        self.scoreDB = ScoreDB()
        self.log = DefaultLog().getLogger()
    
    def get(self) -> Dict[str, Any]:
        """
        获取积分统计信息
        :return: 统计数据
        """
        try:
            self.log.info("查询积分统计信息")
            
            # 获取基础统计
            user_count = self.scoreDB.get_user_count()
            total_score = self.scoreDB.get_total_score()
            avg_score = round(total_score / user_count, 2) if user_count > 0 else 0
            
            # 获取排行榜前三
            top_users = self.scoreDB.find_score_rank(3)
            
            stats = {
                'user_count': user_count,
                'total_score': total_score,
                'average_score': avg_score,
                'top_users': [
                    {
                        'rank': index + 1,
                        'userName': user.get('userName'),
                        'score': user.get('score', 0)
                    }
                    for index, user in enumerate(top_users)
                ]
            }
            
            return jsonify({
                'status': 'success',
                'message': '查询成功',
                'data': stats
            })
            
        except Exception as e:
            self.log.error(f"查询积分统计异常: {str(e)}")
            return jsonify({
                'status': 'error',
                'message': f'查询统计失败: {str(e)}',
                'data': {}
            }), 500