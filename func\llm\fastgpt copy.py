import requests
import json
from func.tools.singleton_mode import singleton
from func.config.default_config import defaultConfig
from func.gobal.data import CommonData
from func.log.default_log import DefaultLog

@singleton
class FastGpt:
    def __init__(self):
        self.config0 = defaultConfig()
        self.log = DefaultLog().getLogger()
        self.commonData = CommonData()
        
        self.config = self.config0.get_config()
        # API配置
        self.fastgpt_url = self.config["llm"]["fastgpt"]["fastgpt_url"]
        self.fastgpt_authorization = self.config["llm"]["fastgpt"]["fastgpt_authorization"]
        
        self.headers = {
            "Authorization": self.fastgpt_authorization,
            "Content-Type": "application/json"
        }

    async def chat(self, content, chat_version="", history=None, stream=False):
        """
        与FastGPT进行对话
        :param content: 对话内容
        :param chat_version: 对话版本
        :param history: 历史记录
        :param stream: 是否流式响应
        :return: 响应内容
        """
        try:
            if history is None:
                history = []
                
            data = {
                "chatId": "",
                "stream": stream,
                "messages": history + [{"role": "user", "content": content}],
                "variables": {}
            }

            response = requests.post(
                self.fastgpt_url,
                headers=self.headers,
                json=data,
                timeout=30
            )
            
            if response.status_code == 200:
                return response.json()
            else:
                self.log.error(f"【FastGPT】信息回复异常: {response.text}")
                return "我听不懂你说什么"
                
        except Exception as e:
            print(e)
            self.log.error(f"【FastGPT】异常: {str(e)}")
            return "我听不懂你说什么"

    def get_skill(self, skill):
        """
        获取技能描述
        :param skill: 技能名称
        :return: 技能描述
        """
        skill_map = {
            "chat": "用户和你聊天。请保证每次回复用户的内容都不同。",
            "唱歌": "用户叫你唱歌，歌曲名:请用《》标注，给用户列出好听的歌曲名称吧。如果用户有指定歌曲名称，请不要再列出其他歌曲名称。",
            "跳舞": "用户叫你跳舞，舞蹈名称:请用《》标注。",
            "画画": "用户叫你画画，图画标题请用《》标注，图画详细描述请用括号()一整段框起来，给用户详细描述画画的内容吧。"
        }
        
        return skill_map.get(skill, "请你根据问题的内容，使用以下词汇来进行回复，把以上的词汇结合你的想象力说成一句有趣的话，不过使用的词汇需要符合说话逻辑。")