J:\ai\�������\ai-yinmei\func\database\mongodb.c        mongodb.py      __init__        mongodb.Mongodb.__init__    get_db      mongodb.Mongodb.get_db  mongodb         Interpreter change detected - this module can only be loaded into one interpreter per process.  name    __loader__  loader      __file__    origin      __package__ parent      __path__        submodule_search_locations      Module 'mongodb' has already been imported. Re-initialisation is not supported. builtins        cython_runtime  __builtins__    init mongodb    %s() got multiple values for keyword argument '%U'      %.200s() keywords must be strings       %s() got an unexpected keyword argument '%U'    exactly %.200s() takes %.8s %zd positional argument%.1s (%zd given)     name '%U' is not defined        join() result is too long for a Python string    while calling a Python object  NULL result without error in PyObject_Call      cannot fit '%.200s' into an index-sized integer '%.200s' object is not subscriptable    cannot import name %S   _cython_3_0_11  Shared Cython type %.200s is not a type object  Shared Cython type %.200s has the wrong size, try recompiling   keywords must be strings        __name__ must be set to a string object __qualname__ must be set to a string object     function's dictionary may not be deleted        setting function's dictionary to a non-dict     __defaults__ must be set to a tuple object      changes to cyfunction.__defaults__ will not currently affect the values used in function calls  __kwdefaults__ must be set to a dict object     changes to cyfunction.__kwdefaults__ will not currently affect the values used in function calls        __annotations__ must be set to a dict object    func_doc        __doc__ func_name       __name__        __qualname__    func_dict       __dict__        func_globals    __globals__     func_closure    __closure__     func_code       __code__        func_defaults   __defaults__    __kwdefaults__  __annotations__ _is_coroutine   __module__      __reduce__      Bad call flags for CyFunction   <cyfunction %U at %p>   %.200s() takes no arguments (%zd given) %.200s() takes exactly one argument (%zd given) %.200s() takes no keyword arguments     unbound method %.200S() needs an argument       %.200s() needs an argument      _cython_3_0_11.cython_function_or_method        %s (%s:%d)      does not match          compile time Python version %d.%d of module '%.100s' %s runtime version %d.%d   *       J:\ai\吟美打包\ai-yinmei\func\database\mongodb.py   cline_in_traceback      func.tools.singleton_mode       @� �   db      __prepare__     Mongodb.get_db  get_db  @       Mongodb.__init__        func.gobal.data __main__        __qualname__    __module__  self        username    conn        __dict__        � �   _initializing   url switch      _is_coroutine   super   __spec__        Mongodb __import__  log __doc__ __class_getitem__       __set_name__    status  .       func.log.default_log    __name__        DefaultLog      __init_subclass__       MongodbData     MongoClient     mongodb://      __test__        func.tools.decorator_mode       __metaclass__   getLogger       class_switch    mongodbData     __init__    :   ?       asyncio.coroutines      password        singleton   /   mongodb dbname  pymongo