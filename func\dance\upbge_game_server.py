# UPBGE游戏端WebSocket服务器
# 这个文件是游戏端的示例代码，需要在UPBGE中运行

import asyncio
import websockets
import json
import os
import glob
from pathlib import Path

class GameWebSocketServer:
    def __init__(self):
        self.vmd_path = "H:/人工智能/ai/跳舞视频/vmd"  # VMD文件路径
        self.current_dance = None
        self.clients = set()
        
    def get_vmd_files(self):
        """获取所有VMD文件"""
        vmd_files = {}
        if os.path.exists(self.vmd_path):
            for file_path in glob.glob(os.path.join(self.vmd_path, "*.vmd")):
                filename = os.path.basename(file_path)
                dance_name = os.path.splitext(filename)[0]
                vmd_files[dance_name.lower()] = file_path
        return vmd_files
        
    def find_matching_vmd(self, dance_name):
        """根据舞蹈名称查找匹配的VMD文件"""
        vmd_files = self.get_vmd_files()
        dance_name_lower = dance_name.lower()
        
        # 精确匹配
        if dance_name_lower in vmd_files:
            return vmd_files[dance_name_lower]
            
        # 模糊匹配
        for name, path in vmd_files.items():
            if dance_name_lower in name or name in dance_name_lower:
                return path
                
        # 随机选择一个VMD文件
        if dance_name_lower == "随机" and vmd_files:
            import random
            return random.choice(list(vmd_files.values()))
            
        return None
        
    def load_vmd_to_mmd(self, vmd_file_path):
        """加载VMD文件到MMD模型（这里需要你实现具体的UPBGE MMD加载逻辑）"""
        print(f"Loading VMD file: {vmd_file_path}")
        
        # TODO: 在这里实现具体的UPBGE MMD模型控制逻辑
        # 例如：
        # 1. 找到场景中的MMD模型对象
        # 2. 加载VMD动作文件
        # 3. 播放动作
        
        # 伪代码示例：
        # import bge
        # scene = bge.logic.getCurrentScene()
        # mmd_object = scene.objects.get("MMD_Model")
        # if mmd_object:
        #     # 加载VMD动作
        #     mmd_object.load_vmd(vmd_file_path)
        #     # 播放动作
        #     mmd_object.play_animation()
        
        return True
        
    async def handle_client(self, websocket, path):
        """处理客户端连接"""
        self.clients.add(websocket)
        print(f"新客户端连接: {path}")
        
        try:
            async for message in websocket:
                data = json.loads(message)
                await self.handle_message(websocket, data)
        except websockets.exceptions.ConnectionClosed:
            print("客户端连接已断开")
        finally:
            self.clients.remove(websocket)
            
    async def handle_message(self, websocket, data):
        """处理接收到的消息"""
        print(f"收到消息: {data}")
        
        if data.get("type") == "dance":
            await self.handle_dance_command(websocket, data)
        else:
            await self.send_error(websocket, "未知的消息类型")
            
    async def handle_dance_command(self, websocket, data):
        """处理跳舞命令"""
        dance_name = data.get("dance_name", "")
        user_name = data.get("user_name", "")
        
        print(f"处理跳舞命令 - 舞蹈: {dance_name}, 用户: {user_name}")
        
        # 查找匹配的VMD文件
        vmd_file = self.find_matching_vmd(dance_name)
        
        if vmd_file:
            # 加载VMD到MMD模型
            success = self.load_vmd_to_mmd(vmd_file)
            
            if success:
                self.current_dance = {
                    "dance_name": dance_name,
                    "vmd_file": vmd_file,
                    "user_name": user_name
                }
                
                response = {
                    "type": "dance_response",
                    "status": "success",
                    "message": f"开始播放舞蹈: {dance_name}",
                    "vmd_file": vmd_file,
                    "dance_name": dance_name
                }
            else:
                response = {
                    "type": "dance_response", 
                    "status": "error",
                    "message": f"加载VMD文件失败: {vmd_file}"
                }
        else:
            response = {
                "type": "dance_response",
                "status": "error", 
                "message": f"未找到匹配的舞蹈: {dance_name}"
            }
            
        await websocket.send(json.dumps(response, ensure_ascii=False))
        
    async def send_error(self, websocket, message):
        """发送错误消息"""
        response = {
            "type": "error",
            "message": message
        }
        await websocket.send(json.dumps(response, ensure_ascii=False))
        
    async def broadcast_to_clients(self, message):
        """广播消息给所有客户端"""
        if self.clients:
            await asyncio.gather(
                *[client.send(json.dumps(message, ensure_ascii=False)) for client in self.clients],
                return_exceptions=True
            )
            
    def start_server(self, host="localhost", port=8765):
        """启动WebSocket服务器"""
        print(f"启动游戏WebSocket服务器 {host}:{port}")
        print(f"VMD文件路径: {self.vmd_path}")
        
        return websockets.serve(self.handle_client, host, port)

# UPBGE游戏脚本示例
def upbge_game_script():
    """
    这是在UPBGE游戏引擎中运行的脚本示例
    需要将此函数的内容添加到你的UPBGE游戏脚本中
    """
    import bge
    from bge import logic
    
    # 获取游戏对象和场景
    scene = logic.getCurrentScene()
    
    # 初始化WebSocket服务器（只初始化一次）
    if not hasattr(logic, 'ws_server'):
        logic.ws_server = GameWebSocketServer()
        
        # 启动服务器
        import threading
        def run_server():
            import asyncio
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            start_server = logic.ws_server.start_server()
            loop.run_until_complete(start_server)
            loop.run_forever()
            
        server_thread = threading.Thread(target=run_server, daemon=True)
        server_thread.start()
        print("UPBGE WebSocket服务器已启动")

if __name__ == "__main__":
    # 独立运行时的测试代码
    server = GameWebSocketServer()
    
    # 启动服务器
    start_server = server.start_server()
    
    loop = asyncio.get_event_loop()
    loop.run_until_complete(start_server)
    print("WebSocket服务器已启动，等待连接...")
    loop.run_forever() 