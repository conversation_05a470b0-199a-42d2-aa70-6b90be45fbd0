"""
基础管理器类
提供所有模块的通用功能和接口
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, Optional
from func.log.default_log import DefaultLog


class BaseManager(ABC):
    """基础管理器抽象类"""

    def __init__(self, database_manager=None):
        """
        初始化基础管理器
        :param database_manager: 数据库管理器实例
        """
        self.db_manager = database_manager
        self.log = DefaultLog().getLogger()
        self._initialize()

    @abstractmethod
    def _initialize(self):
        """子类初始化方法，由子类实现"""
        pass

    def _validate_params(self, **kwargs) -> Dict[str, Any]:
        """
        通用参数验证方法
        :param kwargs: 待验证的参数
        :return: 验证结果
        """
        return {
            'valid': True,
            'errors': []
        }

    def _handle_error(self, error: Exception, operation: str, **context) -> None:
        """
        统一错误处理
        :param error: 异常对象
        :param operation: 操作名称
        :param context: 上下文信息
        """
        error_msg = f"{operation}失败: {str(error)}"
        if context:
            error_msg += f" - 上下文: {context}"
        self.log.error(error_msg)

    def _log_operation(self, operation: str, success: bool, **details) -> None:
        """
        记录操作日志
        :param operation: 操作名称
        :param success: 是否成功
        :param details: 详细信息
        """
        level = "info" if success else "warning"
        status = "成功" if success else "失败"
        msg = f"{operation}{status}"
        if details:
            msg += f" - {details}"

        if level == "info":
            self.log.info(msg)
        else:
            self.log.warning(msg)


class CacheableMixin:
    """缓存功能混入类"""

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        if not hasattr(self, '_cache_enabled'):
            self._cache_enabled = True
        if not hasattr(self, '_cache_ttl'):
            self._cache_ttl = 300  # 默认5分钟缓存

    def _get_cache_key(self, prefix: str, *args) -> str:
        """
        生成缓存键
        :param prefix: 前缀
        :param args: 参数
        :return: 缓存键
        """
        key_parts = [prefix] + [str(arg) for arg in args]
        return ":".join(key_parts)

    def _get_from_cache(self, key: str) -> Optional[Any]:
        """
        从缓存获取数据
        :param key: 缓存键
        :return: 缓存数据
        """
        if not self._cache_enabled or not hasattr(self.db_manager, 'cache_manager'):
            return None

        try:
            return self.db_manager.cache_manager.get(key)
        except Exception as e:
            self.log.warning(f"缓存获取失败: {e}")
            return None

    def _set_to_cache(self, key: str, value: Any, ttl: Optional[int] = None) -> None:
        """
        设置缓存数据
        :param key: 缓存键
        :param value: 缓存值
        :param ttl: 过期时间
        """
        if not self._cache_enabled or not hasattr(self.db_manager, 'cache_manager'):
            return

        try:
            cache_ttl = ttl or self._cache_ttl
            self.db_manager.cache_manager.set(key, value, cache_ttl)
        except Exception as e:
            self.log.warning(f"缓存设置失败: {e}")

    def _clear_cache(self, pattern: str) -> None:
        """
        清除缓存
        :param pattern: 缓存键模式
        """
        if not self._cache_enabled or not hasattr(self.db_manager, 'cache_manager'):
            return

        try:
            self.db_manager.cache_manager.delete_pattern(pattern)
        except Exception as e:
            self.log.warning(f"缓存清除失败: {e}")


class ValidatorMixin:
    """验证功能混入类"""

    def _validate_openid(self, openId: str) -> bool:
        """验证openId"""
        if not openId or not isinstance(openId, str) or len(openId.strip()) == 0:
            self.log.error(f"无效的openId: {openId}")
            return False
        return True

    def _validate_username(self, userName: str) -> bool:
        """验证用户名"""
        if not userName or not isinstance(userName, str) or len(userName.strip()) == 0:
            self.log.error(f"无效的userName: {userName}")
            return False
        return True

    def _validate_score(self, score: int) -> bool:
        """验证积分数量"""
        if not isinstance(score, int):
            self.log.error(f"无效的score类型: {type(score)}")
            return False
        return True

    def _validate_pagination(self, page_number: int, page_size: int) -> Dict[str, int]:
        """
        验证分页参数
        :param page_number: 页码
        :param page_size: 页面大小
        :return: 验证后的参数
        """
        page_number = max(1, int(page_number))
        page_size = max(1, min(100, int(page_size)))
        return {
            'page_number': page_number,
            'page_size': page_size,
            'skip_count': (page_number - 1) * page_size
        }