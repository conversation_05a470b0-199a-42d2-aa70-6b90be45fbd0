"""
项目环境内积分系统测试
适用于实际项目环境，避免复杂的Mock依赖
"""

import unittest
import sys
import os

# 添加项目根目录到path
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

class ProjectScoreSystemTest(unittest.TestCase):
    """项目环境积分系统测试"""
    
    def setUp(self):
        """测试初始化"""
        self.test_openid = "test_user_001"
        self.test_username = "测试用户"
        self.test_userface = "test_avatar.jpg"
    
    def test_config_availability(self):
        """测试配置模块可用性"""
        try:
            from func.score.score_config import ScoreSystemConfig, ScoreOperationType
            
            # 测试基础配置
            self.assertEqual(ScoreSystemConfig.CHAT_SCORE, 1)
            self.assertEqual(ScoreSystemConfig.SING_COST, 2)
            self.assertEqual(ScoreSystemConfig.DANCE_COST, 3)
            
            # 测试枚举类型
            operations = [op.value for op in ScoreOperationType]
            self.assertIn("聊天", operations)
            self.assertIn("唱歌", operations)
            self.assertIn("跳舞", operations)
            
            print("✅ 配置模块测试通过")
            
        except Exception as e:
            self.fail(f"配置模块测试失败: {e}")
    
    def test_score_db_availability(self):
        """测试ScoreDB模块可用性"""
        try:
            from func.score.score_db import ScoreDB
            
            # 创建实例
            score_db = ScoreDB()
            
            # 测试验证方法
            self.assertTrue(score_db._validate_score_params("user", "name", 10, "test"))
            self.assertFalse(score_db._validate_score_params("", "name", 10, "test"))
            
            self.assertTrue(score_db._validate_user_params("user", "name"))
            self.assertFalse(score_db._validate_user_params("", "name"))
            
            print("✅ ScoreDB模块测试通过")
            
        except Exception as e:
            self.fail(f"ScoreDB模块测试失败: {e}")
    
    def test_oper_score_availability(self):
        """测试OperScore模块可用性"""
        try:
            from func.score.oper_score import OperScore
            
            # 创建实例
            oper_score = OperScore()
            
            # 测试基本方法调用
            rank_list = oper_score.find_score_rank(5)
            self.assertIsInstance(rank_list, list)
            
            stats = oper_score.get_system_stats()
            self.assertIsInstance(stats, dict)
            self.assertIn("total_users", stats)
            self.assertIn("total_score", stats)
            self.assertIn("average_score", stats)
            
            print("✅ OperScore模块测试通过")
            
        except Exception as e:
            self.fail(f"OperScore模块测试失败: {e}")
    
    def test_score_validation_logic(self):
        """测试积分验证逻辑"""
        from func.score.score_config import ScoreSystemConfig
        
        # 测试积分数量验证
        self.assertTrue(ScoreSystemConfig.validate_score_amount(100))
        self.assertTrue(ScoreSystemConfig.validate_score_amount(-50))
        self.assertFalse(ScoreSystemConfig.validate_score_amount(20000))
        
        # 测试操作类型验证
        self.assertTrue(ScoreSystemConfig.validate_operation_type("聊天"))
        self.assertTrue(ScoreSystemConfig.validate_operation_type("唱歌"))
        self.assertFalse(ScoreSystemConfig.validate_operation_type("无效操作"))
        
        # 测试消耗型操作
        self.assertTrue(ScoreSystemConfig.is_consumption_operation("唱歌"))
        self.assertFalse(ScoreSystemConfig.is_consumption_operation("聊天"))
        
        print("✅ 积分验证逻辑测试通过")
    
    def test_score_calculation_logic(self):
        """测试积分计算逻辑"""
        from func.score.score_config import ScoreSystemConfig
        
        # 测试获取操作积分
        self.assertEqual(ScoreSystemConfig.get_operation_score("聊天"), 1)
        self.assertEqual(ScoreSystemConfig.get_operation_score("唱歌"), -2)
        self.assertEqual(ScoreSystemConfig.get_operation_score("跳舞"), -3)
        
        print("✅ 积分计算逻辑测试通过")
    
    def test_system_integration(self):
        """测试系统集成"""
        try:
            from func.score.score_db import ScoreDB
            from func.score.oper_score import OperScore
            from func.score.score_config import ScoreSystemConfig
            
            # 验证所有模块都能正常导入和初始化
            score_db = ScoreDB()
            oper_score = OperScore()
            config = ScoreSystemConfig.get_config_dict()
            
            self.assertIsNotNone(score_db)
            self.assertIsNotNone(oper_score)
            self.assertIsInstance(config, dict)
            
            print("✅ 系统集成测试通过")
            
        except Exception as e:
            self.fail(f"系统集成测试失败: {e}")
    
    def test_error_handling(self):
        """测试错误处理"""
        try:
            from func.score.oper_score import OperScore
            oper_score = OperScore()
            
            # 测试错误输入处理
            result = oper_score.find_score_user("")  # 空用户ID
            self.assertIsNone(result)
            
            result = oper_score.find_score_user(None)  # None用户ID
            self.assertIsNone(result)
            
            print("✅ 错误处理测试通过")
            
        except Exception as e:
            self.fail(f"错误处理测试失败: {e}")

def run_project_tests():
    """运行项目环境测试"""
    print("=" * 60)
    print("开始运行项目环境积分系统测试...")
    print("=" * 60)
    
    # 检查模块可用性
    modules_status = {}
    
    try:
        from func.score.score_config import ScoreSystemConfig
        modules_status["Config"] = "✅"
    except Exception as e:
        modules_status["Config"] = f"❌ {e}"
    
    try:
        from func.score.score_db import ScoreDB
        modules_status["ScoreDB"] = "✅"
    except Exception as e:
        modules_status["ScoreDB"] = f"❌ {e}"
    
    try:
        from func.score.oper_score import OperScore
        modules_status["OperScore"] = "✅"
    except Exception as e:
        modules_status["OperScore"] = f"❌ {e}"
    
    print("模块可用性检查:")
    for module, status in modules_status.items():
        print(f"  {module}: {status}")
    print()
    
    # 运行单元测试
    suite = unittest.TestLoader().loadTestsFromTestCase(ProjectScoreSystemTest)
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    print("\n" + "=" * 60)
    if result.wasSuccessful():
        print("🎉 所有项目环境测试都通过了！")
        print("✅ 积分系统在项目环境中运行正常")
    else:
        print("⚠️  有部分测试失败，请检查上述错误信息")
        print(f"失败的测试: {len(result.failures)}")
        print(f"错误的测试: {len(result.errors)}")
    
    print("=" * 60)
    
    return result.wasSuccessful()

if __name__ == "__main__":
    success = run_project_tests()
    
    if success:
        print("\n✨ 项目环境测试总结:")
        print("  - 所有核心模块都能正常加载")
        print("  - 配置系统工作正常")  
        print("  - 数据库操作层功能正常")
        print("  - 业务逻辑层功能正常")
        print("  - 错误处理机制有效")
        print("  - 系统集成测试通过")
        print("\n🚀 积分系统已准备好在项目环境中使用！")
    else:
        print("\n❌ 项目环境测试未完全通过，需要进一步调试")
    
    sys.exit(0 if success else 1) 