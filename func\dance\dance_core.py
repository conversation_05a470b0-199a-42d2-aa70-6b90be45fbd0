# 跳舞功能
from threading import Thread
import time
import random
import asyncio
import json
from pathlib import Path
from func.log.default_log import DefaultLog
from func.cmd.cmd_core import CmdCore
from func.tts.tts_core import TTsCore
from func.score.oper_score import OperScore

from func.obs.obs_init import ObsInit
from func.obs.obs_websocket import ObsWebSocket,VideoStatus,VideoControl
from func.tools.string_util import StringUtil
from func.tools.singleton_mode import singleton

from func.gobal.data import SingData
from func.gobal.data import DanceData

# 导入WebSocket客户端（只使用dance文件夹下的客户端）
try:
    from func.dance.websocket_client import MMDWebSocketClient
    from func.dance.async_event_loop_manager import run_async_global, run_async_safe_global, ensure_event_loop
    WEBSOCKET_AVAILABLE = True
    ASYNC_MANAGER_AVAILABLE = True
    print("✅ 异步事件循环管理器已加载")
except ImportError as e:
    WEBSOCKET_AVAILABLE = False
    ASYNC_MANAGER_AVAILABLE = False
    print(f"⚠️ MMD WebSocket客户端模块未找到，将使用传统模式: {e}")

@singleton
class DanceCore:
    # 设置控制台日志
    log = DefaultLog().getLogger()

    singData = SingData()  # 唱歌数据
    danceData = DanceData()  # 跳舞数据

    ttsCore = TTsCore()  # tts语音
    cmdCore = CmdCore()  # cmd操作

    operScore = OperScore()  # 积分
    
    def __init__(self):
        self.obs = ObsInit().get_ws()
        
        # 初始化WebSocket客户端（用于连接UPBGE MMD服务端）
        self.mmd_websocket_client = None
        self.mmd_server_available = False
        self.last_connection_check = 0
        self.connection_check_interval = 30  # 30秒检查一次连接
        self.auto_reconnect_enabled = True   # 启用自动重连
        self.reconnect_task = None           # 重连任务
        
        # 舞蹈状态管理
        self.current_mmd_dance = None  # 当前MMD舞蹈名称
        self.mmd_dance_status = "停止"  # MMD舞蹈状态：停止/播放中/暂停
        
        # 日常动作配置
        self.idle_actions = {
            "default": "站立呼吸",  # 默认日常动作
            "available": ["站立呼吸", "眨眼", "轻微摇摆"],  # 可用的日常动作
            "current": None  # 当前日常动作
        }
        
        # 启动MMD WebSocket连接
        if WEBSOCKET_AVAILABLE and self.danceData.websocket_switch:
            # 确保全局事件循环已启动
            if ASYNC_MANAGER_AVAILABLE:
                ensure_event_loop()
            self._init_mmd_websocket()
        
        # 🔥 修复：移除传统客户端，统一使用MMD客户端
        # 传统的游戏WebSocket客户端功能现在集成到MMD客户端中

    def _init_mmd_websocket(self):
        """初始化MMD WebSocket客户端"""
        try:
            # 从配置或端口文件获取服务器地址
            server_host = getattr(self.danceData, 'mmd_server_host', 'localhost')
            server_port = getattr(self.danceData, 'mmd_server_port', 8765)
            mmd_enabled = getattr(self.danceData, 'mmd_server_enabled', True)
            
            if not mmd_enabled:
                self.log.info("MMD服务器功能已禁用")
                return
            
            self.mmd_websocket_client = MMDWebSocketClient(server_host, server_port)
            
            # 🔥 修复：启用客户端自动重连
            self.mmd_websocket_client.auto_reconnect_enabled = True
            self.mmd_websocket_client.max_reconnect_attempts = 20  # 增加重连次数
            self.mmd_websocket_client.heartbeat_interval = 45      # 心跳间隔45秒，避免与其他心跳冲突
            
            self.log.info(f"✅ 初始化MMD WebSocket客户端: {server_host}:{server_port}")
            
            # 在后台线程中尝试连接
            Thread(target=self._connect_mmd_server_async, daemon=True).start()
            
        except Exception as e:
            self.log.error(f"初始化MMD WebSocket客户端失败: {e}")
            self.mmd_websocket_client = None

    def _connect_mmd_server_async(self):
        """异步连接MMD服务器"""
        try:
            # 🔥 修复：使用全局事件循环管理器，避免事件循环冲突
            if ASYNC_MANAGER_AVAILABLE:
                success = run_async_safe_global(
                    self._try_connect_mmd_server(), 
                    timeout=10.0, 
                    default_value=False
                )
            else:
                # 回退到传统方式
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                try:
                    success = loop.run_until_complete(self._try_connect_mmd_server())
                finally:
                    try:
                        loop.close()
                    except:
                        pass
            
            if success:
                self.mmd_server_available = True
                self.log.info("✅ 成功连接到MMD服务器")
            else:
                self.mmd_server_available = False
                self.log.warning("⚠️ 无法连接到MMD服务器，将使用传统模式")
                
        except Exception as e:
            self.log.error(f"连接MMD服务器时出错: {e}")
            self.mmd_server_available = False

    async def _try_connect_mmd_server(self):
        """尝试连接MMD服务器"""
        if not self.mmd_websocket_client:
            return False
        
        try:
            success = await self.mmd_websocket_client.connect(show_messages=False)
            if success:
                # 测试连接
                status = await self.mmd_websocket_client.get_status()
                if "error" not in status:
                    return True
            return False
        except Exception as e:
            self.log.debug(f"连接MMD服务器失败: {e}")
            return False

    def check_mmd_connection(self):
        """检查MMD服务器连接状态"""
        current_time = time.time()
        if current_time - self.last_connection_check > self.connection_check_interval:
            self.last_connection_check = current_time
            
            if self.mmd_websocket_client:
                # 🔥 简化连接状态检查
                if not self.mmd_websocket_client.is_connected:
                    self.mmd_server_available = False
                    # 🔥 不再手动启动复杂的异步重连，让WebSocket客户端自己处理
                    if current_time % 60 < self.connection_check_interval:  # 每分钟最多打印一次
                        self.log.info("🔄 MMD连接已断开，将在下次使用时自动重连")
                else:
                    # 连接正常，更新状态
                    if not self.mmd_server_available:
                        self.mmd_server_available = True
                        self.log.info("✅ MMD服务器连接已恢复")
            else:
                # 客户端不存在，重新初始化
                if WEBSOCKET_AVAILABLE and self.danceData.websocket_switch:
                    self.log.info("🔄 重新初始化MMD WebSocket客户端...")
                    self._init_mmd_websocket()

    def _reconnect_mmd_server(self):
        """重新连接MMD服务器（简化版）"""
        try:
            if self.mmd_websocket_client:
                # 🔥 简化：直接使用WebSocket客户端的同步重连
                success = self.mmd_websocket_client._reconnect_sync()
                
                if success:
                    self.mmd_server_available = True
                    self.log.info("✅ MMD服务器重连成功")
                else:
                    self.mmd_server_available = False
                    self.log.info("❌ MMD服务器重连失败")
                    
        except Exception as e:
            self.log.debug(f"重新连接MMD服务器失败: {e}")
            self.mmd_server_available = False

    async def execute_mmd_dance(self, dance_name: str, user_name: str = "") -> dict:
        """执行MMD舞蹈"""
        if not self.mmd_websocket_client or not self.mmd_server_available:
            return {"error": "MMD服务器不可用"}
        
        try:
            # 配置舞蹈选项
            options = {
                "play_audio": True,  # 播放音频
                "loop": False,      # 不循环
                "speed": 1.0        # 正常速度
            }
            
            # 执行舞蹈
            result = await self.mmd_websocket_client.execute_dance(dance_name, options)
            
            if "error" not in result:
                self.current_mmd_dance = dance_name
                self.mmd_dance_status = "播放中"
                self.log.info(f"🎭 MMD舞蹈开始播放: {dance_name} (用户: {user_name})")
                
                # 停止日常动作
                self._stop_idle_actions()
            else:
                self.log.warning(f"MMD舞蹈执行失败: {result['error']}")
            
            return result
            
        except Exception as e:
            self.log.error(f"执行MMD舞蹈时出错: {e}")
            return {"error": f"执行舞蹈失败: {e}"}

    async def stop_mmd_dance(self) -> dict:
        """停止MMD舞蹈"""
        if not self.mmd_websocket_client or not self.mmd_server_available:
            return {"error": "MMD服务器不可用"}
        
        try:
            result = await self.mmd_websocket_client.stop_dance()
            
            if "error" not in result:
                self.current_mmd_dance = None
                self.mmd_dance_status = "停止"
                self.log.info("⏹️ MMD舞蹈已停止")
                
                # 启动日常动作
                self._start_idle_actions()
            
            return result
            
        except Exception as e:
            self.log.error(f"停止MMD舞蹈时出错: {e}")
            return {"error": f"停止舞蹈失败: {e}"}

    async def search_mmd_dances(self, query: str) -> dict:
        """搜索MMD舞蹈"""
        if not self.mmd_websocket_client or not self.mmd_server_available:
            return {"error": "MMD服务器不可用"}
        
        try:
            self.log.info(f"搜索MMD舞蹈：{query}")
            result = await self.mmd_websocket_client.search_dances(query, "both")
            return result
        except Exception as e:
            self.log.error(f"搜索MMD舞蹈时出错: {e}")
            return {"error": f"搜索失败: {e}"}

    async def get_mmd_status(self) -> dict:
        """获取MMD状态"""
        if not self.mmd_websocket_client or not self.mmd_server_available:
            return {"error": "MMD服务器不可用"}
        
        try:
            result = await self.mmd_websocket_client.get_status()
            return result
        except Exception as e:
            self.log.error(f"获取MMD状态时出错: {e}")
            return {"error": f"获取状态失败: {e}"}

    def _start_idle_actions(self):
        """启动日常动作"""
        try:
            # 这里预留日常动作的实现
            # 当前只是记录状态，实际的动作文件加载需要等文件准备好
            default_action = self.idle_actions["default"]
            self.idle_actions["current"] = default_action
            
            self.log.info(f"🧘 启动日常动作: {default_action}")
            
            # TODO: 实际加载和播放日常动作文件
            # 示例：
            # await self.execute_idle_animation(default_action)
            
        except Exception as e:
            self.log.error(f"启动日常动作失败: {e}")

    def _stop_idle_actions(self):
        """停止日常动作"""
        try:
            current_action = self.idle_actions["current"]
            if current_action:
                self.log.info(f"⏹️ 停止日常动作: {current_action}")
                self.idle_actions["current"] = None
                
                # TODO: 实际停止日常动作
                # 示例：
                # await self.stop_idle_animation()
                
        except Exception as e:
            self.log.error(f"停止日常动作失败: {e}")

    def execute_mmd_dance_sync(self, dance_name: str, user_name: str = "") -> dict:
        """同步执行MMD舞蹈（用于在主线程中调用）"""
        try:
            # 🔥 修复：使用全局事件循环管理器，避免事件循环冲突
            if ASYNC_MANAGER_AVAILABLE:
                result = run_async_safe_global(
                    self.execute_mmd_dance(dance_name, user_name),
                    timeout=30.0,
                    default_value={"error": "执行超时"}
                )
            else:
                # 回退到传统方式
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                try:
                    result = loop.run_until_complete(self.execute_mmd_dance(dance_name, user_name))
                finally:
                    try:
                        loop.close()
                    except:
                        pass
            return result
        except Exception as e:
            self.log.error(f"同步执行MMD舞蹈失败: {e}")
            return {"error": f"执行失败: {e}"}

    # 🔥 新增：直接同步方法，避免异步复杂性
    def execute_dance_direct(self, dance_name: str, user_name: str = "") -> dict:
        """直接执行舞蹈（同步，快速响应）"""
        if not self.mmd_websocket_client:
            return {"error": "MMD客户端未初始化"}
        
        # 🔥 不再预先检查连接状态，让同步方法自己处理重连
        try:
            # 直接使用同步方法，内部会自动处理重连
            result = self.mmd_websocket_client.execute_dance_sync(dance_name)
            
            if "error" not in result:
                self.current_mmd_dance = dance_name
                self.mmd_dance_status = "播放中"
                self.mmd_server_available = True  # 执行成功说明连接正常
                self.log.info(f"🎭 MMD舞蹈开始播放: {dance_name} (用户: {user_name})")
                
                # 停止日常动作
                self._stop_idle_actions()
            else:
                self.log.warning(f"MMD舞蹈执行失败: {result['error']}")
                # 如果是连接错误，更新状态
                if "连接" in result['error'] or "服务器" in result['error']:
                    self.mmd_server_available = False
            
            return result
            
        except Exception as e:
            self.log.error(f"直接执行MMD舞蹈时出错: {e}")
            return {"error": f"执行舞蹈失败: {e}"}
    
    def stop_dance_direct(self) -> dict:
        """直接停止舞蹈（同步，快速响应）"""
        if not self.mmd_websocket_client:
            return {"error": "MMD客户端未初始化"}
        
        try:
            # 直接使用同步方法，内部会自动处理重连
            result = self.mmd_websocket_client.stop_dance_sync()
            
            if "error" not in result:
                self.current_mmd_dance = None
                self.mmd_dance_status = "停止"
                self.mmd_server_available = True  # 执行成功说明连接正常
                self.log.info("⏹️ MMD舞蹈已停止")
                
                # 启动日常动作
                self._start_idle_actions()
            else:
                self.log.warning(f"MMD舞蹈停止失败: {result['error']}")
                # 如果是连接错误，更新状态
                if "连接" in result['error'] or "服务器" in result['error']:
                    self.mmd_server_available = False
            
            return result
            
        except Exception as e:
            self.log.error(f"直接停止MMD舞蹈时出错: {e}")
            return {"error": f"停止舞蹈失败: {e}"}
    
    def search_dances_direct(self, query: str) -> dict:
        """直接搜索舞蹈（同步，快速响应）"""
        if not self.mmd_websocket_client:
            return {"error": "MMD客户端未初始化"}
        
        try:
            self.log.info(f"搜索MMD舞蹈：{query}")
            # 直接使用同步方法，内部会自动处理重连
            result = self.mmd_websocket_client.search_dances_sync(query, "both")
            
            if "error" not in result:
                self.mmd_server_available = True  # 执行成功说明连接正常
            else:
                # 如果是连接错误，更新状态
                if "连接" in result['error'] or "服务器" in result['error']:
                    self.mmd_server_available = False
            
            return result
        except Exception as e:
            self.log.error(f"直接搜索MMD舞蹈时出错: {e}")
            return {"error": f"搜索失败: {e}"}
    
    def _stop_dance_direct(self):
        """直接停止舞蹈，简化处理逻辑"""
        try:
            stopped = False
            
            # 停止MMD舞蹈
            if self.mmd_websocket_client:
                print("停止MMD舞蹈--------")
                try:
                    # 🔥 使用直接同步方法，内部会自动处理重连
                    result = self.stop_dance_direct()
                    if "error" not in result:
                        stopped = True
                        self.log.info("✅ MMD舞蹈已停止")
                    else:
                        self.log.warning(f"停止MMD舞蹈失败: {result['error']}")
                        
                except Exception as e:
                    self.log.warning(f"停止MMD舞蹈时出错: {e}")
            
            # 停止传统模式跳舞
            if self.danceData.is_dance == 1:
                self.danceData.is_dance = 2  # 标记跳舞完成  
                stopped = True
                self.log.info("✅ 传统舞蹈已停止")
            
            # 启动日常动作
            if stopped:
                self._start_idle_actions()
                self.ttsCore.tts_say("跳舞已停止")
            else:
                self.log.info("当前没有进行中的舞蹈")
                
        except Exception as e:
            self.log.error(f"停止舞蹈时出错: {e}")
            # 强制停止
            self.current_mmd_dance = None
            self.mmd_dance_status = "停止"
            self.danceData.is_dance = 2

    # 跳舞任务
    def check_dance(self,sched1):
        # 检查MMD连接状态
        self.check_mmd_connection()
        
        if not self.danceData.DanceQueueList.empty() and self.danceData.is_dance == 2:
            self.danceData.is_dance = 1
            # 停止所有定时任务
            sched1.pause()
            # 停止所有在执行的任务
            self.cmdCore.cmd("all","\\dance","0", "check_dance")
            self.ttsCore.tts_say("开始跳舞了，大家嗨起来")
            dance_json = self.danceData.DanceQueueList.get()
            # 开始跳舞任务
            self.dance(dance_json)
            # 重启定时任务
            sched1.resume()
            self.danceData.is_dance = 2  # 跳舞完成

    # 跳舞操作
    def dance(self,dance_json):
        video_path = dance_json["video_path"]
        self.log.info(dance_json)
        self.obs.control_video("背景音乐", VideoControl.PAUSE.value)
        # ============== 跳舞视频 ==============
        # 第一次播放
        if video_path != self.danceData.dance_now_path:
            self.obs.play_video("video", video_path)
        else:
            self.obs.control_video("video", VideoControl.RESTART.value)
        # 赋值当前跳舞视频
        self.danceData.dance_now_path = video_path
        time.sleep(1)
        while self.obs.get_video_status("video") != VideoStatus.END.value and self.danceData.is_dance == 1:
            time.sleep(1)
        self.obs.control_video("video", VideoControl.STOP.value)
        # ============== end ==============
        self.obs.control_video("背景音乐", VideoControl.PLAY.value)

    # 唱歌跳舞
    def sing_dance(self,songname):
        # 提示语为空，随机视频
        self.danceData.video_path = ""
        if songname != "":
            matches_list = StringUtil.fuzzy_match_list(songname, self.danceData.dance_video)
            if len(matches_list) > 0:
                rnd_video = random.randrange(0, len(matches_list))
                video_path = matches_list[rnd_video]
        if self.danceData.video_path == "":
            return
        # 第一次播放
        if self.danceData.video_path != self.danceData.singdance_now_path:
            self.obs.play_video("唱歌视频", self.danceData.video_path)
        else:
            self.obs.control_video("唱歌视频", VideoControl.RESTART.value)
        # 赋值当前表情视频
        self.danceData.singdance_now_path = self.danceData.video_path
        time.sleep(1)
        while self.singData.is_singing == 1:
            # 结束循环重新播放
            if self.obs.get_video_status("唱歌视频") == VideoStatus.END.value:
                self.obs.control_video("唱歌视频", VideoControl.RESTART.value)
            time.sleep(1)

    # 表情播放[不用停止跳舞]
    def emote_play_nodance(self,eomte_path):
        self.danceData.emote_video_lock.acquire()
        self.log.info(f"播放表情:{eomte_path}")
        # 第一次播放
        if eomte_path != self.danceData.emote_now_path:
            self.obs.play_video("表情", eomte_path)
        else:
            self.obs.control_video("表情", VideoControl.RESTART.value)
        # 赋值当前表情视频
        self.danceData.emote_now_path = eomte_path
        time.sleep(1)
        # 20秒超时停止播放
        sec = 20
        while self.obs.get_video_status("表情") != VideoStatus.END.value and sec > 0:
            time.sleep(1)
            sec = sec - 1
        time.sleep(1)
        self.obs.control_video("表情", VideoControl.STOP.value)
        self.danceData.emote_video_lock.release()

    # 表情播放
    def emote_play(self,eomte_path):
        self.danceData.emote_video_lock.acquire()
        self.obs.control_video("video", VideoControl.PAUSE.value)
        self.log.info(f"播放表情:{eomte_path}")
        # 第一次播放
        if eomte_path != self.danceData.emote_now_path:
            self.obs.play_video("表情", eomte_path)
        else:
            self.obs.control_video("表情", VideoControl.RESTART.value)
        # 赋值当前表情视频
        self.danceData.emote_now_path = eomte_path
        time.sleep(1)
        # 20秒超时停止播放
        sec = 20
        while self.obs.get_video_status("表情") != VideoStatus.END.value and sec > 0:
            time.sleep(1)
            sec = sec - 1
        time.sleep(1)
        self.obs.control_video("表情", VideoControl.STOP.value)
        self.obs.control_video("video", VideoControl.PLAY.value)
        self.danceData.emote_video_lock.release()

    # 跳舞表情入口处理
    def msg_deal_emotevideo(self, traceid, query, uid, user_name):
        text = ["#","表情"]
        is_contain = StringUtil.has_string_reg_list(f"^{text}", query)
        if is_contain is not None:
            num = StringUtil.is_index_contain_string(text, query)
            queryExtract = query[num: len(query)]  # 提取提问语句
            queryExtract = queryExtract.strip()
            self.log.info(f"[{traceid}]跳舞表情：" + queryExtract)
            video_path = ""
            if queryExtract == "rnd" or queryExtract == "随机":
                rnd_video = random.randrange(0, len(self.danceData.emote_video))
                video_path = self.danceData.emote_video[rnd_video]
            else:
                matches_list = StringUtil.fuzzy_match_list(queryExtract, self.danceData.emote_video)
                if len(matches_list) > 0:
                    rnd_video = random.randrange(0, len(matches_list))
                    video_path = matches_list[rnd_video]
            # 第一次播放
            if video_path != "":
                if self.danceData.is_dance == 1:
                    emote_play_thread = Thread(target=self.emote_play, args=(video_path,))
                    emote_play_thread.start()
                else:
                    emote_play_thread = Thread(target=self.emote_play_nodance, args=(video_path,))
                    emote_play_thread.start()
            return True

        # 跳舞中不执行其他任务
        if self.danceData.is_dance == 1:
            return True

        return False

    # 跳舞入口处理
    def msg_deal_dance(self, traceid, query, uid, user_name):
        # 跳舞
        text = ["跳舞", "跳一下", "舞蹈"]
        is_contain = StringUtil.has_string_reg_list(f"^{text}", query)
        if is_contain is not None:
            num = StringUtil.is_index_contain_string(text, query)
            queryExtract = query[num: len(query)]  # 提取提问语句
            queryExtract = queryExtract.strip()
            self.log.info(f"[{traceid}]跳舞提示：" + queryExtract)

            # 积分
            print("dance----msg_deal-----oper_score-----", query)
            self.operScore.oper_score(uid, user_name, -3, "", "dance")
            
            # 处理MMD舞蹈请求
            if self.mmd_server_available and self.mmd_websocket_client:
                return self._handle_mmd_dance_request(traceid, queryExtract, uid, user_name)
            else:
                # 检查MMD连接状态
                self.check_mmd_connection()
                if not self.mmd_server_available:
                    self.log.info("🔄 重新初始化MMD WebSocket客户端...")
                    self._init_mmd_websocket()
                    if self.mmd_server_available and self.mmd_websocket_client:
                        return self._handle_mmd_dance_request(traceid, queryExtract, uid, user_name)
                    else:
                        return False
                        # #回退到传统模式
                        # return self._handle_traditional_dance_request(traceid, queryExtract, uid, user_name)
        
        # 处理停止跳舞命令
        stop_text = ["停止跳舞", "停舞", "切舞"]
        is_stop = StringUtil.has_string_reg_list(f"^{stop_text}", query)
        if is_stop is not None:
            print(f"[{traceid}]停止跳舞请求：{user_name}")
            self._stop_dance_direct()
            return True
        
        return False

    def _handle_mmd_dance_request(self, traceid, queryExtract, uid, user_name):
        """处理MMD舞蹈请求"""
        try:
            self.log.info(f"[{traceid}]跳舞请求：{user_name}")
            # 🔥 修复：直接在当前线程处理，避免异步复杂性
            self._process_mmd_dance_direct(traceid, queryExtract, uid, user_name)
            return True
            
        except Exception as e:
            self.log.error(f"处理MMD舞蹈请求失败: {e}")
            return False

    def _process_mmd_dance_direct(self, traceid, queryExtract, uid, user_name):
        """直接处理MMD舞蹈请求（同步版本）"""
        try:
            # 如果没有指定舞蹈名，随机选择
            if not queryExtract or queryExtract in ["", "随机", "rnd"]:
                dance_name = "随机舞蹈"  # 这里可以改为实际的随机选择逻辑
            else:
                dance_name = queryExtract

            # 🔥 使用直接同步方法，快速响应
            # 搜索舞蹈
            search_result = self.search_dances_direct(dance_name)
            
            if "error" in search_result:
                self.log.warning(f"搜索舞蹈失败: {search_result['error']}")
                self.ttsCore.tts_say(f"抱歉，没有找到舞蹈：{dance_name}")
                return
            
            dances = search_result.get("dances", [])
            if not dances:
                self.log.warning(f"未找到匹配的舞蹈: {dance_name}")
                self.ttsCore.tts_say(f"抱歉，没有找到舞蹈：{dance_name}")
                return
            
            # 选择第一个匹配的舞蹈
            selected_dance = dances[0]["name"]
            
            # 执行舞蹈
            result = self.execute_dance_direct(selected_dance, user_name)
            
            if "error" in result:
                self.log.warning(f"执行MMD舞蹈失败: {result['error']}")
                self.ttsCore.tts_say("舞蹈执行失败")
            else:
                self.log.info(f"✅ MMD舞蹈开始播放: {selected_dance}")
                self.ttsCore.tts_say(f"开始为{user_name}跳舞")
                
        except Exception as e:
            self.log.error(f"直接处理MMD舞蹈请求时出错: {e}")
            self.ttsCore.tts_say("舞蹈系统出错了")

    def _handle_traditional_dance_request(self, traceid, queryExtract, uid, user_name):
        """处理传统跳舞请求（回退模式）"""
        # 🔥 修复：移除对传统WebSocket客户端的依赖
        # 只处理本地视频播放
        
        video_path = ""
        # 提示语为空，随机视频
        if queryExtract == "":
            rnd_video = random.randrange(0, len(self.danceData.dance_video))
            video_path = self.danceData.dance_video[rnd_video]
        else:
            matches_list = StringUtil.fuzzy_match_list(queryExtract, self.danceData.dance_video)
            if len(matches_list) > 0:
                rnd_video = random.randrange(0, len(matches_list))
                video_path = matches_list[rnd_video]
        
        # 加入跳舞队列
        if video_path != "":
            dance_json = {"traceid": traceid, "prompt": queryExtract, "username": user_name,
                          "video_path": video_path}
            self.danceData.DanceQueueList.put(dance_json)
            self.log.info(f"📹 传统模式：加入跳舞队列 - {queryExtract}")
            return True
        else:
            self.log.info("跳舞视频不存在：" + queryExtract)
            return True

    def get_dance_status(self):
        """获取跳舞状态信息"""
        # 获取详细的连接信息
        connection_info = {
            "websocket_enabled": self.danceData.websocket_switch,
            "last_connection_check": self.last_connection_check,
            "auto_reconnect_enabled": self.auto_reconnect_enabled
        }
        
        # 如果有MMD WebSocket客户端，获取详细信息
        if self.mmd_websocket_client:
            client_info = self.mmd_websocket_client.get_connection_info()
            connection_info.update({
                "mmd_client_status": client_info["status"],
                "mmd_client_uptime": client_info["uptime"],
                "mmd_reconnect_attempts": client_info["reconnect_attempts"],
                "mmd_server_address": client_info["server_address"],
                "mmd_heartbeat_status": client_info["heartbeat_status"],
                "mmd_auto_reconnect": client_info["auto_reconnect"],
                "mmd_connection_quality": client_info["connection_quality"]
            })
        
        status = {
            "mmd_server_available": self.mmd_server_available,
            "current_mmd_dance": self.current_mmd_dance,
            "mmd_dance_status": self.mmd_dance_status,
            "idle_action": self.idle_actions["current"],
            "traditional_dance_status": self.danceData.is_dance,
            "connection_info": connection_info
        }
        return status

    def force_stop_all_dances(self):
        """强制停止所有舞蹈"""
        try:
            self._stop_dance_direct()
            self.log.info("🛑 已强制停止所有舞蹈")
        except Exception as e:
            self.log.error(f"强制停止舞蹈失败: {e}")
