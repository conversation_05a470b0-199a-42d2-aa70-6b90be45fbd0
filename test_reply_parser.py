#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试回复消息解析功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from func.llm.chat_db import ChatDB

def test_reply_parser():
    """测试回复消息解析功能"""
    
    # 创建ChatDB实例
    chat_db = ChatDB()
    
    # 测试用例
    test_cases = [
        {
            "input": "[回复<AI呦呦-3D舞宠:dc89b178fba24e20a036878e8cae3c68> 的消息：复合点] 复合啥",
            "expected": {
                "sender_name": "AI呦呦-3D舞宠",
                "sender_id": "dc89b178fba24e20a036878e8cae3c68",
                "question": "复合点",
                "reply_content": "复合啥"
            }
        },
        {
            "input": "[回复<用户名:123456> 的消息：你好吗] 我很好",
            "expected": {
                "sender_name": "用户名",
                "sender_id": "123456",
                "question": "你好吗",
                "reply_content": "我很好"
            }
        },
        {
            "input": "[回复<测试用户:test_id_123> 的消息：今天天气怎么样] 今天天气很好",
            "expected": {
                "sender_name": "测试用户",
                "sender_id": "test_id_123",
                "question": "今天天气怎么样",
                "reply_content": "今天天气很好"
            }
        },
        {
            "input": "这不是回复消息",
            "expected": None
        },
        {
            "input": "",
            "expected": None
        }
    ]
    
    print("开始测试回复消息解析功能...")
    print("=" * 50)
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n测试用例 {i}:")
        print(f"输入: {test_case['input']}")
        
        result = chat_db.parse_reply_message(test_case['input'])
        
        print(f"期望结果: {test_case['expected']}")
        print(f"实际结果: {result}")
        
        if result == test_case['expected']:
            print("✅ 测试通过")
        else:
            print("❌ 测试失败")
            
    print("\n" + "=" * 50)
    print("测试完成!")

def test_reply_insertion():
    """测试回复插入功能（需要MongoDB连接）"""
    
    print("\n开始测试回复插入功能...")
    print("=" * 50)
    
    try:
        chat_db = ChatDB()
        
        # 测试数据
        reply_data = {
            "traceid": "test_reply_123",
            "sender_name": "测试用户",
            "sender_id": "test_user_123",
            "question": "测试问题",
            "reply_content": "测试回复内容"
        }
        
        print(f"插入测试回复数据: {reply_data}")
        
        result = chat_db.insert_reply(reply_data)
        
        if result:
            print("✅ 回复插入成功")
        else:
            print("❌ 回复插入失败")
            
    except Exception as e:
        print(f"❌ 测试过程中出现异常: {e}")
        print("注意: 此测试需要MongoDB连接，如果没有连接会失败")
    
    print("=" * 50)
    print("回复插入测试完成!")

if __name__ == "__main__":
    # 测试解析功能
    test_reply_parser()
    
    # 测试插入功能（可选，需要数据库连接）
    print("\n是否测试数据库插入功能？(需要MongoDB连接) [y/N]: ", end="")
    user_input = input().strip().lower()
    
    if user_input in ['y', 'yes']:
        test_reply_insertion()
    else:
        print("跳过数据库插入测试")
