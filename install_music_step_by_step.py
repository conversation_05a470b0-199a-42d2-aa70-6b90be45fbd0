#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分步安装音乐转换项目依赖
"""
import subprocess
import sys
import os

# 目标环境路径
TARGET_ENV = r"H:/baidudown/AI-YinMei-v2.0.0/MB7-YM2-runtime"
PYTHON_EXE = os.path.join(TARGET_ENV, "python.exe")

def run_pip_install(packages, description="", extra_args=None):
    """运行pip安装命令"""
    if not packages:
        return True
    
    print(f"\n🚀 {description}")
    print(f"安装包: {', '.join(packages)}")
    
    cmd = [PYTHON_EXE, "-m", "pip", "install"] + packages + ["--upgrade"]
    if extra_args:
        cmd.extend(extra_args)
    
    print(f"执行命令: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(cmd, check=False, text=True)
        if result.returncode == 0:
            print(f"✅ {description} 安装成功")
            return True
        else:
            print(f"❌ {description} 安装失败，返回码: {result.returncode}")
            return False
    except Exception as e:
        print(f"❌ {description} 安装异常: {e}")
        return False

def main():
    """主函数"""
    print("=== 分步安装音乐转换项目依赖 ===")
    
    # 检查环境
    if not os.path.exists(PYTHON_EXE):
        print(f"❌ Python可执行文件不存在: {PYTHON_EXE}")
        return
    
    # 第1步：升级基础工具
    print("\n" + "="*50)
    print("第1步：升级基础工具")
    run_pip_install(["pip", "setuptools", "wheel"], "基础工具升级")
    
    # 第2步：安装基础科学计算包
    print("\n" + "="*50)
    print("第2步：安装基础科学计算包")
    basic_packages = ["numpy", "scipy", "matplotlib", "pandas"]
    run_pip_install(basic_packages, "基础科学计算包")
    
    # 第3步：安装PyTorch (CPU版本，更快)
    print("\n" + "="*50)
    print("第3步：安装PyTorch (CPU版本)")
    pytorch_packages = ["torch", "torchvision", "torchaudio"]
    run_pip_install(pytorch_packages, "PyTorch CPU版本", 
                   ["--index-url", "https://download.pytorch.org/whl/cpu"])
    
    # 第4步：安装音频处理包
    print("\n" + "="*50)
    print("第4步：安装音频处理包")
    audio_packages = ["librosa", "soundfile", "pydub", "audioread"]
    run_pip_install(audio_packages, "音频处理包")
    
    # 第5步：安装Web框架
    print("\n" + "="*50)
    print("第5步：安装Web框架")
    web_packages = ["flask", "requests", "selenium"]
    run_pip_install(web_packages, "Web框架")
    
    # 第6步：安装机器学习相关包
    print("\n" + "="*50)
    print("第6步：安装机器学习相关包")
    ml_packages = ["transformers", "pytorch_lightning", "scikit_learn"]
    run_pip_install(ml_packages, "机器学习包")
    
    # 第7步：安装ONNX相关包
    print("\n" + "="*50)
    print("第7步：安装ONNX相关包")
    onnx_packages = ["onnx", "onnxruntime"]
    run_pip_install(onnx_packages, "ONNX包")
    
    # 第8步：安装其他工具包
    print("\n" + "="*50)
    print("第8步：安装其他工具包")
    other_packages = ["tqdm", "loguru", "rich", "psutil", "Pillow"]
    run_pip_install(other_packages, "其他工具包")
    
    # 第9步：安装专业音频包
    print("\n" + "="*50)
    print("第9步：安装专业音频包")
    pro_audio_packages = ["pedalboard", "pyrubberband", "pyworld"]
    run_pip_install(pro_audio_packages, "专业音频包")
    
    # 第10步：安装剩余包
    print("\n" + "="*50)
    print("第10步：安装剩余包")
    remaining_packages = [
        "bottle", "cryptography", "ffmpeg_python", "natsort", 
        "omegaconf", "playsound", "pyperclip", "six", "wget",
        "screeninfo", "samplerate", "yt_dlp", "altgraph", 
        "certifi", "cffi", "future", "PyYAML", "resampy",
        "sounddevice", "starlette", "urllib3", "einops"
    ]
    run_pip_install(remaining_packages, "剩余包")
    
    print("\n" + "="*50)
    print("安装完成！")
    print("建议运行以下命令测试核心功能：")
    print(f'{PYTHON_EXE} -c "import torch; import librosa; import flask; print(\'核心依赖导入成功\')"')

if __name__ == "__main__":
    main()
