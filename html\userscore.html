<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>用户积分</title>
    <script type="text/javascript" src="jquery-3.7.1.min.js"></script>
    <style>
        body {
            background-color: transparent;
            font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
            overflow: hidden;
            margin: 0;
            padding: 0;
        }

        .avatar {
            float: left;
            width: 120px;
            height: 120px;
            border-radius: 60px;
            object-fit: cover;
            background-position: center;
            background-size: cover;
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.4);
            border: 4px solid rgba(255, 255, 255, 0.9);
            transition: all 0.3s ease;
        }

        .avatar:hover {
            transform: scale(1.05) rotate(5deg);
            box-shadow: 0 8px 25px rgba(255, 215, 0, 0.6);
        }

        .rightArea {
            float: left;
            width: auto;
            height: 120px;
            margin-left: 25px;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .score {
            font-size: 72px;
            font-weight: bold;
            background: linear-gradient(45deg, #FFD700, #FFA500, #FF6B35);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
            animation: scoreGlow 2s ease-in-out infinite alternate;
            line-height: 1;
            margin-bottom: 8px;
        }

        @keyframes scoreGlow {
            from {
                filter: drop-shadow(0 0 8px #FFD700);
                transform: scale(1);
            }
            to {
                filter: drop-shadow(0 0 20px #FFA500);
                transform: scale(1.02);
            }
        }

        .username {
            font-size: 42px;
            color: #FFFFFF;
            text-shadow: 2px 2px 6px rgba(0, 0, 0, 0.8);
            font-weight: 600;
            line-height: 1;
        }

        .tip {
            width: auto;
            height: auto;
            position: absolute;
            padding: 20px 25px;
            background: linear-gradient(135deg,
                rgba(0, 0, 0, 0.8) 0%,
                rgba(30, 30, 30, 0.9) 50%,
                rgba(0, 0, 0, 0.8) 100%);
            border-radius: 20px;
            backdrop-filter: blur(15px);
            border: 2px solid rgba(255, 215, 0, 0.3);
            animation: tipAnimation 18s ease-out forwards;
            transform-origin: center bottom;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
        }

        @keyframes tipAnimation {
            0% {
                opacity: 0;
                transform: translateY(80px) scale(0.7) rotateX(20deg);
            }
            8% {
                opacity: 1;
                transform: translateY(0) scale(1) rotateX(0deg);
            }
            12% {
                transform: translateY(-10px) scale(1.02) rotateX(0deg);
            }
            16% {
                transform: translateY(0) scale(1) rotateX(0deg);
            }
            80% {
                opacity: 1;
                transform: translateY(-120px) scale(1) rotateX(0deg);
            }
            100% {
                opacity: 0;
                transform: translateY(-250px) scale(0.8) rotateX(-20deg);
            }
        }

        /* 积分变化特效 */
        .score-change {
            position: absolute;
            font-size: 28px;
            font-weight: bold;
            pointer-events: none;
            z-index: 1000;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
        }

        .score-positive {
            color: #00FF88;
            animation: scoreUp 2.5s ease-out forwards;
        }

        .score-negative {
            color: #FF4444;
            animation: scoreDown 2.5s ease-out forwards;
        }

        @keyframes scoreUp {
            0% {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
            50% {
                opacity: 1;
                transform: translateY(-30px) scale(1.3);
            }
            100% {
                opacity: 0;
                transform: translateY(-80px) scale(1.1);
            }
        }

        @keyframes scoreDown {
            0% {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
            50% {
                opacity: 1;
                transform: translateY(30px) scale(1.2);
            }
            100% {
                opacity: 0;
                transform: translateY(80px) scale(0.9);
            }
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .avatar {
                width: 100px;
                height: 100px;
                border-radius: 50px;
            }

            .score {
                font-size: 58px;
            }

            .username {
                font-size: 36px;
            }

            .rightArea {
                margin-left: 20px;
                height: 100px;
            }

            .tip {
                padding: 15px 20px;
            }
        }

        @media (max-width: 480px) {
            .avatar {
                width: 80px;
                height: 80px;
                border-radius: 40px;
            }

            .score {
                font-size: 48px;
            }

            .username {
                font-size: 30px;
            }

            .rightArea {
                margin-left: 15px;
                height: 80px;
            }
        }
    </style>
</head>
<body>
<div id="area">

</div>

<script>
    let ws
    function connect() {
        ws = new WebSocket("ws://localhost:18765");
        ws.onopen = function () {
            console.log("Connected!------userscore---------");
            ws.send("用户积分--");
        };
        ws.onmessage = function (event) {
            console.log("Received message:", event.data);
            try {
                const jsonstr = JSON.parse(event.data);
                if (jsonstr["type"] !== "用户积分") {
                    return;
                }

                const timestamp = Date.now();
                const id = "tip" + timestamp;

                // 创建积分显示元素
                const html = `
                    <div id="${id}" class="tip">
                        <div class="avatar"></div>
                        <div class="rightArea">
                            <div class="score"></div>
                            <div class="username"></div>
                        </div>
                    </div>
                `;

                $('#area').append(html);

                // 随机位置显示
                const ntop = getRandomInt(50, Math.max(100, window.innerHeight - 300));
                const nleft = getRandomInt(50, Math.max(100, window.innerWidth - 400));

                const $tip = $('#' + id);
                $tip.css({
                    'top': ntop + 'px',
                    'left': nleft + 'px'
                });

                // 设置头像
                const avatarUrl = jsonstr["userface"] || 'default-avatar.png';
                $tip.find('.avatar').css("background-image", `url('${avatarUrl}')`);

                // 设置用户名
                const username = jsonstr["username"] || "匿名用户";
                $tip.find('.username').text(username);

                // 设置积分，添加动画效果
                const score = jsonstr["score"] || 0;
                const scoreChange = jsonstr["score_change"] || 0;
                $tip.find('.score').text(score + "积分");

                // 如果有积分变化，显示变化动画
                if (scoreChange !== 0) {
                    showScoreChange(nleft + 60, ntop + 60, scoreChange);
                }

                // 添加音效（如果需要）
                playNotificationSound();

                // 18秒后删除元素（与CSS动画时间一致）
                setTimeout(function () {
                    $tip.fadeOut(500, function() {
                        $(this).remove();
                    });
                }, 18000);

            } catch (error) {
                console.error("解析积分消息失败:", error);
            }
        };
        ws.onclose = function (event) {
            console.log('WebSocket连接已关闭');
            setTimeout(reconnect, 5000);
        };
    }

    function reconnect() {
        if (ws !== null && (ws.readyState === WebSocket.CLOSED || ws.readyState === WebSocket.CLOSING)) {
            console.log("尝试重连WebSocket...");
            connect();
        }
    }

    // 显示积分变化动画
    function showScoreChange(x, y, change) {
        const changeId = "change" + Date.now();
        const changeClass = change > 0 ? "score-positive" : "score-negative";
        const changeText = change > 0 ? `+${change}` : `${change}`;

        const changeHtml = `
            <div id="${changeId}" class="score-change ${changeClass}"
                 style="left: ${x}px; top: ${y}px;">
                ${changeText}
            </div>
        `;

        $('body').append(changeHtml);

        // 2.5秒后删除
        setTimeout(() => {
            $('#' + changeId).remove();
        }, 2500);
    }

    // 播放通知音效
    function playNotificationSound() {
        try {
            // 创建简单的提示音
            if (typeof AudioContext !== 'undefined' || typeof webkitAudioContext !== 'undefined') {
                const audioContext = new (AudioContext || webkitAudioContext)();
                const oscillator = audioContext.createOscillator();
                const gainNode = audioContext.createGain();

                oscillator.connect(gainNode);
                gainNode.connect(audioContext.destination);

                oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
                oscillator.frequency.exponentialRampToValueAtTime(400, audioContext.currentTime + 0.1);

                gainNode.gain.setValueAtTime(0.05, audioContext.currentTime);
                gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.1);

                oscillator.start(audioContext.currentTime);
                oscillator.stop(audioContext.currentTime + 0.1);
            }
        } catch (error) {
            console.log("音效播放失败:", error);
        }
    }

    function getRandomInt(min, max) {
        min = Math.ceil(min);
        max = Math.floor(max);
        return Math.floor(Math.random() * (max - min + 1)) + min;
    }

    // 初始化连接
    connect();

</script>
</body>
</html>