from flask import jsonify
from flask.views import MethodView

class BaseView(MethodView):
    def __init__(self):
        super().__init__()
        self.status = 200
        
    def formattingData(self, data):
        """
        格式化返回数据
        Args:
            data: 需要返回的数据
        Returns:
            json格式的响应
        """
        return jsonify({
            "code": self.status,
            "data": data
        })
        
    def dispatch_request(self, *args, **kwargs):
        """
        重写父类的dispatch_request方法
        处理请求并返回响应
        """
        try:
            response = super().dispatch_request(*args, **kwargs)
            return response
        except Exception as e:
            print(e)
            self.status = 500
            return self.formattingData({
                "error": str(e)
            })