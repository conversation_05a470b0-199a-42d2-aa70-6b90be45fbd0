#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试酷狗音乐搜索的第二个方案
"""

import sys
import os
sys.path.append('.')

from kugou import Kugou_music
import json

def test_search_with_mixsong():
    """测试搜索功能，包括第二个方案"""
    print("=" * 80)
    print("测试酷狗音乐搜索功能（包含第二个方案）")
    print("=" * 80)
    
    # 创建酷狗实例
    kugou = Kugou_music()
    
    # 测试歌曲列表
    test_songs = [
        # "周杰伦 - 稻香",
        # "蓝光乐队 - 轻舟",
        "张杰 - 轻舟",
        # "草帽酱 - 戏说"
    ]
    
    for i, song in enumerate(test_songs, 1):
        print(f"\n{'='*60}")
        print(f"[{i}/{len(test_songs)}] 测试歌曲: {song}")
        print(f"{'='*60}")
        
        try:
            # 搜索歌曲，获取详细结果
            search_results = kugou.search_music(song, limit=5, return_list=True)
            
            if not search_results:
                print(f"❌ 未找到歌曲: {song}")
                continue
            
            print(f"找到 {len(search_results)} 个候选结果:")
            for j, result in enumerate(search_results[:3], 1):
                print(f"  {j}. {result['artist_name']} - {result['song_name']}")
                print(f"     Hash: {result['file_hash']}")
                print(f"     MixSongID: {result.get('mix_song_id', 'N/A')}")
                print(f"     匹配度: {result['match_score']}, URL分数: {result['url_score']}, BackupUrl: {result['has_backup_url']}")
                
                # 测试完全匹配判断
                is_exact = kugou._is_exact_match(song, result['song_name'], result['artist_name'])
                print(f"     完全匹配: {is_exact}")
                
                # 如果没有backupUrl且完全匹配，测试第二个方案
                if not result['has_backup_url'] and is_exact and result.get('mix_song_id'):
                    print(f"     测试第二个方案（替代API）...")
                    alternative_url = kugou._try_alternative_api(
                        result['mix_song_id'],
                        result['song_name'],
                        result['artist_name']
                    )
                    if alternative_url:
                        print(f"     ✅ 第二个方案成功: {alternative_url[:100]}...")
                    else:
                        print(f"     ⚠️ 第二个方案未找到链接")
                print()
            
            # 尝试下载（仅测试获取URL，不实际下载文件）
            print("尝试获取下载链接...")
            try:
                name, file_path = kugou.download_music(song)
                print(f"✅ 成功: {name}")
                print(f"   文件路径: {file_path}")
                
                # 检查文件是否存在且大小合理
                if os.path.exists(file_path):
                    file_size = os.path.getsize(file_path)
                    print(f"   文件大小: {file_size / 1024 / 1024:.2f} MB")
                    if file_size < 1024 * 100:  # 小于100KB可能是错误文件
                        print(f"   ⚠️ 文件大小异常，可能下载失败")
                else:
                    print(f"   ❌ 文件不存在")
                    
            except Exception as e:
                print(f"❌ 下载失败: {e}")
                
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            import traceback
            traceback.print_exc()

def test_exact_match():
    """测试完全匹配判断功能"""
    print("\n" + "=" * 80)
    print("测试完全匹配判断功能")
    print("=" * 80)
    
    kugou = Kugou_music()
    
    test_cases = [
        # (搜索词, 歌名, 歌手, 期望结果)
        ("周杰伦 - 稻香", "稻香", "周杰伦", True),
        ("周杰伦-稻香", "稻香", "周杰伦", True),
        ("稻香", "稻香", "周杰伦", True),  # 仅歌名匹配
        ("蓝光乐队 - 轻舟", "轻舟(dj阿卓版)", "蓝光乐队", True),  # 括号处理
        ("张杰 - 轻舟", "轻舟", "蓝光乐队", False),  # 歌手不匹配
        ("轻舟 蓝光乐队", "轻舟", "蓝光乐队", True),  # 空格分隔
    ]
    
    for i, (search, song, artist, expected) in enumerate(test_cases, 1):
        result = kugou._is_exact_match(search, song, artist)
        status = "✅" if result == expected else "❌"
        print(f"{i}. {status} 搜索: '{search}' | 歌曲: '{song}' - '{artist}' | 结果: {result} (期望: {expected})")

if __name__ == "__main__":
    # 测试完全匹配判断
    test_exact_match()
    
    # 测试搜索和下载功能
    test_search_with_mixsong()
    
    print("\n" + "=" * 80)
    print("测试完成")
    print("=" * 80)
