from flask import Flask
from controller.config.cmd_script import Restart
from controller.config.config_controller import WriteYml, ReadYml
from controller.memory.memory_controller import MemorySearch, MemoryResetInputdata
from controller.userinfo.userinfo_controller import <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ScoreRecord
from controller.livebroadcast.live_controller import Cha<PERSON>, <PERSON>tReply, OperScore, SongList, HttpRecharge, HttpCmd, HttpSay, HttpEmote, HttpScene, HttpSing, HttpDraw, Msg

def bind_urls(app):
    """绑定所有URL路由"""
    print("绑定所有URL路由----------")
    # 聊天相关路由
    app.add_url_rule('/chat', view_func=Chat.as_view('chat'), methods=['GET', 'POST'])
    app.add_url_rule('/chatlist', view_func=ChatList.as_view('chatlist'), methods=['GET'])
    app.add_url_rule('/chatreply', view_func=ChatReply.as_view('chatreply'), methods=['POST'])
    
    # 命令相关路由
    app.add_url_rule('/cmd', view_func=HttpCmd.as_view('cmd'), methods=['POST'])
    app.add_url_rule('/say', view_func=HttpSay.as_view('say'), methods=['POST'])
    app.add_url_rule('/msg', view_func=Msg.as_view('msg'), methods=['POST'])
    app.add_url_rule('/emote', view_func=HttpEmote.as_view('emote'), methods=['POST'])
    
    # 场景相关路由
    app.add_url_rule('/http_scene', view_func=HttpScene.as_view('http_scene'), methods=['POST'])
    app.add_url_rule('/http_sing', view_func=HttpSing.as_view('http_sing'), methods=['POST'])
    app.add_url_rule('/http_draw', view_func=HttpDraw.as_view('http_draw'), methods=['POST'])
    
    # 配置相关路由
    app.add_url_rule('/oper/write_yml', view_func=WriteYml.as_view('write_yml'), methods=['POST'])
    app.add_url_rule('/oper/read_yml', view_func=ReadYml.as_view('read_yml'), methods=['GET'])
    
    # 内存相关路由
    app.add_url_rule('/memory_search', view_func=MemorySearch.as_view('memory_search'), methods=['POST'])
    app.add_url_rule('/memory_reset_inputdata', view_func=MemoryResetInputdata.as_view('memory_reset_inputdata'), methods=['POST'])
    
    # 用户相关路由
    app.add_url_rule('/userlist', view_func=UserList.as_view('userlist'), methods=['GET'])
    app.add_url_rule('/scorerecord', view_func=ScoreRecord.as_view('scorerecord'), methods=['GET'])
    app.add_url_rule('/songlist', view_func=SongList.as_view('songlist'), methods=['GET'])
    app.add_url_rule('/recharge', view_func=HttpRecharge.as_view('recharge'), methods=['POST'])
    
    # 重启路由
    app.add_url_rule('/restart', view_func=Restart.as_view('restart'), methods=['POST'])