J:\ai\�������\ai-yinmei\func\llm\fastgpt.c     fastgpt.py  chat        fastgpt.FastGpt.chat    get_skill       fastgpt.FastGpt.get_skill       fastgpt Interpreter change detected - this module can only be loaded into one interpreter per process.  name    __loader__  loader      __file__    origin      __package__ parent      __path__        submodule_search_locations      Module 'fastgpt' has already been imported. Re-initialisation is not supported. builtins        cython_runtime  __builtins__    init fastgpt    exactly s       %.200s() takes %.8s %zd positional argument%.1s (%zd given)     %s() got multiple values for keyword argument '%U'      %.200s() keywords must be strings       %s() got an unexpected keyword argument '%U'     while calling a Python object  NULL result without error in PyObject_Call      strings are too large to concat name '%U' is not defined        join() result is too long for a Python string   cannot import name %S   _cython_3_0_11  Shared Cython type %.200s is not a type object  Shared Cython type %.200s has the wrong size, try recompiling   keywords must be strings        __name__ must be set to a string object __qualname__ must be set to a string object     function's dictionary may not be deleted        setting function's dictionary to a non-dict     __defaults__ must be set to a tuple object      changes to cyfunction.__defaults__ will not currently affect the values used in function calls  __kwdefaults__ must be set to a dict object     changes to cyfunction.__kwdefaults__ will not currently affect the values used in function calls        __annotations__ must be set to a dict object    func_doc        __doc__ func_name       __name__        __qualname__    func_dict       __dict__        func_globals    __globals__     func_closure    __closure__     func_code       __code__        func_defaults   __defaults__    __kwdefaults__  __annotations__ _is_coroutine   __module__      __reduce__      Bad call flags for CyFunction   <cyfunction %U at %p>   %.200s() takes no arguments (%zd given) %.200s() takes exactly one argument (%zd given) %.200s() takes no keyword arguments     unbound method %.200S() needs an argument       %.200s() needs an argument      _cython_3_0_11.cython_function_or_method        %s (%s:%d)      does not match          compile time Python version %d.%d of module '%.100s' %s runtime version %d.%d   requests        "至少一个词汇来进行回复，把以上的词汇结合你的想象力说成一句有趣的话，不过使用的词汇需要符合说话逻辑。 headers info    exception               用户叫你画画，图画标题请用《》标注，图画详细描述请用括号()一整段框起来，给用户详细描述画画的内容吧。   cline_in_traceback      get_skill       func.tools.singleton_mode   role        �� �   character       __prepare__ time    ;
  CommonData      defaultConfig   func.gobal.data 【 verify      __main__    chatId      __qualname__    fastgpt func.config.default_config      __module__      relation    self        username    skill       __dict__        h� �   _initializing   FastGpt.get_skill       Ai_Name str     dataIds .       FastGpt detail  _is_coroutine   super   __spec__        describeStr post        application/json    intent  user        __import__  config  log __doc__ content __set_name__    response    、 uid     func.log.default_log    __name__                用户叫你唱歌，歌曲名:请用《》标注，给用户列出好听的歌曲名称吧。如果用户有指定歌曲名称，请不要再列出其他歌曲名称。       DefaultLog      __init_subclass__       chat_version    Content-Type    get_config  画画  *   chat    __annotations__ 跳舞  history timeout data    __test__        variables       fastgpt_url json        messages        】信息回复异常   J:\ai\吟美打包\ai-yinmei\func\llm\fastgpt.py    e   commonData      Authorization   llm     用户叫你跳舞，舞蹈名称:请用《》标注。 ?   stream      __metaclass__   getLogger       用户和你聊天。请保证每次回复用户的内容都不同。   historyStr      fastgpt_authorization   唱歌  asyncio.coroutines      describe    s   我听不懂你说什么        singleton       请你根据问题的内容，使用以下"     emotion FastGpt.chat