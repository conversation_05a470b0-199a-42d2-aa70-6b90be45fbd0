import requests
import json
from func.tools.singleton_mode import singleton
from func.gobal.data import MemoryData
from func.log.default_log import DefaultLog

@singleton
class ChromaServer:
    def __init__(self, server_url):
        """
        初始化ChromaServer
        :param server_url: 服务器URL
        """
        self.server_url = server_url
        self.log = DefaultLog().getLogger()
        self.memoryData = MemoryData()

    def memory_add_batch(self, data):
        """
        批量插入记忆数据
        :param data: 要插入的数据
        :return: 响应结果
        """
        try:
            response = requests.post(
                f"{self.server_url}/memory_add_batch",
                json=data,
                timeout=30
            )
            return response.json()
        except Exception as e:
            print(e)
            self.log.error(f"【memory_add_batch异常】{str(e)}")
            return None

    def memory_search(self, data):
        """
        查询记忆
        :param data: 查询参数
        :return: 查询结果
        """
        try:
            response = requests.post(
                f"{self.server_url}/memory_search",
                json=data,
                timeout=30
            )
            return response.json()
        except Exception as e:
            print(e)
            self.log.error(f"【memory_search异常】{str(e)}")
            return None

    def memory_reset(self):
        """
        清洗全部记忆
        :return: 操作结果
        """
        try:
            response = requests.post(
                f"{self.server_url}/memory_reset",
                timeout=30
            )
            return response.json()
        except Exception as e:
            print(e)
            self.log.error(f"【memory_reset异常】{str(e)}")
            return None