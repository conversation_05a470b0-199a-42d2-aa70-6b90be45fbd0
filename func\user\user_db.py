import re

from flask import jsonify
from func.database.mongodb import Mongodb, MongodbData
from func.tools.singleton_mode import singleton
from func.log.default_log import DefaultLog

log = DefaultLog().getLogger()

@singleton
class UserDB:
    def __init__(self):
        self.db = Mongodb().get_db()
        self.mongodbData= MongodbData()
        self.users_list = self.db['users_list']
        print("UserDB--------__init__--", self.users_list)
        
    def find_user(self, username):
        """
        查询单个用户
        :param username: 用户名
        :return: 用户信息
        """
        query = {"userName": re.compile(username, re.IGNORECASE)}
        try:
            userinfo = self.users_list.find_one(query)
        except Exception as e:
            print("UserDB--------find_user--", e)
            return None

        return userinfo

    def find_user_list_page(self, username="", page_number=1, page_size=10):
        """
        用户列表查询
        :param username: 用户名
        :param page_number: 页码
        :param page_size: 每页数量
        :return: 分页后的用户列表数据
        """
        skip_count = (page_number - 1) * page_size
        cursor = self.users_list.find(
            #{"userName": {"$regex": username}}#, "$options": "i"
        ).sort("submitTime", -1).skip(skip_count).limit(page_size)

        total_documents = self.users_list.count_documents(
            {"userName": {"$regex": username, "$options": "i"}}
        )
        
        total_pages = (total_documents + page_size - 1) // page_size

        return {
            "status": True,
            "total_documents": total_documents,
            "total_pages": total_pages,
            "current_page": page_number,
            "data": list(cursor)
        }