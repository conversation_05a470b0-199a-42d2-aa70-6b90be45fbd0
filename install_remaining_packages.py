#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
安装剩余的重要音乐转换依赖
"""
import subprocess
import sys
import os

# 目标环境路径
TARGET_ENV = r"H:/baidudown/AI-YinMei-v2.0.0/MB7-YM2-runtime"
PYTHON_EXE = os.path.join(TARGET_ENV, "python.exe")

def install_package(package, extra_args=None):
    """安装单个包"""
    print(f"📦 安装 {package}")
    
    cmd = [PYTHON_EXE, "-m", "pip", "install", package]
    if extra_args:
        cmd.extend(extra_args)
    
    try:
        result = subprocess.run(cmd, check=False, text=True, capture_output=True)
        if result.returncode == 0:
            print(f"✅ {package} 安装成功")
            return True
        else:
            print(f"❌ {package} 安装失败: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ {package} 安装异常: {e}")
        return False

def main():
    """主函数"""
    print("=== 安装剩余的重要音乐转换依赖 ===")
    
    # 重要的缺失包
    important_packages = [
        # 音频处理相关
        ("pedalboard", None),
        ("pyrubberband", None),
        ("pyworld", None),
        ("torchcrepe", None),
        ("demucs", None),
        
        # 机器学习相关
        ("fairseq", ["--no-deps"]),  # 不安装依赖，避免冲突
        ("pytorch_lightning", None),
        ("torchmetrics", None),
        ("auraloss", None),
        
        # ONNX相关
        ("onnx2pytorch", None),
        ("onnxsim", None),
        ("onnxoptimizer", None),
        
        # 其他工具
        ("diffq", None),
        ("julius", None),
        ("kthread", None),
        ("matchering", None),
        ("ml_collections", None),
        ("pynvml", None),
        ("scikit_maad", None),
        ("segmentation_models_pytorch", None),
        ("timm", None),
        ("audiomentations", None),
        ("beartype", None),
        ("rotary_embedding_torch", None),
        ("spafe", None),
        ("protobuf", None),
        ("torch_audiomentations", None),
        ("asteroid", None),
        ("praat-parselmouth", None),
    ]
    
    success_count = 0
    failed_packages = []
    
    for package, extra_args in important_packages:
        if install_package(package, extra_args):
            success_count += 1
        else:
            failed_packages.append(package)
    
    print(f"\n=== 安装结果 ===")
    print(f"✅ 成功: {success_count}/{len(important_packages)}")
    print(f"❌ 失败: {len(failed_packages)}")
    
    if failed_packages:
        print(f"\n失败的包:")
        for pkg in failed_packages:
            print(f"  - {pkg}")
    
    # 尝试安装PyAudio（Windows特殊处理）
    print(f"\n🔧 尝试安装PyAudio...")
    if not install_package("PyAudio"):
        print("尝试使用pipwin安装PyAudio...")
        if install_package("pipwin"):
            cmd = [PYTHON_EXE, "-m", "pipwin", "install", "pyaudio"]
            try:
                result = subprocess.run(cmd, check=False, text=True)
                if result.returncode == 0:
                    print("✅ PyAudio 通过pipwin安装成功")
                else:
                    print("❌ PyAudio 通过pipwin安装失败")
            except:
                print("❌ pipwin命令执行失败")

if __name__ == "__main__":
    main()
