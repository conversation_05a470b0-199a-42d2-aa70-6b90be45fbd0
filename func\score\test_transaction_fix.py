#!/usr/bin/env python3
"""
测试事务修复
验证积分系统在不支持事务的MongoDB环境下能正常工作
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../..'))

def test_transaction_support_detection():
    """测试事务支持检测"""
    print("=" * 50)
    print("测试事务支持检测")
    print("=" * 50)

    try:
        from func.score.score_db import ScoreDB

        # 创建积分系统实例
        score_db = ScoreDB()

        # 测试事务支持检测方法
        supports_transactions = score_db._supports_transactions()
        print(f"数据库支持事务: {'是' if supports_transactions else '否'}")

        # 测试模块化系统的事务支持检测
        module_supports = score_db.score_operations._supports_transactions()
        print(f"模块化系统支持事务: {'是' if module_supports else '否'}")

        return True

    except Exception as e:
        print(f"❌ 事务支持检测失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_score_operation_fallback():
    """测试积分操作回退机制"""
    print("\n" + "=" * 50)
    print("测试积分操作回退机制")
    print("=" * 50)

    try:
        from func.score.score_db import ScoreDB

        # 创建积分系统实例
        score_db = ScoreDB()

        # 测试用户
        test_openid = "test_transaction_user"
        test_username = "事务测试用户"

        # 先确保用户存在
        user_info = score_db.user_refresh(test_openid, test_username, "http://example.com/avatar.jpg")
        if user_info:
            print(f"✅ 用户创建/更新成功: {test_username}")

            # 测试积分操作（会自动检测事务支持并回退）
            success = score_db.oper_score(test_openid, test_username, 10, "事务测试")
            print(f"积分操作结果: {'✅ 成功' if success else '❌ 失败'}")

            if success:
                # 验证积分是否正确更新
                updated_user = score_db.get_score(test_openid)
                if updated_user:
                    print(f"✅ 积分更新成功，当前积分: {updated_user.get('score', 0)}")
                else:
                    print("❌ 无法获取更新后的用户信息")

            return success
        else:
            print("❌ 用户创建失败")
            return False

    except Exception as e:
        print(f"❌ 积分操作测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_modular_system_fallback():
    """测试模块化系统的回退机制"""
    print("\n" + "=" * 50)
    print("测试模块化系统回退机制")
    print("=" * 50)

    try:
        from func.score.score_db import ScoreDB

        # 创建积分系统实例
        score_db = ScoreDB()

        # 测试用户
        test_openid = "test_modular_user"
        test_username = "模块化测试用户"

        # 先确保用户存在
        user_info = score_db.user_refresh(test_openid, test_username, "http://example.com/avatar.jpg")
        if user_info:
            print(f"✅ 用户创建/更新成功: {test_username}")

            # 直接使用模块化系统进行积分操作
            success = score_db.score_operations.operate_score(
                test_openid, test_username, 15, "模块化测试", use_transaction=True
            )
            print(f"模块化积分操作结果: {'✅ 成功' if success else '❌ 失败'}")

            if success:
                # 验证积分是否正确更新
                updated_user = score_db.get_score(test_openid)
                if updated_user:
                    print(f"✅ 积分更新成功，当前积分: {updated_user.get('score', 0)}")
                else:
                    print("❌ 无法获取更新后的用户信息")

            return success
        else:
            print("❌ 用户创建失败")
            return False

    except Exception as e:
        print(f"❌ 模块化系统测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_error_handling():
    """测试错误处理"""
    print("\n" + "=" * 50)
    print("测试错误处理")
    print("=" * 50)

    try:
        from func.score.score_db import ScoreDB

        # 创建积分系统实例
        score_db = ScoreDB()

        # 测试不存在的用户
        success = score_db.oper_score("non_existent_user", "不存在用户", 10, "错误测试")
        print(f"不存在用户操作结果: {'❌ 正确失败' if not success else '⚠️ 意外成功'}")

        # 测试无效参数
        success = score_db.oper_score("", "", 0, "")
        print(f"无效参数操作结果: {'❌ 正确失败' if not success else '⚠️ 意外成功'}")

        return True

    except Exception as e:
        print(f"❌ 错误处理测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🔧 MongoDB事务修复测试")
    print("=" * 60)

    success1 = test_transaction_support_detection()
    success2 = test_score_operation_fallback()
    success3 = test_modular_system_fallback()
    success4 = test_error_handling()

    print("\n" + "=" * 60)
    print("测试总结:")
    print(f"事务支持检测: {'✅' if success1 else '❌'}")
    print(f"积分操作回退: {'✅' if success2 else '❌'}")
    print(f"模块化系统回退: {'✅' if success3 else '❌'}")
    print(f"错误处理: {'✅' if success4 else '❌'}")

    if success1 and success2 and success3 and success4:
        print("\n🎉 所有测试通过！事务修复成功！")
        print("积分系统现在可以在单机MongoDB环境下正常工作。")
        sys.exit(0)
    else:
        print("\n❌ 部分测试失败，请检查错误信息")
        sys.exit(1)