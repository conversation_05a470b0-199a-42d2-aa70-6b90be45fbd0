"""
回复器API模块

提供回复器相关功能，采用标准Python包设计模式
使用方式：
    from src.plugin_system.apis import generator_api
    replyer = generator_api.get_replyer(chat_stream)
    success, reply_set, _ = await generator_api.generate_reply(chat_stream, action_data, reasoning)
"""

import traceback
from typing import Tuple, Any, Dict, List, Optional
from rich.traceback import install
from src.common.logger import get_logger
from src.config.api_ada_configs import TaskConfig
from src.chat.replyer.default_generator import DefaultReplyer
from src.chat.message_receive.chat_stream import ChatStream
from src.chat.utils.utils import process_llm_response
from src.chat.replyer.replyer_manager import replyer_manager
from src.plugin_system.base.component_types import ActionInfo

install(extra_lines=3)

logger = get_logger("generator_api")


# =============================================================================
# 回复器获取API函数
# =============================================================================


def get_replyer(
    chat_stream: Optional[ChatStream] = None,
    chat_id: Optional[str] = None,
    model_set_with_weight: Optional[List[Tuple[TaskConfig, float]]] = None,
    request_type: str = "replyer",
) -> Optional[DefaultReplyer]:
    """获取回复器对象

    优先使用chat_stream，如果没有则使用chat_id直接查找。
    使用 ReplyerManager 来管理实例，避免重复创建。

    Args:
        chat_stream: 聊天流对象（优先）
        chat_id: 聊天ID（实际上就是stream_id）
        model_set_with_weight: 模型配置列表，每个元素为 (TaskConfig, weight) 元组
        request_type: 请求类型

    Returns:
        Optional[DefaultReplyer]: 回复器对象，如果获取失败则返回None

    Raises:
        ValueError: chat_stream 和 chat_id 均为空
    """
    # ========== 调试信息：get_replyer 方法入口 ==========
    logger.info("-" * 60)
    logger.info("[DEBUG] get_replyer 方法开始执行")
    logger.info(f"[DEBUG] 输入参数:")
    logger.info(f"  - chat_stream: {'存在' if chat_stream else '不存在'}")
    if chat_stream:
        logger.info(f"    - stream_id: {chat_stream.stream_id}")
        logger.info(f"    - platform: {chat_stream.platform}")
        logger.info(f"    - user_info.user_nickname: {chat_stream.user_info.user_nickname if chat_stream.user_info else 'None'}")
        logger.info(f"    - group_info: {'存在' if chat_stream.group_info else '不存在'}")
        if chat_stream.group_info:
            logger.info(f"      - group_name: {chat_stream.group_info.group_name}")
    logger.info(f"  - chat_id: {chat_id}")
    logger.info(f"  - model_set_with_weight: {len(model_set_with_weight) if model_set_with_weight else 0} 个模型")
    if model_set_with_weight:
        for i, (config, weight) in enumerate(model_set_with_weight):
            logger.info(f"    [{i}] 模型: {config.model_list}, 权重: {weight}")
    logger.info(f"  - request_type: {request_type}")

    # ========== 调试信息：参数验证 ==========
    logger.info("[DEBUG] 步骤1: 参数验证")
    if not chat_id and not chat_stream:
        logger.error("[DEBUG] 参数验证失败: chat_stream 和 chat_id 均为空")
        raise ValueError("chat_stream 和 chat_id 不可均为空")
    logger.info("[DEBUG] 参数验证通过")

    try:
        logger.info(f"[GeneratorAPI] 正在获取回复器，chat_id: {chat_id}, chat_stream: {'有' if chat_stream else '无'}")

        # ========== 调试信息：调用 replyer_manager.get_replyer ==========
        logger.info("[DEBUG] 步骤2: 调用 replyer_manager.get_replyer")
        logger.info(f"[DEBUG] replyer_manager 类型: {type(replyer_manager).__name__}")

        replyer = replyer_manager.get_replyer(
            chat_stream=chat_stream,
            chat_id=chat_id,
            model_set_with_weight=model_set_with_weight,
            request_type=request_type,
        )

        # ========== 调试信息：返回结果 ==========
        logger.info("[DEBUG] 步骤3: 处理返回结果")
        if replyer:
            logger.info(f"[DEBUG] 成功获取回复器:")
            logger.info(f"  - 类型: {type(replyer).__name__}")
            logger.info(f"  - chat_stream.stream_id: {replyer.chat_stream.stream_id}")
            logger.info(f"  - model_set 数量: {len(replyer.model_set) if replyer.model_set else 0}")
            logger.info(f"  - request_type: {replyer.request_type}")
        else:
            logger.error("[DEBUG] 获取回复器失败，返回 None")

        logger.info("[DEBUG] get_replyer 方法执行完成")
        logger.info("-" * 60)
        return replyer

    except Exception as e:
        logger.error(f"[DEBUG] get_replyer 异常: {e}")
        logger.error(f"[GeneratorAPI] 获取回复器时发生意外错误: {e}", exc_info=True)
        traceback.print_exc()
        return None


# =============================================================================
# 回复生成API函数
# =============================================================================


async def generate_reply(
    chat_stream: Optional[ChatStream] = None,
    chat_id: Optional[str] = None,
    action_data: Optional[Dict[str, Any]] = None,
    reply_to: str = "",
    extra_info: str = "",
    available_actions: Optional[Dict[str, ActionInfo]] = None,
    enable_tool: bool = False,
    enable_splitter: bool = True,
    enable_chinese_typo: bool = True,
    return_prompt: bool = False,
    model_set_with_weight: Optional[List[Tuple[TaskConfig, float]]] = None,
    request_type: str = "generator_api",
) -> Tuple[bool, List[Tuple[str, Any]], Optional[str]]:
    """生成回复

    Args:
        chat_stream: 聊天流对象（优先）
        chat_id: 聊天ID（备用）
        action_data: 动作数据（向下兼容，包含reply_to和extra_info）
        reply_to: 回复对象，格式为 "发送者:消息内容"
        extra_info: 额外信息，用于补充上下文
        available_actions: 可用动作
        enable_tool: 是否启用工具调用
        enable_splitter: 是否启用消息分割器
        enable_chinese_typo: 是否启用错字生成器
        return_prompt: 是否返回提示词
        model_set_with_weight: 模型配置列表，每个元素为 (TaskConfig, weight) 元组
        request_type: 请求类型（可选，记录LLM使用）
    Returns:
        Tuple[bool, List[Tuple[str, Any]], Optional[str]]: (是否成功, 回复集合, 提示词)
    """
    # ========== 调试信息：方法入口 ==========
    logger.info("=" * 80)
    logger.info("[DEBUG] generate_reply 方法开始执行")
    logger.info(f"[DEBUG] 输入参数:")
    logger.info(f"  - chat_stream: {'存在' if chat_stream else '不存在'}")
    if chat_stream:
        logger.info(f"    - stream_id: {chat_stream.stream_id}")
        logger.info(f"    - platform: {chat_stream.platform}")
        logger.info(f"    - user_info: {chat_stream.user_info.user_nickname if chat_stream.user_info else 'None'}")
        logger.info(f"    - group_info: {'存在' if chat_stream.group_info else '不存在'}")
    logger.info(f"  - chat_id: {chat_id}")
    logger.info(f"  - action_data: {action_data}")
    logger.info(f"  - reply_to: '{reply_to}'")
    logger.info(f"  - extra_info: '{extra_info}'")
    logger.info(f"  - available_actions: {len(available_actions) if available_actions else 0} 个动作")
    logger.info(f"  - enable_tool: {enable_tool}")
    logger.info(f"  - enable_splitter: {enable_splitter}")
    logger.info(f"  - enable_chinese_typo: {enable_chinese_typo}")
    logger.info(f"  - return_prompt: {return_prompt}")
    logger.info(f"  - model_set_with_weight: {len(model_set_with_weight) if model_set_with_weight else 0} 个模型")
    logger.info(f"  - request_type: {request_type}")
    logger.info("=" * 80)

    try:
        # ========== 调试信息：获取回复器阶段 ==========
        logger.info("[DEBUG] 步骤1: 开始获取回复器")
        logger.info(f"[DEBUG] 调用 get_replyer 参数:")
        logger.info(f"  - chat_stream: {'存在' if chat_stream else '不存在'}")
        logger.info(f"  - chat_id: {chat_id}")
        logger.info(f"  - model_set_with_weight: {model_set_with_weight}")
        logger.info(f"  - request_type: {request_type}")

        replyer = get_replyer(chat_stream, chat_id, model_set_with_weight=model_set_with_weight, request_type=request_type)

        if not replyer:
            logger.error("[DEBUG] 获取回复器失败！")
            logger.error("[GeneratorAPI] 无法获取回复器")
            return False, [], None

        logger.info(f"[DEBUG] 成功获取回复器: {type(replyer).__name__}")
        logger.info(f"[DEBUG] 回复器属性:")
        logger.info(f"  - chat_stream.stream_id: {replyer.chat_stream.stream_id}")
        logger.info(f"  - model_set: {len(replyer.model_set) if replyer.model_set else 0} 个模型配置")
        logger.info(f"  - request_type: {replyer.request_type}")

        logger.info("[GeneratorAPI] 开始生成回复")

        # ========== 调试信息：参数处理阶段 ==========
        logger.info("[DEBUG] 步骤2: 处理参数")
        original_reply_to = reply_to
        original_extra_info = extra_info

        if not reply_to and action_data:
            reply_to = action_data.get("reply_to", "")
            logger.info(f"[DEBUG] 从 action_data 中获取 reply_to: '{reply_to}'")
        if not extra_info and action_data:
            extra_info = action_data.get("extra_info", "")
            logger.info(f"[DEBUG] 从 action_data 中获取 extra_info: '{extra_info}'")

        logger.info(f"[DEBUG] 最终参数:")
        logger.info(f"  - reply_to: '{original_reply_to}' -> '{reply_to}'")
        logger.info(f"  - extra_info: '{original_extra_info}' -> '{extra_info}'")

        # ========== 调试信息：调用回复器生成回复阶段 ==========
        logger.info("[DEBUG] 步骤3: 调用回复器生成回复")
        logger.info(f"[DEBUG] 调用 replyer.generate_reply_with_context 参数:")
        logger.info(f"  - reply_to: '{reply_to}'")
        logger.info(f"  - extra_info: '{extra_info}'")
        logger.info(f"  - available_actions: {len(available_actions) if available_actions else 0} 个")
        logger.info(f"  - enable_tool: {enable_tool}")

        success, content, prompt = await replyer.generate_reply_with_context(
            reply_to=reply_to,
            extra_info=extra_info,
            available_actions=available_actions,
            enable_tool=enable_tool,
        )

        logger.info(f"[DEBUG] replyer.generate_reply_with_context 返回结果:")
        logger.info(f"  - success: {success}")
        logger.info(f"  - content: '{content[:100] if content else None}{'...' if content and len(content) > 100 else ''}'")
        logger.info(f"  - prompt: {'存在' if prompt else '不存在'} ({'长度: ' + str(len(prompt)) if prompt else ''})")

        # ========== 调试信息：处理回复内容阶段 ==========
        logger.info("[DEBUG] 步骤4: 处理回复内容")
        reply_set = []
        if content:
            logger.info(f"[DEBUG] 开始处理回复内容，长度: {len(content)}")
            logger.info(f"[DEBUG] 调用 process_human_text 参数:")
            logger.info(f"  - content: '{content[:50]}{'...' if len(content) > 50 else ''}'")
            logger.info(f"  - enable_splitter: {enable_splitter}")
            logger.info(f"  - enable_chinese_typo: {enable_chinese_typo}")

            reply_set = await process_human_text(content, enable_splitter, enable_chinese_typo)

            logger.info(f"[DEBUG] process_human_text 返回结果:")
            logger.info(f"  - reply_set 长度: {len(reply_set)}")
            for i, (msg_type, msg_content) in enumerate(reply_set):
                logger.info(f"    [{i}] 类型: {msg_type}, 内容: '{msg_content[:50]}{'...' if len(msg_content) > 50 else ''}'")
        else:
            logger.info("[DEBUG] 没有内容需要处理")

        # ========== 调试信息：结果处理阶段 ==========
        logger.info("[DEBUG] 步骤5: 处理最终结果")
        if success:
            logger.info(f"[DEBUG] 回复生成成功！")
            logger.info(f"[GeneratorAPI] 回复生成成功，生成了 {len(reply_set)} 个回复项")
        else:
            logger.warning("[DEBUG] 回复生成失败！")
            logger.warning("[GeneratorAPI] 回复生成失败")

        # ========== 调试信息：返回结果 ==========
        logger.info("[DEBUG] 步骤6: 准备返回结果")
        logger.info(f"[DEBUG] 返回参数:")
        logger.info(f"  - return_prompt: {return_prompt}")
        logger.info(f"  - success: {success}")
        logger.info(f"  - reply_set 长度: {len(reply_set)}")
        logger.info(f"  - prompt: {'将返回' if return_prompt and prompt else '不返回'}")

        result_prompt = prompt if return_prompt else None
        logger.info(f"[DEBUG] generate_reply 方法执行完成")
        logger.info("=" * 80)

        return success, reply_set, result_prompt

    except ValueError as ve:
        logger.error(f"[DEBUG] ValueError 异常: {ve}")
        logger.error(traceback.format_exc())
        raise ve

    except Exception as e:
        logger.error(f"[DEBUG] 未预期异常: {e}")
        logger.error(f"[GeneratorAPI] 生成回复时出错: {e}")
        logger.error(traceback.format_exc())
        return False, [], None


async def rewrite_reply(
    chat_stream: Optional[ChatStream] = None,
    reply_data: Optional[Dict[str, Any]] = None,
    chat_id: Optional[str] = None,
    enable_splitter: bool = True,
    enable_chinese_typo: bool = True,
    model_set_with_weight: Optional[List[Tuple[TaskConfig, float]]] = None,
    raw_reply: str = "",
    reason: str = "",
    reply_to: str = "",
    return_prompt: bool = False,
) -> Tuple[bool, List[Tuple[str, Any]], Optional[str]]:
    """重写回复

    Args:
        chat_stream: 聊天流对象（优先）
        reply_data: 回复数据字典（向下兼容备用，当其他参数缺失时从此获取）
        chat_id: 聊天ID（备用）
        enable_splitter: 是否启用消息分割器
        enable_chinese_typo: 是否启用错字生成器
        model_set_with_weight: 模型配置列表，每个元素为 (TaskConfig, weight) 元组
        raw_reply: 原始回复内容
        reason: 回复原因
        reply_to: 回复对象
        return_prompt: 是否返回提示词

    Returns:
        Tuple[bool, List[Tuple[str, Any]]]: (是否成功, 回复集合)
    """
    try:
        # 获取回复器
        replyer = get_replyer(chat_stream, chat_id, model_set_with_weight=model_set_with_weight)
        if not replyer:
            logger.error("[GeneratorAPI] 无法获取回复器")
            return False, [], None

        logger.info("[GeneratorAPI] 开始重写回复")

        # 如果参数缺失，从reply_data中获取
        if reply_data:
            raw_reply = raw_reply or reply_data.get("raw_reply", "")
            reason = reason or reply_data.get("reason", "")
            reply_to = reply_to or reply_data.get("reply_to", "")

        # 调用回复器重写回复
        success, content, prompt = await replyer.rewrite_reply_with_context(
            raw_reply=raw_reply,
            reason=reason,
            reply_to=reply_to,
            return_prompt=return_prompt,
        )
        reply_set = []
        if content:
            reply_set = await process_human_text(content, enable_splitter, enable_chinese_typo)

        if success:
            logger.info(f"[GeneratorAPI] 重写回复成功，生成了 {len(reply_set)} 个回复项")
        else:
            logger.warning("[GeneratorAPI] 重写回复失败")

        return success, reply_set, prompt if return_prompt else None

    except ValueError as ve:
        raise ve

    except Exception as e:
        logger.error(f"[GeneratorAPI] 重写回复时出错: {e}")
        return False, [], None


async def process_human_text(content: str, enable_splitter: bool, enable_chinese_typo: bool) -> List[Tuple[str, Any]]:
    """将文本处理为更拟人化的文本

    Args:
        content: 文本内容
        enable_splitter: 是否启用消息分割器
        enable_chinese_typo: 是否启用错字生成器
    """
    # ========== 调试信息：process_human_text 方法入口 ==========
    logger.info("~" * 50)
    logger.info("[DEBUG] process_human_text 方法开始执行")
    logger.info(f"[DEBUG] 输入参数:")
    logger.info(f"  - content: '{content[:100]}{'...' if len(content) > 100 else ''}' (长度: {len(content)})")
    logger.info(f"  - enable_splitter: {enable_splitter}")
    logger.info(f"  - enable_chinese_typo: {enable_chinese_typo}")

    # ========== 调试信息：参数验证 ==========
    logger.info("[DEBUG] 步骤1: 参数验证")
    if not isinstance(content, str):
        logger.error(f"[DEBUG] 参数类型错误: content 类型为 {type(content)}, 期望 str")
        raise ValueError("content 必须是字符串类型")
    logger.info("[DEBUG] 参数验证通过")

    try:
        # ========== 调试信息：调用 process_llm_response ==========
        logger.info("[DEBUG] 步骤2: 调用 process_llm_response")
        logger.info(f"[DEBUG] 调用参数:")
        logger.info(f"  - text: '{content[:50]}{'...' if len(content) > 50 else ''}'")
        logger.info(f"  - enable_splitter: {enable_splitter}")
        logger.info(f"  - enable_chinese_typo: {enable_chinese_typo}")

        processed_response = process_llm_response(content, enable_splitter, enable_chinese_typo)

        logger.info(f"[DEBUG] process_llm_response 返回结果:")
        logger.info(f"  - 类型: {type(processed_response)}")
        logger.info(f"  - 长度: {len(processed_response) if processed_response else 0}")
        if processed_response:
            for i, text in enumerate(processed_response):
                logger.info(f"    [{i}] '{text[:50]}{'...' if len(text) > 50 else ''}' (长度: {len(text)})")

        # ========== 调试信息：构建回复集合 ==========
        logger.info("[DEBUG] 步骤3: 构建回复集合")
        reply_set = []
        for i, text in enumerate(processed_response):
            reply_seg = ("text", text)
            reply_set.append(reply_seg)
            logger.info(f"[DEBUG] 添加回复项 [{i}]: 类型='text', 内容='{text[:30]}{'...' if len(text) > 30 else ''}'")

        logger.info(f"[DEBUG] 最终回复集合:")
        logger.info(f"  - 总数: {len(reply_set)}")
        logger.info(f"  - 总字符数: {sum(len(text) for _, text in reply_set)}")

        logger.info("[DEBUG] process_human_text 方法执行完成")
        logger.info("~" * 50)
        return reply_set

    except Exception as e:
        logger.error(f"[DEBUG] process_human_text 异常: {e}")
        logger.error(f"[GeneratorAPI] 处理人形文本时出错: {e}")
        logger.error(traceback.format_exc())
        return []

async def generate_response_custom(
    chat_stream: Optional[ChatStream] = None,
    chat_id: Optional[str] = None,
    model_set_with_weight: Optional[List[Tuple[TaskConfig, float]]] = None,
    prompt: str = "",
) -> Optional[str]:
    replyer = get_replyer(chat_stream, chat_id, model_set_with_weight=model_set_with_weight)
    if not replyer:
        logger.error("[GeneratorAPI] 无法获取回复器")
        return None

    try:
        logger.info("[GeneratorAPI] 开始生成自定义回复")
        response, _, _, _ = await replyer.llm_generate_content(prompt)
        if response:
            logger.info("[GeneratorAPI] 自定义回复生成成功")
            return response
        else:
            logger.warning("[GeneratorAPI] 自定义回复生成失败")
            return None
    except Exception as e:
        logger.error(f"[GeneratorAPI] 生成自定义回复时出错: {e}")
        return None