#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MMD连接调试工具
用于快速检查MMD服务端状态和连接问题
"""

import asyncio
import socket
import json
from pathlib import Path
from func.dance.websocket_client import MMDWebSocketClient

async def check_tcp_port(host, port, timeout=3):
    """检查TCP端口是否可用"""
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(timeout)
        result = sock.connect_ex((host, port))
        sock.close()
        return result == 0
    except Exception as e:
        return False

async def test_websocket_connection(host, port, timeout=5):
    """测试WebSocket连接"""
    try:
        import websockets
        uri = f"ws://{host}:{port}"
        
        print(f"🔍 测试WebSocket连接: {uri}")
        
        # 尝试连接
        websocket = await asyncio.wait_for(
            websockets.connect(uri), 
            timeout=timeout
        )
        
        print(f"✅ WebSocket连接成功")
        
        # 尝试发送简单消息
        test_message = {
            "type": "get_status",
            "data": {}
        }
        
        await websocket.send(json.dumps(test_message))
        print(f"📤 发送测试消息成功")
        
        # 尝试接收响应
        try:
            response = await asyncio.wait_for(
                websocket.recv(), 
                timeout=timeout
            )
            print(f"📥 收到响应: {len(response)} 字符")
            
            # 尝试解析响应
            try:
                data = json.loads(response)
                print(f"✅ 响应解析成功: {list(data.keys())}")
                return True, data
            except json.JSONDecodeError as e:
                print(f"⚠️ 响应解析失败: {e}")
                return True, {"raw_response": response}
                
        except asyncio.TimeoutError:
            print(f"❌ 接收响应超时 ({timeout}秒)")
            return False, {"error": "响应超时"}
        
    except asyncio.TimeoutError:
        print(f"❌ 连接超时 ({timeout}秒)")
        return False, {"error": "连接超时"}
    except Exception as e:
        print(f"❌ WebSocket连接失败: {e}")
        return False, {"error": str(e)}
    finally:
        try:
            await websocket.close()
        except:
            pass

async def check_port_file():
    """检查端口状态文件"""
    port_file = Path("mmd_server_port.txt")
    
    print(f"📄 检查端口文件: {port_file}")
    
    if port_file.exists():
        try:
            content = port_file.read_text().strip()
            print(f"✅ 端口文件存在: {content}")
            
            if ':' in content:
                host, port = content.split(':', 1)
                return host, int(port)
            else:
                print(f"⚠️ 端口文件格式错误: {content}")
                return None, None
                
        except Exception as e:
            print(f"❌ 读取端口文件失败: {e}")
            return None, None
    else:
        print(f"❌ 端口文件不存在")
        return None, None

async def scan_ports_range(host="localhost", start_port=8765, end_port=8775):
    """扫描端口范围"""
    print(f"🔍 扫描端口范围: {host}:{start_port}-{end_port}")
    
    open_ports = []
    for port in range(start_port, end_port):
        if await check_tcp_port(host, port, timeout=1):
            print(f"   ✅ 端口 {port}: 开放")
            open_ports.append(port)
        else:
            print(f"   ❌ 端口 {port}: 关闭")
    
    return open_ports

async def test_mmd_client():
    """测试MMD客户端功能"""
    print(f"🎭 测试MMD客户端...")
    
    client = MMDWebSocketClient()
    
    # 测试连接
    success = await client.connect(show_messages=True)
    if not success:
        print(f"❌ 客户端连接失败")
        return False
    
    print(f"✅ 客户端连接成功")
    
    # 测试基本请求
    try:
        print(f"🔍 测试状态请求...")
        status = await client.get_status()
        print(f"✅ 状态请求成功: {status.get('status', {}).get('server_status', '未知')}")
        
        print(f"🔍 测试舞蹈搜索...")
        search_result = await client.search_dances("test", "both")
        print(f"✅ 搜索请求成功: 找到 {len(search_result.get('dances', []))} 个舞蹈")
        
        return True
        
    except Exception as e:
        print(f"❌ 客户端测试失败: {e}")
        return False
    
    finally:
        await client.disconnect()

async def main():
    """主诊断流程"""
    print("🔧 MMD连接诊断工具")
    print("=" * 50)
    
    # 1. 检查端口文件
    host, port = await check_port_file()
    
    # 2. 如果没有端口文件，扫描常用端口
    if not host or not port:
        print(f"\n🔍 端口扫描...")
        open_ports = await scan_ports_range()
        
        if open_ports:
            host = "localhost"
            port = open_ports[0]  # 使用第一个开放端口
            print(f"🎯 选择端口: {port}")
        else:
            print(f"❌ 未发现任何开放端口，服务端可能未启动")
            return
    
    # 3. 测试TCP连接
    print(f"\n🔍 TCP连接测试...")
    tcp_ok = await check_tcp_port(host, port)
    if tcp_ok:
        print(f"✅ TCP端口 {host}:{port} 可达")
    else:
        print(f"❌ TCP端口 {host}:{port} 不可达")
        return
    
    # 4. 测试WebSocket连接
    print(f"\n🔍 WebSocket连接测试...")
    ws_ok, ws_data = await test_websocket_connection(host, port)
    if ws_ok:
        print(f"✅ WebSocket连接正常")
        if "error" not in ws_data:
            print(f"📊 服务端状态: {ws_data}")
    else:
        print(f"❌ WebSocket连接异常: {ws_data}")
        return
    
    # 5. 测试完整客户端
    print(f"\n🔍 完整客户端测试...")
    client_ok = await test_mmd_client()
    if client_ok:
        print(f"✅ 客户端功能正常")
    else:
        print(f"❌ 客户端功能异常")
    
    print(f"\n🎉 诊断完成！")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print(f"\n👋 诊断被中断")
    except Exception as e:
        print(f"\n❌ 诊断过程出错: {e}")
        import traceback
        traceback.print_exc() 