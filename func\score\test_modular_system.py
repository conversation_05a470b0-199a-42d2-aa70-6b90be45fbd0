#!/usr/bin/env python3
"""
积分系统模块化测试
测试新的模块化积分系统的各项功能
"""

import sys
import os
import unittest
from unittest.mock import Mock, patch
import time

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../..'))

from func.score.score_db import ScoreDB
from func.score.modules.user_manager import UserManager
from func.score.modules.score_operations import ScoreOperations
from func.score.core.database_manager import DatabaseManager


class TestModularScoreSystem(unittest.TestCase):
    """测试模块化积分系统"""

    def setUp(self):
        """测试前准备"""
        self.score_db = ScoreDB()

    def test_score_db_initialization(self):
        """测试积分系统初始化"""
        self.assertIsNotNone(self.score_db)
        self.assertIsNotNone(self.score_db.db_manager)
        self.assertIsNotNone(self.score_db.user_manager)
        self.assertIsNotNone(self.score_db.score_operations)

        # 测试向后兼容性
        self.assertIsNotNone(self.score_db.db)
        self.assertIsNotNone(self.score_db.score_col)
        self.assertIsNotNone(self.score_db.score_record_col)

    def test_user_manager_functionality(self):
        """测试用户管理器功能"""
        user_manager = self.score_db.user_manager

        # 测试用户管理器是否正确初始化
        self.assertIsInstance(user_manager, UserManager)

        # 测试方法是否存在
        self.assertTrue(hasattr(user_manager, 'create_user'))
        self.assertTrue(hasattr(user_manager, 'update_user'))
        self.assertTrue(hasattr(user_manager, 'get_user'))
        self.assertTrue(hasattr(user_manager, 'search_users'))
        self.assertTrue(hasattr(user_manager, 'delete_user'))

    def test_score_operations_functionality(self):
        """测试积分操作功能"""
        score_ops = self.score_db.score_operations

        # 测试积分操作器是否正确初始化
        self.assertIsInstance(score_ops, ScoreOperations)

        # 测试方法是否存在
        self.assertTrue(hasattr(score_ops, 'operate_score'))
        self.assertTrue(hasattr(score_ops, 'batch_update_scores'))
        self.assertTrue(hasattr(score_ops, 'check_score_sufficient'))
        self.assertTrue(hasattr(score_ops, 'get_user_score'))

    def test_database_manager_functionality(self):
        """测试数据库管理器功能"""
        db_manager = self.score_db.db_manager

        # 测试数据库管理器是否正确初始化
        self.assertIsInstance(db_manager, DatabaseManager)

        # 测试方法是否存在
        self.assertTrue(hasattr(db_manager, 'health_check'))
        self.assertTrue(hasattr(db_manager, 'find_one_common'))
        self.assertTrue(hasattr(db_manager, 'find_common'))
        self.assertTrue(hasattr(db_manager, 'aggregate_common'))

    def test_backward_compatibility(self):
        """测试向后兼容性"""
        # 测试原有API方法是否仍然存在
        self.assertTrue(hasattr(self.score_db, 'get_score'))
        self.assertTrue(hasattr(self.score_db, 'oper_score'))
        self.assertTrue(hasattr(self.score_db, 'user_refresh'))
        self.assertTrue(hasattr(self.score_db, 'get_reg_userinfo'))
        self.assertTrue(hasattr(self.score_db, 'new_user_insert'))
        self.assertTrue(hasattr(self.score_db, 'search_users'))

    @patch('func.score.core.database_manager.Mongodb')
    def test_mock_user_operations(self, mock_mongodb):
        """测试用户操作（使用模拟数据库）"""
        # 模拟数据库返回
        mock_collection = Mock()
        mock_db = Mock()
        mock_db.__getitem__ = Mock(return_value=mock_collection)
        mock_mongodb.return_value.get_db.return_value = mock_db

        # 创建新的积分系统实例
        score_db = ScoreDB()

        # 模拟用户数据
        mock_user_data = {
            "openId": "test_user_001",
            "userName": "测试用户",
            "userface": "http://example.com/avatar.jpg",
            "score": 100,
            "createTime": "2024-01-01 12:00:00",
            "updateTime": "2024-01-01 12:00:00"
        }

        mock_collection.find_one.return_value = mock_user_data

        # 测试获取用户积分
        result = score_db.get_score("test_user_001")
        self.assertEqual(result, mock_user_data)

    def test_performance_metrics(self):
        """测试性能指标"""
        start_time = time.time()

        # 执行一些基本操作
        score_db = ScoreDB()

        # 测试初始化时间
        init_time = time.time() - start_time
        self.assertLess(init_time, 5.0, "积分系统初始化时间应该少于5秒")

        # 测试方法调用时间
        start_time = time.time()
        try:
            # 这些调用可能会失败，但我们主要测试性能
            score_db.get_score("non_existent_user")
        except:
            pass
        call_time = time.time() - start_time
        self.assertLess(call_time, 1.0, "方法调用时间应该少于1秒")

    def test_error_handling(self):
        """测试错误处理"""
        # 测试无效参数
        result = self.score_db.get_score("")
        self.assertIsNone(result)

        result = self.score_db.get_score(None)
        self.assertIsNone(result)

        # 测试无效的积分操作
        result = self.score_db.oper_score("", "test", 10, "test")
        self.assertFalse(result)

        result = self.score_db.oper_score("test", "", 10, "test")
        self.assertFalse(result)

    def test_module_integration(self):
        """测试模块集成"""
        # 测试主控制器是否正确委托给子模块
        user_manager = self.score_db.user_manager
        score_operations = self.score_db.score_operations

        # 验证子模块都使用同一个数据库管理器
        self.assertEqual(user_manager.db_manager, self.score_db.db_manager)
        self.assertEqual(score_operations.db_manager, self.score_db.db_manager)

    def test_cache_functionality(self):
        """测试缓存功能"""
        cache_manager = self.score_db.cache_manager

        # 测试缓存管理器是否存在
        self.assertIsNotNone(cache_manager)

        # 测试缓存方法是否存在
        self.assertTrue(hasattr(cache_manager, 'get'))
        self.assertTrue(hasattr(cache_manager, 'set'))
        self.assertTrue(hasattr(cache_manager, 'delete'))

    def test_metrics_functionality(self):
        """测试指标收集功能"""
        metrics = self.score_db.metrics

        # 测试指标收集器是否存在
        self.assertIsNotNone(metrics)

        # 测试指标方法是否存在
        self.assertTrue(hasattr(metrics, 'record_operation'))
        self.assertTrue(hasattr(metrics, 'get_basic_stats'))
        self.assertTrue(hasattr(metrics, 'get_all_metrics'))


class TestSystemIntegration(unittest.TestCase):
    """测试系统集成"""

    def test_singleton_pattern(self):
        """测试单例模式"""
        score_db1 = ScoreDB()
        score_db2 = ScoreDB()

        # 验证单例模式
        self.assertEqual(id(score_db1), id(score_db2))

    def test_module_dependencies(self):
        """测试模块依赖"""
        score_db = ScoreDB()

        # 验证所有模块都正确初始化
        self.assertIsNotNone(score_db.db_manager)
        self.assertIsNotNone(score_db.user_manager)
        self.assertIsNotNone(score_db.score_operations)

        # 验证模块间的依赖关系
        self.assertEqual(
            score_db.user_manager.db_manager,
            score_db.db_manager
        )
        self.assertEqual(
            score_db.score_operations.db_manager,
            score_db.db_manager
        )


def run_tests():
    """运行所有测试"""
    print("=" * 60)
    print("积分系统模块化测试")
    print("=" * 60)

    # 创建测试套件
    test_suite = unittest.TestSuite()

    # 添加测试用例
    test_suite.addTest(unittest.makeSuite(TestModularScoreSystem))
    test_suite.addTest(unittest.makeSuite(TestSystemIntegration))

    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)

    # 输出结果
    print("\n" + "=" * 60)
    print(f"测试结果: 运行 {result.testsRun} 个测试")
    print(f"成功: {result.testsRun - len(result.failures) - len(result.errors)}")
    print(f"失败: {len(result.failures)}")
    print(f"错误: {len(result.errors)}")

    if result.failures:
        print("\n失败的测试:")
        for test, traceback in result.failures:
            print(f"- {test}: {traceback}")

    if result.errors:
        print("\n错误的测试:")
        for test, traceback in result.errors:
            print(f"- {test}: {traceback}")

    print("=" * 60)

    return result.wasSuccessful()


if __name__ == "__main__":
    success = run_tests()
    sys.exit(0 if success else 1)