J:\ai\�������\tt2\entrance\entrance_core.c     entrance_core.py        __init__        entrance_core.EntranceCore.__init__     msg_deal        entrance_core.EntranceCore.msg_deal draw        entrance_core.EntranceCore.draw sing    entrance_core.EntranceCore.sing dance   entrance_core.EntranceCore.dance        insert_request  entrance_core.EntranceCore.insert_request       entrance_core   Interpreter change detected - this module can only be loaded into one interpreter per process.  name    __loader__  loader      __file__    origin      __package__ parent      __path__        submodule_search_locations      Module 'entrance_core' has already been imported. Re-initialisation is not supported.   builtins        cython_runtime  __builtins__    init entrance_core      %s() got multiple values for keyword argument '%U'      %.200s() keywords must be strings       %s() got an unexpected keyword argument '%U'    exactly s       %.200s() takes %.8s %zd positional argument%.1s (%zd given)     name '%U' is not defined         while calling a Python object  NULL result without error in PyObject_Call      join() result is too long for a Python string   cannot import name %S   _cython_3_0_11  Shared Cython type %.200s is not a type object  Shared Cython type %.200s has the wrong size, try recompiling   keywords must be strings        __name__ must be set to a string object __qualname__ must be set to a string object     function's dictionary may not be deleted        setting function's dictionary to a non-dict     __defaults__ must be set to a tuple object      changes to cyfunction.__defaults__ will not currently affect the values used in function calls  __kwdefaults__ must be set to a dict object     changes to cyfunction.__kwdefaults__ will not currently affect the values used in function calls        __annotations__ must be set to a dict object    func_doc        __doc__ func_name       __name__        __qualname__    func_dict       __dict__        func_globals    __globals__     func_closure    __closure__     func_code       __code__        func_defaults   __defaults__    __kwdefaults__  __annotations__ _is_coroutine   __module__      __reduce__      Bad call flags for CyFunction   <cyfunction %U at %p>   %.200s() takes no arguments (%zd given) %.200s() takes exactly one argument (%zd given) %.200s() takes no keyword arguments     unbound method %.200S() needs an argument       %.200s() needs an argument      _cython_3_0_11.cython_function_or_method        %s (%s:%d)      does not match          compile time Python version %d.%d of module '%.100s' %s runtime version %d.%d   func.draw.draw_core \   func.score.oper_score   func.search.search_core info    exception       actionOper      isExtend        str_filter      LLmCore drawCore        cline_in_traceback      threading       func.sing.sing_core     CmdCore func.tools.singleton_mode       SingData        (�   singCore        __prepare__     ]弹幕捕获：[       ImageCore       entrance_core   chatDB  func.nsfw.nsfw_core     msg_deal_dance  EntranceCore.draw       EntranceCore.__init__   func.gobal.data jsonData    *   __main__        __qualname__    draw    __module__  ?   self    username        msg_deal_score_rank     __dict__        ��   _initializing   danceCore       func.vtuber.action_oper .       strftime    target      NsfwCore    ChatDB  dance       insert_request_thread   inner_draw      EntranceCore.sing       _is_coroutine   super   __spec__        msg_deal        searchCore      SearchCore  now start   llmCore %Y-%m-%d %H:%M:%S       msg_deal_scene  drawData        func.tools.string_util  __import__      DanceData   query   log __doc__ DanceCore       J:\ai\吟美打包\tt2\entrance\entrance_core.py        ObsInit __set_name__    func.obs.obs_init       EntranceCore    用户请求流水记录异常: uid     func.log.default_log    submitTime      DrawCore        __name__        DefaultLog      __init_subclass__       danceData       singData        func.image.image_core   msg_deal_emotevideo     datetime        channel num     
        处理弹幕消息
            text    __test__        SingCore    sing        DrawData    e   func.cmd.cmd_core       msg_deal_clothes        StringUtil      EntranceCore.msg_deal   EntranceCore.insert_request     is_index_contain_string uface   func.llm.chat_db        insert_request  func.dance.dance_core   Thread  EntranceCore.dance      __metaclass__   cmdCore inner_dance     getLogger       operScore       drawcontent obs args    __init__        traceid [       nsfwCore        asyncio.coroutines  get_ws      is_dance        user_name       singleton   cmd ]:      inner_sing      ActionOper      imageCore       func.llm.llm_core       OperScore