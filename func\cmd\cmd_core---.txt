J:\ai\�������\ai-yinmei\func\cmd\cmd_core.c    cmd_core.py     __init__        cmd_core.CmdCore.__init__   cmd cmd_core.CmdCore.cmd    cmd_core        Interpreter change detected - this module can only be loaded into one interpreter per process.  name    __loader__  loader      __file__    origin      __package__ parent      __path__        submodule_search_locations      Module 'cmd_core' has already been imported. Re-initialisation is not supported.        builtins        cython_runtime  __builtins__    init cmd_core   %s() got multiple values for keyword argument '%U'      %.200s() keywords must be strings       %s() got an unexpected keyword argument '%U'    exactly s       %.200s() takes %.8s %zd positional argument%.1s (%zd given)     name '%U' is not defined         while calling a Python object  NULL result without error in PyObject_Call      join() result is too long for a Python string   cannot import name %S   _cython_3_0_11  Shared Cython type %.200s is not a type object  Shared Cython type %.200s has the wrong size, try recompiling   keywords must be strings        __name__ must be set to a string object __qualname__ must be set to a string object     function's dictionary may not be deleted        setting function's dictionary to a non-dict     __defaults__ must be set to a tuple object              changes to cyfunction.__defaults__ will not currently affect the values used in function calls  __kwdefaults__ must be set to a dict object     changes to cyfunction.__kwdefaults__ will not currently affect the values used in function calls        __annotations__ must be set to a dict object    func_doc        __doc__ func_name       __name__        __qualname__    func_dict       __dict__        func_globals    __globals__     func_closure    __closure__     func_code       __code__        func_defaults   __defaults__    __kwdefaults__  __annotations__ _is_coroutine   __module__      __reduce__      Bad call flags for CyFunction   <cyfunction %U at %p>   %.200s() takes no arguments (%zd given) %.200s() takes exactly one argument (%zd given) %.200s() takes no keyword arguments     unbound method %.200S() needs an argument       %.200s() needs an argument      _cython_3_0_11.cython_function_or_method        %s (%s:%d)      does not match          compile time Python version %d.%d of module '%.100s' %s runtime version %d.%d   func.score.oper_score   下首  ttsCore info    不要跳舞    SongNowName ^   taskkill /T /F /IM mpv.exe      cline_in_traceback      LLmData next    threading       CmdCore func.tools.singleton_mode       SingData        H� �   __prepare__     TTsData J:\ai\吟美打包\ai-yinmei\func\cmd\cmd_core.py       emoteOper   ][  is_creating_song        taskkill /T /F /IM accompany.exe        func.gobal.data SearchData      __main__        has_string_reg_list ?   __qualname__    __module__  self        username        CmdCore.cmd     __dict__        � �   _initializing   \next   切歌  target  \dance  stop dance      socre_thread    _is_coroutine   super   is_ai_ready     __spec__    start       drawData        func.tools.string_util  __import__      DanceData   query   log __doc__ __set_name__    imageData   system      is_tts_playing  下一首       oper_score  uid func.log.default_log    is_drawing      __name__        \停止跳舞   func.vtuber.emote_oper  DefaultLog      __init_subclass__       llmData danceData       singData        ImageData       停止跳舞    CmdCore.__init__        assistant_tts_say       your_score      channel *   text        __test__    \stop       DrawData        taskkill /T /F /IM song.exe     ]你的积分小于     StringUtil      is_contain  uface       is_SearchText   .       ttsData Thread  __metaclass__   getLogger       operScore   args        __init__        traceid [       停止学歌    asyncio.coroutines      searchData      sing_play_flag  is_dance        cmd_core    score       user_name       ，不能进行切歌，请通过聊天和点赞赚取积分，查询积分请输入"我的积分"      singleton   cmd ]执行命令：    os  EmoteOper       is_SearchImg    get_score OperScore