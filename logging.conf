# 日志配置文件
# 用于控制各个模块的日志级别

[loggers]
keys=root,apscheduler,websockets,urllib3,requests,asyncio

[handlers]
keys=consoleHandler,fileHandler

[formatters]
keys=simpleFormatter

[logger_root]
level=INFO
handlers=consoleHandler,fileHandler

[logger_apscheduler]
level=WARNING
handlers=consoleHandler
qualname=apscheduler
propagate=0

[logger_websockets]
level=WARNING
handlers=consoleHandler
qualname=websockets
propagate=0

[logger_urllib3]
level=WARNING
handlers=consoleHandler
qualname=urllib3
propagate=0

[logger_requests]
level=WARNING
handlers=consoleHandler
qualname=requests
propagate=0

[logger_asyncio]
level=WARNING
handlers=consoleHandler
qualname=asyncio
propagate=0

[handler_consoleHandler]
class=StreamHandler
level=INFO
formatter=simpleFormatter
args=(sys.stdout,)

[handler_fileHandler]
class=FileHandler
level=DEBUG
formatter=simpleFormatter
args=('logs/app.log',)

[formatter_simpleFormatter]
format=%(asctime)s - %(name)s - %(levelname)s - %(message)s