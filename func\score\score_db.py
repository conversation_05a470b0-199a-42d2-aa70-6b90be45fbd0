"""
积分系统主控制器 - 重构版本
整合各个子模块，保持向后兼容性
"""

from datetime import datetime
from bson.json_util import dumps
import re
import time
from typing import Dict, List, Optional, Any, Tuple
import pymongo
from pymongo.errors import DuplicateKeyError, BulkWriteError

from flask import jsonify
from func.tools.singleton_mode import singleton
from func.log.default_log import DefaultLog

# 导入新的模块化组件
from .core.database_manager import DatabaseManager
from .modules.user_manager import UserManager
from .modules.score_operations import ScoreOperations

@singleton
class ScoreDB:
    def __init__(self):
        """初始化积分系统主控制器"""
        print("ScoreDB init-----------")
        self.log = DefaultLog().getLogger()

        # 初始化数据库管理器
        self.db_manager = DatabaseManager()

        # 初始化各个功能模块
        self.user_manager = UserManager(self.db_manager)
        self.score_operations = ScoreOperations(self.db_manager)

        # 保持向后兼容性的属性
        self.db = self.db_manager.db
        self.score_col = self.db_manager.users_collection
        self.score_record_col = self.db_manager.records_collection
        self.chatlistDB = self.db_manager.chatlist_collection
        self.cache_manager = self.db_manager.cache_manager
        self.metrics = self.db_manager.metrics

        self.log.info("积分系统主控制器初始化完成")
    
    def _create_indexes(self):
        """创建数据库索引以提高查询性能"""
        try:
            # 首先清理重复数据
            self._cleanup_duplicate_users()
            
            # 用户表索引 - 使用更安全的索引创建方式
            self._create_index_safely(self.score_col, "openId", unique=True)
            self._create_index_safely(self.score_col, [("score", -1)])  # 积分排行榜查询
            self._create_index_safely(self.score_col, [("createTime", -1)])  # 注册时间查询
            self._create_index_safely(self.score_col, [("updateTime", -1)])  # 更新时间查询
            
            # 积分记录表索引
            self._create_index_safely(self.score_record_col, "openId")
            self._create_index_safely(self.score_record_col, "userName")
            self._create_index_safely(self.score_record_col, [("submitTime", -1)])  # 按时间倒序查询
            self._create_index_safely(self.score_record_col, [("openId", 1), ("submitTime", -1)])  # 复合索引
            self._create_index_safely(self.score_record_col, "oper")  # 操作类型索引
            
            self.log.info("数据库索引创建完成")
        except Exception as e:
            self.log.error(f"创建数据库索引过程中发生错误: {str(e)}")

    def _create_index_safely(self, collection, keys, **kwargs):
        """安全地创建索引，如果已存在则跳过"""
        try:
            collection.create_index(keys, **kwargs)
            index_name = keys if isinstance(keys, str) else str(keys)
            self.log.debug(f"索引创建成功: {index_name}")
        except pymongo.errors.DuplicateKeyError as e:
            # 如果是唯一索引冲突，需要清理数据后重试
            if kwargs.get('unique'):
                self.log.warning(f"唯一索引创建失败，存在重复数据: {str(e)}")
                # 对于 openId 唯一索引，尝试清理重复数据后重试一次
                if keys == "openId":
                    self.log.info("尝试清理重复的 openId 数据...")
                    self._cleanup_duplicate_users()
                    try:
                        collection.create_index(keys, **kwargs)
                        self.log.info("清理重复数据后，openId 唯一索引创建成功")
                    except Exception as retry_error:
                        self.log.error(f"重试创建 openId 唯一索引失败: {str(retry_error)}")
            else:
                raise e
        except pymongo.errors.OperationFailure as e:
            # 如果索引已存在，跳过
            if "already exists" in str(e) or "IndexOptionsConflict" in str(e):
                self.log.debug(f"索引已存在，跳过创建: {keys}")
            else:
                self.log.warning(f"索引创建失败: {keys} - {str(e)}")
        except Exception as e:
            self.log.error(f"创建索引时发生未知错误: {keys} - {str(e)}")

    def _cleanup_duplicate_users(self):
        """清理重复的用户数据，保留最新的记录"""
        try:
            # 查找重复的 openId
            pipeline = [
                {"$group": {
                    "_id": "$openId",
                    "count": {"$sum": 1},
                    "docs": {"$push": "$$ROOT"}
                }},
                {"$match": {"count": {"$gt": 1}}}
            ]
            
            duplicates = list(self.score_col.aggregate(pipeline))
            
            if not duplicates:
                self.log.debug("未发现重复的用户数据")
                return
                
            cleaned_count = 0
            for duplicate_group in duplicates:
                openId = duplicate_group["_id"]
                docs = duplicate_group["docs"]
                
                # 按更新时间排序，保留最新的记录
                docs.sort(key=lambda x: x.get("updateTime", ""), reverse=True)
                keep_doc = docs[0]  # 保留最新的
                remove_docs = docs[1:]  # 删除其他的
                
                # 删除重复记录
                for doc in remove_docs:
                    try:
                        self.score_col.delete_one({"_id": doc["_id"]})
                        cleaned_count += 1
                        self.log.info(f"删除重复用户记录: {openId} (保留最新记录)")
                    except Exception as delete_error:
                        self.log.error(f"删除重复记录失败: {openId} - {str(delete_error)}")
            
            if cleaned_count > 0:
                self.log.info(f"清理重复用户数据完成，共删除 {cleaned_count} 条重复记录")
            
        except Exception as e:
            self.log.error(f"清理重复用户数据失败: {str(e)}")

    def get_score(self, openId: str, use_cache: bool = True) -> Optional[Dict[str, Any]]:
        """
        获取用户积分信息[调用功能前查询积分]
        委托给用户管理器处理
        """
        return self.user_manager.get_user(openId, use_cache)

    def oper_score(self, openId: str, userName: str, score: int, oper: str, use_transaction: bool = True) -> bool:
        """
        积分操作[加分：聊天  减分：切歌：1分、绘画：1分、唱歌：2分、跳舞：3分、切舞：1分]
        委托给积分操作管理器处理
        """
        return self.score_operations.operate_score(openId, userName, score, oper, use_transaction)

    def _oper_score_internal(self, openId: str, userName: str, score: int, oper: str, use_transaction: bool, start_time: float) -> bool:
        """内部积分操作实现"""
        try:
            self.log.info(f"开始积分录入：[{openId}][{userName}][{score}][{oper}]")

            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

            # 先检查用户是否存在
            user_exists = self.score_col.find_one({"openId": openId}, {"_id": 1})
            if not user_exists:
                self.log.error(f"用户不存在，无法更新积分: {openId}")
                self.metrics.record_operation("oper_score", False, time.time() - start_time, openId, score, "user_not_found")
                return False

            # 检查是否支持事务
            if use_transaction and self._supports_transactions():
                return self._oper_score_with_transaction(openId, userName, score, oper, current_time, start_time)
            else:
                return self._oper_score_without_transaction(openId, userName, score, oper, current_time, start_time)

        except Exception as e:
            self.log.error(f"积分操作失败：[{openId}][{userName}][{score}][{oper}] - {str(e)}")
            self.metrics.record_operation("oper_score", False, time.time() - start_time, openId, score, "operation_failed")
            return False

    def _supports_transactions(self) -> bool:
        """检查数据库是否支持事务"""
        try:
            # 检查是否有客户端和会话支持
            if not hasattr(self.db.client, 'start_session'):
                return False

            # 尝试获取服务器状态来检查是否为副本集或分片
            server_status = self.db.command("serverStatus")

            # 检查是否为副本集
            if "repl" in server_status:
                return True

            # 检查是否为分片集群
            if "sharding" in server_status:
                return True

            # 单机MongoDB不支持事务
            self.log.debug("检测到单机MongoDB，将使用非事务模式")
            return False

        except Exception as e:
            self.log.debug(f"检查事务支持失败，使用非事务模式: {e}")
            return False

    def _oper_score_with_transaction(self, openId: str, userName: str, score: int, oper: str, current_time: str, start_time: float) -> bool:
        """使用事务的积分操作"""
        try:
            with self.db.client.start_session() as session:
                with session.start_transaction():
                    # 更新用户积分
                    score_data_update = {
                        "$inc": {"score": score},
                        "$set": {"updateTime": current_time}
                    }

                    update_result = self.score_col.update_one(
                        {"openId": openId},
                        score_data_update,
                        session=session
                    )

                    if update_result.matched_count == 0:
                        self.log.error(f"积分更新失败: {openId}")
                        session.abort_transaction()
                        self.metrics.record_operation("oper_score", False, time.time() - start_time, openId, score, "update_failed")
                        return False

                    # 插入积分操作记录
                    score_record = {
                        "openId": openId,
                        "userName": userName,
                        "score": score,
                        "oper": oper,
                        "submitTime": current_time
                    }

                    self.score_record_col.insert_one(score_record, session=session)

                    # 清除相关缓存
                    self.cache_manager.clear_user_cache(openId)

                    self.log.info(f"积分录入成功（事务）：[{openId}][{userName}][{score}][{oper}]")
                    self.metrics.record_operation("oper_score", True, time.time() - start_time, openId, score)
                    return True

        except Exception as e:
            # 检查是否为事务不支持错误
            error_msg = str(e)
            if "Transaction numbers are only allowed on a replica set member or mongos" in error_msg:
                self.log.warning(f"检测到事务不支持错误，切换到非事务模式: {openId}")
                # 回退到非事务模式
                return self._oper_score_without_transaction(openId, userName, score, oper, current_time, start_time)
            else:
                self.log.error(f"事务积分操作失败: {str(e)} - 上下文: {{'openId': '{openId}', 'score': {score}}}")
                self.metrics.record_operation("oper_score", False, time.time() - start_time, openId, score, "transaction_failed")
                return False

    def _oper_score_without_transaction(self, openId: str, userName: str, score: int, oper: str, current_time: str, start_time: float) -> bool:
        """不使用事务的积分操作（兼容模式）"""
        try:
            # 更新用户积分
            score_data_update = {
                "$inc": {"score": score},
                "$set": {"updateTime": current_time}
            }

            update_result = self.score_col.update_one(
                {"openId": openId},
                score_data_update
            )

            if update_result.matched_count == 0:
                self.log.error(f"积分更新失败: {openId}")
                self.metrics.record_operation("oper_score", False, time.time() - start_time, openId, score, "update_failed")
                return False

            # 插入积分操作记录
            score_record = {
                "openId": openId,
                "userName": userName,
                "score": score,
                "oper": oper,
                "submitTime": current_time
            }

            try:
                self.score_record_col.insert_one(score_record)

                # 清除相关缓存
                self.cache_manager.clear_user_cache(openId)

                self.log.info(f"积分录入成功：[{openId}][{userName}][{score}][{oper}]")
                self.metrics.record_operation("oper_score", True, time.time() - start_time, openId, score)
                return True

            except Exception as record_error:
                # 如果记录插入失败，回滚积分更新
                self.log.warning(f"积分记录插入失败，尝试回滚: {str(record_error)}")
                rollback_update = {
                    "$inc": {"score": -score},
                    "$set": {"updateTime": current_time}
                }
                self.score_col.update_one({"openId": openId}, rollback_update)
                self.metrics.record_operation("oper_score", False, time.time() - start_time, openId, score, "record_failed")
                return False

        except Exception as e:
            self.log.error(f"积分操作失败: {str(e)}")
            self.metrics.record_operation("oper_score", False, time.time() - start_time, openId, score, "operation_failed")
            return False

    def _validate_openid(self, openId: str) -> bool:
        """验证openId"""
        if not openId or not isinstance(openId, str) or len(openId.strip()) == 0:
            self.log.error(f"无效的openId: {openId}")
            return False
        return True

    def _validate_score_params(self, openId: str, userName: str, score: int, oper: str) -> bool:
        """验证积分操作参数"""
        if not self._validate_openid(openId):
            return False
        
        if not userName or not isinstance(userName, str) or len(userName.strip()) == 0:
            self.log.error(f"无效的userName: {userName}")
            return False
            
        if not isinstance(score, int) or score == 0:
            self.log.error(f"无效的score: {score} (类型: {type(score)})")
            return False
            
        if not oper or not isinstance(oper, str) or len(oper.strip()) == 0:
            self.log.error(f"无效的oper: {oper}")
            return False
            
        return True

    def user_refresh(self, openId: str, userName: str, face: str = "") -> Optional[Dict[str, Any]]:
        """
        新增用户&存量用户更新[用户进入直播间]
        委托给用户管理器处理
        """
        return self.user_manager.update_user(openId, userName, face)

    def _validate_user_params(self, openId: str, userName: str) -> bool:
        """验证用户参数"""
        if not self._validate_openid(openId):
            return False
            
        if not userName or not isinstance(userName, str) or len(userName.strip()) == 0:
            self.log.error(f"无效的userName: {userName}")
            return False
            
        return True

    def get_reg_userinfo(self, openId: str) -> Optional[Dict[str, Any]]:
        """获取用户信息（兼容性方法）"""
        return self.user_manager.get_user(openId)

    def new_user_insert(self, openId: str, userName: str, face: str) -> Dict[str, Any]:
        """新用户注册（兼容性方法）"""
        return self.user_manager.new_user_insert(openId, userName, face)

    def find_score_record_page(self, username: str = "", page_number: int = 1, page_size: int = 20) -> Dict[str, Any]:
        """
        分页查询积分操作记录
        优化版本，提高查询性能
        :param username: 用户名（支持模糊搜索）
        :param page_number: 页码（从1开始）
        :param page_size: 每页大小
        :return: 分页后的记录
        """
        try:
            # 参数验证和标准化
            page_number = max(1, int(page_number))
            page_size = max(1, min(100, int(page_size)))  # 限制页面大小在1-100之间
                
            skip_count = (page_number - 1) * page_size
            
            # 构建查询条件
            query_filter = {}
            if username and username.strip():
                # 使用索引友好的查询
                query_filter["userName"] = {"$regex": re.escape(username.strip()), "$options": "i"}
            
            # 只查询必要字段，提高性能
            projection = {
                "_id": 0, 
                "openId": 1, 
                "userName": 1, 
                "score": 1, 
                "oper": 1, 
                "submitTime": 1
            }
            
            # 查询数据
            cursor = self.score_record_col.find(query_filter, projection)\
                .sort("submitTime", -1)\
                .skip(skip_count)\
                .limit(page_size)

            # 计算总数
            total_documents = self.score_record_col.count_documents(query_filter)
            total_pages = (total_documents + page_size - 1) // page_size

            records = list(cursor)
            
            return {
                "data": records,
                "total": total_documents,
                "current_page": page_number,
                "total_pages": total_pages,
                "page_size": page_size,
                "has_next": page_number < total_pages,
                "has_prev": page_number > 1
            }
            
        except Exception as e:
            self.log.error(f"查询积分记录失败: {str(e)}")
            return {
                "data": [],
                "total": 0,
                "current_page": max(1, page_number),
                "total_pages": 0,
                "page_size": max(1, page_size),
                "has_next": False,
                "has_prev": False
            }

    def find_user_score_records(self, openId: str, limit: int = 50) -> List[Dict[str, Any]]:
        """
        查询指定用户的积分记录
        :param openId: 用户ID
        :param limit: 返回记录数量限制
        :return: 用户积分记录列表
        """
        try:
            if not self._validate_openid(openId):
                return []
                
            limit = max(1, min(200, int(limit)))  # 限制在1-200之间
            
            projection = {
                "_id": 0,
                "score": 1,
                "oper": 1,
                "submitTime": 1
            }
            
            cursor = self.score_record_col.find(
                {"openId": openId}, 
                projection
            ).sort("submitTime", -1).limit(limit)
            
            return list(cursor)
            
        except Exception as e:
            self.log.error(f"查询用户积分记录失败 openId={openId}: {str(e)}")
            return []

    def find_userinfo(self, openId: str) -> Optional[Dict[str, Any]]:
        """
        查找用户信息 - 保留兼容性
        建议使用 get_score 方法
        :param openId: 用户开放平台id
        :return: 用户信息
        """
        return self.get_score(openId)

    def find_score_rank(self, limit: int = 100, min_score: int = 0, use_cache: bool = True) -> List[Dict[str, Any]]:
        """
        查询积分排行榜
        优化版本，支持缓存，提高查询性能
        :param limit: 返回的排行榜数量，默认100
        :param min_score: 最小积分要求，默认0
        :param use_cache: 是否使用缓存
        :return: 排行榜数据
        """
        start_time = time.time()

        try:
            limit = max(1, min(1000, int(limit)))  # 限制查询数量在1-1000之间
            min_score = max(0, int(min_score))

            # 尝试从缓存获取（仅当min_score为0时使用缓存）
            if use_cache and min_score == 0:
                cached_ranks = self.cache_manager.get_rank_cache(limit)
                if cached_ranks:
                    self.metrics.record_operation("find_score_rank", True, time.time() - start_time)
                    return cached_ranks

            # 使用聚合管道优化查询
            pipeline = [
                {"$match": {"score": {"$gte": min_score}}},
                {"$sort": {"score": -1}},
                {"$limit": limit},
                {"$project": {
                    "_id": 0,
                    "openId": 1,
                    "userName": 1,
                    "userface": 1,
                    "score": 1,
                    "updateTime": 1
                }},
                {"$addFields": {
                    "rank": {"$add": [{"$indexOfArray": [{"$range": [0, limit]}, {"$subtract": [{"$indexOfArray": [{"$range": [0, limit]}, 0]}, 0]}]}, 1]}
                }}
            ]

            # 使用更简单的方式添加排名
            cursor = self.score_col.find({"score": {"$gte": min_score}}, {
                "_id": 0,
                "openId": 1,
                "userName": 1,
                "userface": 1,
                "score": 1,
                "updateTime": 1
            }).sort("score", -1).limit(limit)

            ranks = list(cursor)

            # 添加排名信息
            for i, rank in enumerate(ranks, 1):
                rank["rank"] = i

            # 缓存结果（仅当min_score为0时缓存）
            if use_cache and min_score == 0 and ranks:
                self.cache_manager.set_rank_cache(limit, ranks)

            self.metrics.record_operation("find_score_rank", True, time.time() - start_time)
            return ranks

        except Exception as e:
            self.log.error(f"查询积分排行榜失败: {str(e)}")
            self.metrics.record_operation("find_score_rank", False, time.time() - start_time, None, 0, "query_failed")
            return []

    def get_user_statistics(self) -> Dict[str, Any]:
        """
        获取用户统计信息
        :return: 统计信息
        """
        try:
            # 使用聚合管道一次性获取多个统计信息
            pipeline = [
                {
                    "$group": {
                        "_id": None,
                        "total_users": {"$sum": 1},
                        "total_score": {"$sum": "$score"},
                        "avg_score": {"$avg": "$score"},
                        "max_score": {"$max": "$score"},
                        "min_score": {"$min": "$score"},
                        "active_users": {
                            "$sum": {"$cond": [{"$gt": ["$score", 0]}, 1, 0]}
                        }
                    }
                }
            ]
            
            result = list(self.score_col.aggregate(pipeline))
            
            if result:
                stats = result[0]
                # 移除 _id 字段
                stats.pop('_id', None)
                # 格式化平均分
                if stats.get('avg_score'):
                    stats['avg_score'] = round(stats['avg_score'], 2)
                return stats
            else:
                return {
                    "total_users": 0,
                    "total_score": 0,
                    "avg_score": 0,
                    "max_score": 0,
                    "min_score": 0,
                    "active_users": 0
                }
                
        except Exception as e:
            self.log.error(f"获取用户统计信息失败: {str(e)}")
            return {}

    def get_user_count(self) -> int:
        """获取用户总数"""
        try:
            return self.score_col.count_documents({})
        except Exception as e:
            self.log.error(f"获取用户总数失败: {str(e)}")
            return 0

    def get_total_score(self) -> int:
        """获取系统总积分"""
        try:
            pipeline = [
                {"$group": {"_id": None, "total_score": {"$sum": "$score"}}}
            ]
            result = list(self.score_col.aggregate(pipeline))
            return result[0]["total_score"] if result else 0
        except Exception as e:
            self.log.error(f"获取系统总积分失败: {str(e)}")
            return 0

    def batch_update_scores(self, score_updates: List[Dict[str, Any]]) -> Tuple[bool, int, List[str]]:
        """
        批量更新用户积分 - 移除事务操作
        :param score_updates: 积分更新列表，格式：[{"openId": "xxx", "score_change": 10, "oper": "批量更新"}, ...]
        :return: (是否成功, 成功数量, 失败的openId列表)
        """
        try:
            if not score_updates:
                return True, 0, []
                
            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            success_count = 0
            failed_openids = []
            
            for update_item in score_updates:
                try:
                    openId = update_item.get("openId")
                    score_change = update_item.get("score_change", 0)
                    oper = update_item.get("oper", "批量更新")
                    
                    if not openId or score_change == 0:
                        continue
                        
                    # 获取用户信息
                    user_info = self.get_score(openId)
                    if not user_info:
                        failed_openids.append(openId)
                        self.log.warning(f"批量更新：用户不存在 {openId}")
                        continue
                        
                    # 更新用户积分
                    score_data_update = {
                        "$inc": {"score": score_change},
                        "$set": {"updateTime": current_time}
                    }
                    
                    result = self.score_col.update_one(
                        {"openId": openId}, 
                        score_data_update
                    )
                    
                    if result.matched_count > 0:
                        # 插入操作记录
                        score_record = {
                            "openId": openId,
                            "userName": user_info.get("userName", ""),
                            "score": score_change,
                            "oper": oper,
                            "submitTime": current_time
                        }
                        
                        try:
                            self.score_record_col.insert_one(score_record)
                            success_count += 1
                        except Exception as record_error:
                            self.log.warning(f"批量更新：记录插入失败 {openId}: {str(record_error)}")
                            # 尝试回滚积分更新
                            self.score_col.update_one(
                                {"openId": openId}, 
                                {"$inc": {"score": -score_change}}
                            )
                            failed_openids.append(openId)
                    else:
                        failed_openids.append(openId)
                        
                except Exception as item_error:
                    self.log.error(f"批量更新单项失败 {openId}: {str(item_error)}")
                    failed_openids.append(openId)
            
            self.log.info(f"批量更新积分完成：成功 {success_count}，失败 {len(failed_openids)}")
            return len(failed_openids) == 0, success_count, failed_openids
            
        except Exception as e:
            self.log.error(f"批量更新积分失败: {str(e)}")
            return False, 0, [item.get("openId", "") for item in score_updates if item.get("openId")]

    def search_users(self, keyword: str = "", page_number: int = 1, page_size: int = 20) -> Dict[str, Any]:
        """搜索用户（委托给用户管理器）"""
        return self.user_manager.search_users(keyword, page_number, page_size)

    # 通用数据库操作方法 - 保留兼容性
    def find_one_common(self, where: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        通用单条查询
        :param where: 查询条件
        :return: 查询结果
        """
        try:
            return self.score_col.find_one(where)
        except Exception as e:
            self.log.error(f"单条查询失败: {str(e)}")
            return None

    def find_common(self, where: Dict[str, Any], limit: int = 1000) -> List[Dict[str, Any]]:
        """
        通用多条查询
        :param where: 查询条件
        :param limit: 查询限制数量
        :return: 查询结果
        """
        try:
            limit = max(1, min(1000, int(limit)))
            return list(self.score_col.find(where).limit(limit))
        except Exception as e:
            self.log.error(f"多条查询失败: {str(e)}")
            return []

    def aggregate_common(self, pipeline: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        通用聚合查询
        :param pipeline: 聚合管道
        :return: 聚合结果
        """
        try:
            return list(self.score_col.aggregate(pipeline))
        except Exception as e:
            self.log.error(f"聚合查询失败: {str(e)}")
            return []

    def update_common(self, where: Dict[str, Any], info: Dict[str, Any]) -> bool:
        """
        通用更新操作
        :param where: 更新条件
        :param info: 更新信息
        :return: 更新是否成功
        """
        try:
            result = self.score_col.update_one(where, info)
            return result.matched_count > 0
        except Exception as e:
            self.log.error(f"更新操作失败: {str(e)}")
            return False

    def delete_user(self, openId: str) -> bool:
        """
        删除用户及其相关记录
        :param openId: 用户ID
        :return: 是否成功
        """
        try:
            if not self._validate_openid(openId):
                return False
                
            # 删除用户积分记录
            self.score_record_col.delete_many({"openId": openId})
            
            # 删除用户信息
            result = self.score_col.delete_one({"openId": openId})
            
            if result.deleted_count > 0:
                self.log.info(f"用户删除成功: {openId}")
                return True
            else:
                self.log.warning(f"用户不存在或删除失败: {openId}")
                return False
                
        except Exception as e:
            self.log.error(f"删除用户失败 {openId}: {str(e)}")
            return False

    def cleanup_old_records(self, days: int = 365) -> int:
        """
        清理旧的积分记录
        :param days: 保留天数
        :return: 删除的记录数
        """
        try:
            from datetime import timedelta
            
            cutoff_date = (datetime.now() - timedelta(days=days)).strftime("%Y-%m-%d %H:%M:%S")
            
            result = self.score_record_col.delete_many({
                "submitTime": {"$lt": cutoff_date}
            })
            
            deleted_count = result.deleted_count
            self.log.info(f"清理旧记录完成，删除 {deleted_count} 条记录")
            return deleted_count
            
        except Exception as e:
            self.log.error(f"清理旧记录失败: {str(e)}")
            return 0

    def get_score_statistics_by_operation(self) -> Dict[str, Any]:
        """
        按操作类型统计积分变化
        :return: 操作统计信息
        """
        try:
            pipeline = [
                {
                    "$group": {
                        "_id": "$oper",
                        "count": {"$sum": 1},
                        "total_score": {"$sum": "$score"},
                        "avg_score": {"$avg": "$score"},
                        "max_score": {"$max": "$score"},
                        "min_score": {"$min": "$score"}
                    }
                },
                {"$sort": {"count": -1}}
            ]
            
            results = list(self.score_record_col.aggregate(pipeline))
            
            # 格式化结果
            formatted_results = []
            for result in results:
                formatted_results.append({
                    "operation": result["_id"],
                    "count": result["count"],
                    "total_score": result["total_score"],
                    "avg_score": round(result["avg_score"], 2),
                    "max_score": result["max_score"],
                    "min_score": result["min_score"]
                })
            
            return {
                "data": formatted_results,
                "summary": {
                    "total_operations": len(formatted_results),
                    "total_records": sum(r["count"] for r in formatted_results)
                }
            }
            
        except Exception as e:
            self.log.error(f"获取操作统计失败: {str(e)}")
            return {"data": [], "summary": {"total_operations": 0, "total_records": 0}}

    def get_daily_score_trend(self, days: int = 30) -> Dict[str, Any]:
        """
        获取每日积分变化趋势
        :param days: 统计天数
        :return: 趋势数据
        """
        try:
            from datetime import timedelta
            
            start_date = (datetime.now() - timedelta(days=days)).strftime("%Y-%m-%d")
            
            pipeline = [
                {
                    "$match": {
                        "submitTime": {"$gte": start_date}
                    }
                },
                {
                    "$addFields": {
                        "date": {"$substr": ["$submitTime", 0, 10]}
                    }
                },
                {
                    "$group": {
                        "_id": "$date",
                        "total_score": {"$sum": "$score"},
                        "positive_score": {
                            "$sum": {"$cond": [{"$gt": ["$score", 0]}, "$score", 0]}
                        },
                        "negative_score": {
                            "$sum": {"$cond": [{"$lt": ["$score", 0]}, "$score", 0]}
                        },
                        "record_count": {"$sum": 1},
                        "user_count": {"$addToSet": "$openId"}
                    }
                },
                {
                    "$addFields": {
                        "active_users": {"$size": "$user_count"}
                    }
                },
                {
                    "$project": {
                        "date": "$_id",
                        "total_score": 1,
                        "positive_score": 1,
                        "negative_score": 1,
                        "record_count": 1,
                        "active_users": 1,
                        "_id": 0
                    }
                },
                {"$sort": {"date": 1}}
            ]
            
            results = list(self.score_record_col.aggregate(pipeline))
            
            return {
                "data": results,
                "summary": {
                    "total_days": len(results),
                    "date_range": {
                        "start": results[0]["date"] if results else None,
                        "end": results[-1]["date"] if results else None
                    }
                }
            }
            
        except Exception as e:
            self.log.error(f"获取每日积分趋势失败: {str(e)}")
            return {"data": [], "summary": {"total_days": 0, "date_range": {"start": None, "end": None}}}

    def get_user_activity_analysis(self, limit: int = 50) -> Dict[str, Any]:
        """
        用户活跃度分析
        :param limit: 返回用户数量限制
        :return: 活跃度分析数据
        """
        try:
            pipeline = [
                {
                    "$group": {
                        "_id": "$openId",
                        "userName": {"$last": "$userName"},
                        "total_records": {"$sum": 1},
                        "total_score_change": {"$sum": "$score"},
                        "positive_operations": {
                            "$sum": {"$cond": [{"$gt": ["$score", 0]}, 1, 0]}
                        },
                        "negative_operations": {
                            "$sum": {"$cond": [{"$lt": ["$score", 0]}, 1, 0]}
                        },
                        "last_activity": {"$max": "$submitTime"},
                        "first_activity": {"$min": "$submitTime"},
                        "operations": {"$addToSet": "$oper"}
                    }
                },
                {
                    "$addFields": {
                        "operation_types": {"$size": "$operations"},
                        "activity_ratio": {
                            "$divide": ["$positive_operations", "$total_records"]
                        }
                    }
                },
                {
                    "$project": {
                        "openId": "$_id",
                        "userName": 1,
                        "total_records": 1,
                        "total_score_change": 1,
                        "positive_operations": 1,
                        "negative_operations": 1,
                        "operation_types": 1,
                        "activity_ratio": {"$round": ["$activity_ratio", 3]},
                        "last_activity": 1,
                        "first_activity": 1,
                        "_id": 0
                    }
                },
                {"$sort": {"total_records": -1}},
                {"$limit": limit}
            ]
            
            results = list(self.score_record_col.aggregate(pipeline))
            
            return {
                "data": results,
                "summary": {
                    "analyzed_users": len(results),
                    "most_active": results[0] if results else None,
                    "avg_records_per_user": sum(r["total_records"] for r in results) / len(results) if results else 0
                }
            }
            
        except Exception as e:
            self.log.error(f"用户活跃度分析失败: {str(e)}")
            return {"data": [], "summary": {"analyzed_users": 0, "most_active": None, "avg_records_per_user": 0}}

    def optimize_database(self) -> Dict[str, Any]:
        """
        数据库优化操作
        :return: 优化结果
        """
        try:
            optimization_results = {
                "cleaned_duplicates": 0,
                "cleaned_old_records": 0,
                "indexes_created": 0,
                "database_stats": {}
            }
            
            # 清理重复数据
            self.log.info("开始数据库优化...")
            
            # 重新创建索引
            self._create_indexes()
            optimization_results["indexes_created"] = 1
            
            # 清理超过1年的旧记录
            old_records_cleaned = self.cleanup_old_records(365)
            optimization_results["cleaned_old_records"] = old_records_cleaned
            
            # 获取数据库统计信息
            optimization_results["database_stats"] = self.get_user_statistics()
            
            self.log.info("数据库优化完成")
            return optimization_results
            
        except Exception as e:
            self.log.error(f"数据库优化失败: {str(e)}")
            return {"error": str(e)}

    def export_user_data(self, openId: str) -> Dict[str, Any]:
        """
        导出用户完整数据
        :param openId: 用户ID
        :return: 用户完整数据
        """
        try:
            if not self._validate_openid(openId):
                return {}
                
            # 获取用户基本信息
            user_info = self.get_score(openId)
            if not user_info:
                return {}
            
            # 获取积分记录
            score_records = self.find_user_score_records(openId, 1000)
            
            # 统计信息
            pipeline = [
                {"$match": {"openId": openId}},
                {
                    "$group": {
                        "_id": None,
                        "total_records": {"$sum": 1},
                        "total_score_earned": {
                            "$sum": {"$cond": [{"$gt": ["$score", 0]}, "$score", 0]}
                        },
                        "total_score_spent": {
                            "$sum": {"$cond": [{"$lt": ["$score", 0]}, {"$abs": "$score"}, 0]}
                        },
                        "operations": {"$addToSet": "$oper"},
                        "first_activity": {"$min": "$submitTime"},
                        "last_activity": {"$max": "$submitTime"}
                    }
                }
            ]
            
            stats_result = list(self.score_record_col.aggregate(pipeline))
            stats = stats_result[0] if stats_result else {}
            
            return {
                "user_info": user_info,
                "score_records": score_records,
                "statistics": {
                    "total_records": stats.get("total_records", 0),
                    "total_score_earned": stats.get("total_score_earned", 0),
                    "total_score_spent": stats.get("total_score_spent", 0),
                    "operation_types": stats.get("operations", []),
                    "first_activity": stats.get("first_activity"),
                    "last_activity": stats.get("last_activity")
                },
                "export_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }
            
        except Exception as e:
            self.log.error(f"导出用户数据失败 {openId}: {str(e)}")
            return {"error": str(e)}

    def get_leaderboard_with_user_rank(self, openId: str, limit: int = 100) -> Dict[str, Any]:
        """
        获取排行榜并包含指定用户的排名信息
        :param openId: 用户ID
        :param limit: 排行榜数量
        :return: 排行榜和用户排名信息
        """
        try:
            # 获取排行榜
            leaderboard = self.find_score_rank(limit)
            
            # 查找用户排名
            user_rank = None
            user_info = None
            
            # 先在排行榜中查找
            for i, user in enumerate(leaderboard):
                if user["openId"] == openId:
                    user_rank = i + 1
                    user_info = user
                    break
            
            # 如果用户不在排行榜中，单独查询其排名
            if user_rank is None:
                user_info = self.get_score(openId)
                if user_info and user_info.get("score", 0) > 0:
                    # 统计有多少用户分数比当前用户高
                    higher_score_count = self.score_col.count_documents({
                        "score": {"$gt": user_info["score"]}
                    })
                    user_rank = higher_score_count + 1
            
            return {
                "leaderboard": leaderboard,
                "user_rank_info": {
                    "rank": user_rank,
                    "user_info": user_info,
                    "in_leaderboard": user_rank is not None and user_rank <= limit
                },
                "total_ranked_users": self.score_col.count_documents({"score": {"$gt": 0}})
            }
            
        except Exception as e:
            self.log.error(f"获取用户排行榜失败 {openId}: {str(e)}")
            return {"leaderboard": [], "user_rank_info": None, "total_ranked_users": 0}

    def get_score_distribution(self) -> Dict[str, Any]:
        """
        获取积分分布统计
        :return: 积分分布数据
        """
        try:
            # 定义积分区间
            score_ranges = [
                {"range": "0", "min": 0, "max": 0},
                {"range": "1-10", "min": 1, "max": 10},
                {"range": "11-50", "min": 11, "max": 50},
                {"range": "51-100", "min": 51, "max": 100},
                {"range": "101-500", "min": 101, "max": 500},
                {"range": "501-1000", "min": 501, "max": 1000},
                {"range": "1000+", "min": 1001, "max": float('inf')}
            ]
            
            distribution = []
            total_users = 0
            
            for score_range in score_ranges:
                if score_range["max"] == float('inf'):
                    count = self.score_col.count_documents({
                        "score": {"$gte": score_range["min"]}
                    })
                else:
                    count = self.score_col.count_documents({
                        "score": {"$gte": score_range["min"], "$lte": score_range["max"]}
                    })
                
                distribution.append({
                    "range": score_range["range"],
                    "count": count,
                    "percentage": 0  # 稍后计算
                })
                total_users += count
            
            # 计算百分比
            for item in distribution:
                item["percentage"] = round((item["count"] / total_users * 100), 2) if total_users > 0 else 0
            
            return {
                "distribution": distribution,
                "total_users": total_users,
                "analysis_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }
            
        except Exception as e:
            self.log.error(f"获取积分分布失败: {str(e)}")
            return {"distribution": [], "total_users": 0}

    def get_recent_activities(self, limit: int = 50, hours: int = 24) -> List[Dict[str, Any]]:
        """
        获取最近活动记录
        :param limit: 返回记录数量
        :param hours: 最近几小时内的活动
        :return: 最近活动列表
        """
        try:
            from datetime import timedelta
            
            # 计算时间范围
            cutoff_time = (datetime.now() - timedelta(hours=hours)).strftime("%Y-%m-%d %H:%M:%S")
            
            projection = {
                "_id": 0,
                "openId": 1,
                "userName": 1,
                "score": 1,
                "oper": 1,
                "submitTime": 1
            }
            
            cursor = self.score_record_col.find(
                {"submitTime": {"$gte": cutoff_time}},
                projection
            ).sort("submitTime", -1).limit(limit)
            
            activities = list(cursor)
            
            # 添加时间标签（如：刚刚、5分钟前等）
            current_time = datetime.now()
            for activity in activities:
                try:
                    activity_time = datetime.strptime(activity["submitTime"], "%Y-%m-%d %H:%M:%S")
                    time_diff = current_time - activity_time
                    
                    if time_diff.total_seconds() < 60:
                        activity["time_label"] = "刚刚"
                    elif time_diff.total_seconds() < 3600:
                        minutes = int(time_diff.total_seconds() // 60)
                        activity["time_label"] = f"{minutes}分钟前"
                    else:
                        hours = int(time_diff.total_seconds() // 3600)
                        activity["time_label"] = f"{hours}小时前"
                except:
                    activity["time_label"] = activity["submitTime"]
            
            return activities
            
        except Exception as e:
            self.log.error(f"获取最近活动失败: {str(e)}")
            return []

    def validate_data_integrity(self) -> Dict[str, Any]:
        """
        验证数据完整性
        :return: 验证结果
        """
        try:
            validation_results = {
                "issues": [],
                "statistics": {},
                "recommendations": []
            }
            
            # 检查孤儿记录（积分记录但用户不存在）
            pipeline = [
                {
                    "$lookup": {
                        "from": "users_list",
                        "localField": "openId",
                        "foreignField": "openId",
                        "as": "user"
                    }
                },
                {"$match": {"user": {"$size": 0}}},
                {"$group": {"_id": "$openId", "count": {"$sum": 1}}}
            ]
            
            orphan_records = list(self.score_record_col.aggregate(pipeline))
            if orphan_records:
                validation_results["issues"].append({
                    "type": "orphan_records",
                    "description": "发现孤儿积分记录（用户不存在）",
                    "count": len(orphan_records),
                    "details": orphan_records[:10]  # 只显示前10个
                })
                validation_results["recommendations"].append("建议清理孤儿积分记录")
            
            # 检查数据类型异常
            invalid_scores = self.score_col.count_documents({
                "score": {"$not": {"$type": "number"}}
            })
            if invalid_scores > 0:
                validation_results["issues"].append({
                    "type": "invalid_score_type",
                    "description": "发现积分字段类型异常",
                    "count": invalid_scores
                })
                validation_results["recommendations"].append("建议修复积分字段数据类型")
            
            # 检查缺失必要字段的记录
            missing_fields = self.score_col.count_documents({
                "$or": [
                    {"openId": {"$exists": False}},
                    {"userName": {"$exists": False}},
                    {"score": {"$exists": False}}
                ]
            })
            if missing_fields > 0:
                validation_results["issues"].append({
                    "type": "missing_fields",
                    "description": "发现缺失必要字段的用户记录",
                    "count": missing_fields
                })
                validation_results["recommendations"].append("建议补充或删除不完整的用户记录")
            
            # 获取统计信息
            validation_results["statistics"] = {
                "total_users": self.get_user_count(),
                "total_records": self.score_record_col.count_documents({}),
                "active_users": self.score_col.count_documents({"score": {"$gt": 0}}),
                "validation_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }
            
            if not validation_results["issues"]:
                validation_results["status"] = "healthy"
                validation_results["message"] = "数据完整性检查通过"
            else:
                validation_results["status"] = "issues_found"
                validation_results["message"] = f"发现 {len(validation_results['issues'])} 个问题"
            
            return validation_results
            
        except Exception as e:
            self.log.error(f"数据完整性验证失败: {str(e)}")
            return {"status": "error", "message": str(e)}

    def repair_data_issues(self) -> Dict[str, Any]:
        """
        修复数据问题
        :return: 修复结果
        """
        try:
            repair_results = {
                "actions_taken": [],
                "statistics": {}
            }
            
            # 清理孤儿积分记录
            pipeline = [
                {
                    "$lookup": {
                        "from": "users_list",
                        "localField": "openId",
                        "foreignField": "openId",
                        "as": "user"
                    }
                },
                {"$match": {"user": {"$size": 0}}},
                {"$project": {"_id": 1}}
            ]
            
            orphan_records = list(self.score_record_col.aggregate(pipeline))
            if orphan_records:
                orphan_ids = [record["_id"] for record in orphan_records]
                delete_result = self.score_record_col.delete_many({
                    "_id": {"$in": orphan_ids}
                })
                repair_results["actions_taken"].append({
                    "action": "删除孤儿积分记录",
                    "count": delete_result.deleted_count
                })
            
            # 修复缺失字段的记录
            update_result = self.score_col.update_many(
                {"userName": {"$exists": False}},
                {"$set": {"userName": "未知用户"}}
            )
            if update_result.modified_count > 0:
                repair_results["actions_taken"].append({
                    "action": "补充缺失的用户名",
                    "count": update_result.modified_count
                })
            
            update_result = self.score_col.update_many(
                {"score": {"$exists": False}},
                {"$set": {"score": 0}}
            )
            if update_result.modified_count > 0:
                repair_results["actions_taken"].append({
                    "action": "补充缺失的积分",
                    "count": update_result.modified_count
                })
            
            # 修复时间字段
            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            update_result = self.score_col.update_many(
                {"createTime": {"$exists": False}},
                {"$set": {"createTime": current_time}}
            )
            if update_result.modified_count > 0:
                repair_results["actions_taken"].append({
                    "action": "补充缺失的创建时间",
                    "count": update_result.modified_count
                })
            
            update_result = self.score_col.update_many(
                {"updateTime": {"$exists": False}},
                {"$set": {"updateTime": current_time}}
            )
            if update_result.modified_count > 0:
                repair_results["actions_taken"].append({
                    "action": "补充缺失的更新时间",
                    "count": update_result.modified_count
                })
            
            repair_results["statistics"] = {
                "total_actions": len(repair_results["actions_taken"]),
                "repair_time": current_time
            }
            
            if repair_results["actions_taken"]:
                self.log.info(f"数据修复完成，执行了 {len(repair_results['actions_taken'])} 个修复动作")
            else:
                self.log.info("未发现需要修复的数据问题")
            
            return repair_results
            
        except Exception as e:
            self.log.error(f"数据修复失败: {str(e)}")
            return {"error": str(e)}

    def get_metrics(self) -> Dict[str, Any]:
        """获取数据库层指标"""
        return self.metrics.get_all_metrics()

    def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        return self.cache_manager.get_cache_stats()

    def cleanup_cache(self):
        """清理缓存"""
        try:
            self.cache_manager.cleanup_expired_cache()
            self.metrics.cleanup_old_data()
            self.log.info("缓存清理完成")
        except Exception as e:
            self.log.error(f"缓存清理失败: {e}")

    def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        try:
            # 检查数据库连接
            db_healthy = True
            try:
                self.db.command('ping')
            except Exception as e:
                db_healthy = False
                self.log.error(f"数据库连接检查失败: {e}")

            # 检查缓存连接
            cache_healthy = True
            try:
                if self.cache_manager.redis_client:
                    self.cache_manager.redis_client.ping()
            except Exception as e:
                cache_healthy = False
                self.log.warning(f"缓存连接检查失败: {e}")

            # 获取基础统计
            basic_stats = self.metrics.get_basic_stats()

            return {
                "status": "healthy" if db_healthy else "unhealthy",
                "database_healthy": db_healthy,
                "cache_healthy": cache_healthy,
                "uptime_seconds": basic_stats.get("uptime_seconds", 0),
                "total_operations": basic_stats.get("total_operations", 0),
                "success_rate": basic_stats.get("success_rate", 0),
                "timestamp": time.time()
            }

        except Exception as e:
            self.log.error(f"健康检查失败: {e}")
            return {
                "status": "error",
                "error": str(e),
                "timestamp": time.time()
            }