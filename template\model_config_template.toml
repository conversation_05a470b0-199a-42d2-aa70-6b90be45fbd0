[inner]
version = "1.1.1"

# 配置文件版本号迭代规则同bot_config.toml

[[api_providers]] # API服务提供商（可以配置多个）
name = "DeepSeek"                       # API服务商名称（可随意命名，在models的api-provider中需使用这个命名）
base_url = "https://api.deepseek.cn/v1" # API服务商的BaseURL
api_key = "your-api-key-here"           # API密钥（请替换为实际的API密钥）
client_type = "openai"                  # 请求客户端（可选，默认值为"openai"，使用gimini等Google系模型时请配置为"gemini"）
max_retry = 2                           # 最大重试次数（单个模型API调用失败，最多重试的次数）
timeout = 30                            # API请求超时时间（单位：秒）
retry_interval = 10                     # 重试间隔时间（单位：秒）

[[api_providers]] # SiliconFlow的API服务商配置
name = "SiliconFlow"
base_url = "https://api.siliconflow.cn/v1"
api_key = "sk-ybtstwycmeubuaccxuljgachtzmxiohplzurafukequatdqf"
client_type = "openai"
max_retry = 2
timeout = 30
retry_interval = 10

[[api_providers]] # 特殊：Google的Gimini使用特殊API，与OpenAI格式不兼容，需要配置client为"gemini"
name = "Google"
base_url = "https://api.google.com/v1"
api_key = "your-google-api-key-1"
client_type = "gemini"
max_retry = 2
timeout = 30
retry_interval = 10


[[models]] # 模型（可以配置多个）
model_identifier = "deepseek-chat" # 模型标识符（API服务商提供的模型标识符）
name = "deepseek-v3"               # 模型名称（可随意命名，在后面中需使用这个命名）
api_provider = "DeepSeek"          # API服务商名称（对应在api_providers中配置的服务商名称）
price_in = 2.0                     # 输入价格（用于API调用统计，单位：元/ M token）（可选，若无该字段，默认值为0）
price_out = 8.0                    # 输出价格（用于API调用统计，单位：元/ M token）（可选，若无该字段，默认值为0）
#force_stream_mode = true          # 强制流式输出模式（若模型不支持非流式输出，请取消该注释，启用强制流式输出，若无该字段，默认值为false）

[[models]]
model_identifier = "Pro/deepseek-ai/DeepSeek-V3"
name = "siliconflow-deepseek-v3"
api_provider = "SiliconFlow"
price_in = 2.0
price_out = 8.0

[[models]]
model_identifier = "Pro/deepseek-ai/DeepSeek-R1-Distill-Qwen-32B"
name = "deepseek-r1-distill-qwen-32b"
api_provider = "SiliconFlow"
price_in = 4.0
price_out = 16.0

[[models]]
model_identifier = "Qwen/Qwen3-8B"
name = "qwen3-8b"
api_provider = "SiliconFlow"
price_in = 0
price_out = 0
[models.extra_params] # 可选的额外参数配置
enable_thinking = false # 不启用思考

[[models]]
model_identifier = "Qwen/Qwen3-14B"
name = "qwen3-14b"
api_provider = "SiliconFlow"
price_in = 0.5
price_out = 2.0
[models.extra_params] # 可选的额外参数配置
enable_thinking = false # 不启用思考

[[models]]
model_identifier = "Qwen/Qwen3-30B-A3B"
name = "qwen3-30b"
api_provider = "SiliconFlow"
price_in = 0.7
price_out = 2.8
[models.extra_params] # 可选的额外参数配置
enable_thinking = false # 不启用思考

[[models]]
model_identifier = "Qwen/Qwen2.5-VL-72B-Instruct"
name = "qwen2.5-vl-72b"
api_provider = "SiliconFlow"
price_in = 4.13
price_out = 4.13

[[models]]
model_identifier = "FunAudioLLM/SenseVoiceSmall"
name = "sensevoice-small"
api_provider = "SiliconFlow"
price_in = 0
price_out = 0

[[models]]
model_identifier = "BAAI/bge-m3"
name = "bge-m3"
api_provider = "SiliconFlow"
price_in = 0
price_out = 0


[model_task_config.utils] # 在麦麦的一些组件中使用的模型，例如表情包模块，取名模块，关系模块，是麦麦必须的模型
model_list = ["siliconflow-deepseek-v3"] # 使用的模型列表，每个子项对应上面的模型名称(name)
temperature = 0.2                        # 模型温度，新V3建议0.1-0.3
max_tokens = 800                         # 最大输出token数

[model_task_config.utils_small] # 在麦麦的一些组件中使用的小模型，消耗量较大，建议使用速度较快的小模型
model_list = ["qwen3-8b"]
temperature = 0.7
max_tokens = 800

[model_task_config.replyer_1] # 首要回复模型，还用于表达器和表达方式学习
model_list = ["siliconflow-deepseek-v3"]
temperature = 0.2                        # 模型温度，新V3建议0.1-0.3
max_tokens = 800

[model_task_config.replyer_2] # 次要回复模型
model_list = ["siliconflow-deepseek-v3"]
temperature = 0.7
max_tokens = 800

[model_task_config.planner] #决策：负责决定麦麦该做什么的模型
model_list = ["siliconflow-deepseek-v3"]
temperature = 0.3
max_tokens = 800

[model_task_config.emotion] #负责麦麦的情绪变化
model_list = ["siliconflow-deepseek-v3"]
temperature = 0.3
max_tokens = 800

[model_task_config.memory] # 记忆模型
model_list = ["qwen3-30b"]
temperature = 0.7
max_tokens = 800

[model_task_config.vlm] # 图像识别模型
model_list = ["qwen2.5-vl-72b"]
max_tokens = 800

[model_task_config.voice] # 语音识别模型
model_list = ["sensevoice-small"]

[model_task_config.tool_use] #工具调用模型，需要使用支持工具调用的模型
model_list = ["qwen3-14b"]
temperature = 0.7
max_tokens = 800

#嵌入模型
[model_task_config.embedding]
model_list = ["bge-m3"]

#------------LPMM知识库模型------------

[model_task_config.lpmm_entity_extract] # 实体提取模型
model_list = ["siliconflow-deepseek-v3"]
temperature = 0.2
max_tokens = 800

[model_task_config.lpmm_rdf_build] # RDF构建模型
model_list = ["siliconflow-deepseek-v3"]
temperature = 0.2
max_tokens = 800

[model_task_config.lpmm_qa] # 问答模型
model_list = ["deepseek-r1-distill-qwen-32b"]
temperature = 0.7
max_tokens = 800
