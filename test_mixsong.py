#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试mixsong页面结构
"""

import requests
from bs4 import BeautifulSoup
import re

def test_mixsong_page(mix_song_id):
    """测试访问mixsong页面"""
    url = f'https://www.kugou.com/mixsong/{mix_song_id}.html'
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/71.0.3578.98 Safari/537.36'
    }
    
    try:
        print(f'访问URL: {url}')
        response = requests.get(url, headers=headers, timeout=10)
        response.raise_for_status()
        response.encoding = 'utf-8'
        
        print(f'状态码: {response.status_code}')
        print(f'页面长度: {len(response.text)}')

        # 查看页面内容的前2000个字符
        print('\n页面内容预览:')
        print(response.text[:2000])
        print('...')

        # 使用BeautifulSoup解析
        soup = BeautifulSoup(response.text, 'html.parser')
        
        # 查找audio标签
        audio_tags = soup.find_all('audio')
        print(f'\n找到 {len(audio_tags)} 个audio标签')
        for i, audio in enumerate(audio_tags):
            print(f'  Audio {i}: {audio}')
        
        # 查找包含音频相关的script标签
        scripts = soup.find_all('script')
        print(f'\n找到 {len(scripts)} 个script标签')
        
        # 查找可能包含音频URL的内容
        audio_url_patterns = [
            r'"url"\s*:\s*"([^"]*\.(?:mp3|flac|m4a)[^"]*)"',
            r'"src"\s*:\s*"([^"]*\.(?:mp3|flac|m4a)[^"]*)"',
            r'src\s*=\s*["\']([^"\']*\.(?:mp3|flac|m4a)[^"\']*)["\']',
            r'https?://[^\s"]*\.(?:mp3|flac|m4a)(?:\?[^\s"]*)?'
        ]
        
        found_urls = []
        for i, script in enumerate(scripts):
            if script.string:
                content = script.string
                for pattern in audio_url_patterns:
                    matches = re.findall(pattern, content, re.IGNORECASE)
                    if matches:
                        print(f'\nScript {i} 中找到音频URL (模式: {pattern}):')
                        for match in matches:
                            url = match if isinstance(match, str) else match
                            if url.startswith(('http://', 'https://')):
                                print(f'  {url}')
                                found_urls.append(url)
        
        # 查找页面中的其他可能包含音频信息的元素
        print(f'\n查找其他音频相关元素:')
        
        # 查找所有包含音频文件扩展名的链接
        all_links = soup.find_all(['a', 'source', 'embed', 'object'])
        for link in all_links:
            href = link.get('href') or link.get('src') or link.get('data')
            if href and any(ext in href.lower() for ext in ['.mp3', '.flac', '.m4a']):
                print(f'  链接: {href}')
                found_urls.append(href)
        
        # 在页面文本中查找音频URL
        page_text = response.text
        for pattern in audio_url_patterns:
            matches = re.findall(pattern, page_text, re.IGNORECASE)
            if matches:
                print(f'\n页面文本中找到音频URL (模式: {pattern}):')
                for match in matches:
                    url = match if isinstance(match, str) else match
                    if url.startswith(('http://', 'https://')):
                        print(f'  {url}')
                        found_urls.append(url)
        
        print(f'\n总共找到 {len(set(found_urls))} 个唯一的音频URL')
        for url in set(found_urls):
            print(f'  {url}')
            
        return list(set(found_urls))
        
    except Exception as e:
        print(f'访问失败: {e}')
        return []

if __name__ == "__main__":
    # 测试几个不同的MixSongID
    test_ids = [
        '32042828',  # 周杰伦-稻香
        '177373480', # 草帽酱-戏说
        '615544655', # 张杰-轻舟
    ]
    
    for mix_id in test_ids:
        print("=" * 80)
        print(f"测试 MixSongID: {mix_id}")
        print("=" * 80)
        urls = test_mixsong_page(mix_id)
        print()
