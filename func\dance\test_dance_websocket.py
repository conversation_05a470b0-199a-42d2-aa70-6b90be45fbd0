# 测试WebSocket跳舞功能
import asyncio
import time
from func.dance.websocket_client import MMDWebSocketClient

def test_websocket_dance():
    """测试WebSocket跳舞功能"""
    print("开始测试WebSocket跳舞功能...")
    
    # 创建WebSocket客户端
    client = WebSocketClient()
    
    # 设置服务器地址（确保游戏端服务器正在运行）
    client.set_server_url("ws://localhost:8765")
    
    # 启动客户端
    print("启动WebSocket客户端...")
    client.start_client()
    
    # 等待连接建立
    print("等待连接建立...")
    time.sleep(3)
    
    # 测试发送不同的跳舞命令
    test_commands = [
        ("极乐净土", "测试用户1"),
        ("科目三", "测试用户2"), 
        ("随机", "测试用户3"),
        ("恋爱循环", "测试用户4"),
        ("不存在的舞蹈", "测试用户5")
    ]
    
    for dance_name, user_name in test_commands:
        print(f"\n测试发送跳舞命令: {dance_name}")
        
        success = client.send_dance_sync(dance_name, user_name)
        
        if success:
            print(f"✅ 成功发送跳舞命令: {dance_name}")
        else:
            print(f"❌ 发送跳舞命令失败: {dance_name}")
            
        # 等待一段时间再发送下一个命令
        time.sleep(2)
    
    print("\n测试完成")
    
    # 停止客户端
    client.stop_client()

def test_dance_core():
    """测试跳舞核心功能"""
    print("开始测试跳舞核心功能...")
    
    from func.dance.dance_core import DanceCore
    
    # 创建跳舞核心实例
    dance_core = DanceCore()
    
    # 测试跳舞消息处理
    test_messages = [
        "跳舞极乐净土",
        "跳一下科目三", 
        "舞蹈随机",
        "跳舞",
        "表情笑"
    ]
    
    for i, message in enumerate(test_messages):
        print(f"\n测试消息 {i+1}: {message}")
        
        # 模拟消息处理
        traceid = f"test_{i+1}"
        uid = f"user_{i+1}"
        user_name = f"测试用户{i+1}"
        
        # 测试跳舞消息处理
        result = dance_core.msg_deal_dance(traceid, message, uid, user_name)
        print(f"跳舞消息处理结果: {result}")
        
        # 测试表情消息处理  
        result = dance_core.msg_deal_emotevideo(traceid, message, uid, user_name)
        print(f"表情消息处理结果: {result}")
        
        time.sleep(1)
    
    print("\n跳舞核心功能测试完成")

if __name__ == "__main__":
    print("=== WebSocket跳舞功能测试 ===\n")
    
    # 首先测试游戏服务器是否运行
    print("请确保以下条件已满足：")
    print("1. 游戏端WebSocket服务器正在运行 (python upbge_game_server.py)")
    print("2. 端口8765未被占用")
    print("3. VMD文件目录已设置正确")
    
    choice = input("\n是否继续测试? (y/n): ")
    
    if choice.lower() == 'y':
        print("\n1. 测试WebSocket连接和消息发送")
        test_websocket_dance()
        
        print("\n" + "="*50)
        print("2. 测试跳舞核心功能")  
        test_dance_core()
        
        print("\n测试全部完成！")
    else:
        print("测试已取消") 