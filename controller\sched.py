from flask_apscheduler import APScheduler
from sympy import true
from func.draw.draw_core import DrawCore
from func.search.search_core import SearchCore
from func.sing.sing_core import SingCore
from func.tts.tts_core import TTsCore
from func.dance.dance_core import DanceCore
from func.image.image_core import ImageCore
from func.llm.llm_core import LLmCore
from func.vtuber.action_oper import ActionOper
from func.obs.obs_init import ObsInit
from func.task.idle import IdleCore
from func.tools.common_websocket import CommonWebsocket

# 降低调度器和相关模块的日志级别，减少DEBUG输出
import logging
logging.getLogger('apscheduler').setLevel(logging.WARNING)
logging.getLogger('apscheduler.scheduler').setLevel(logging.WARNING)
logging.getLogger('apscheduler.executors').setLevel(logging.WARNING)
logging.getLogger('apscheduler.jobstores').setLevel(logging.WARNING)
logging.getLogger('websockets').setLevel(logging.WARNING)
logging.getLogger('websockets.server').setLevel(logging.WARNING)
logging.getLogger('websockets.protocol').setLevel(logging.WARNING)

# import asyncio.coroutines

class sched(object):
    print("sched--------init----------")

    # 初始化调度器
    scheduler = APScheduler()
    
    # ============= OBS直播软件控制 ================
    # obs直播软件连接
    obs = ObsInit().get_ws()

    # 初始化各个核心组件
    drawCore = DrawCore()
    searchCore = SearchCore()
    singCore = SingCore()
    ttsCore = TTsCore()
    danceCore = DanceCore()
    imageCore = ImageCore()
    llmCore = LLmCore()
    actionOper = ActionOper()
    idleCore = IdleCore()
    commonWebsocket = CommonWebsocket()
    
    @classmethod
    def _add_check_jobs(cls):
        """添加各种检查任务"""
        #1 LLM回复
        cls.scheduler.add_job(func=cls.llmCore.check_answer, trigger="interval", seconds=1, id="answer", max_instances=100)
        #2 tts语音合成
        cls.scheduler.add_job(func=cls.ttsCore.check_tts, trigger="interval", seconds=1, id="tts", max_instances=1000)

        cls.scheduler.add_job(func=cls.ttsCore.check_tts_play, trigger="interval", seconds=1, id="tts0", max_instances=1000)

        #3 绘画
        cls.scheduler.add_job(func=cls.drawCore.check_draw, trigger="interval", seconds=1, id="draw", max_instances=50)
        #4 搜索资料
        cls.scheduler.add_job(func=cls.searchCore.check_text_search, trigger="interval", seconds=1, id="text_search", max_instances=50)
        #5 搜图
        cls.scheduler.add_job(func=cls.imageCore.check_img_search, trigger="interval", seconds=1, id="img_search", max_instances=50)
        #6 唱歌转换
        cls.scheduler.add_job(func=cls.singCore.check_sing, trigger="interval", seconds=1, id="sing", max_instances=50)
        #7 歌曲清单播放
        cls.scheduler.add_job(func=cls.singCore.check_playSongMenuList, trigger="interval", seconds=1, id="playSongMenuList", max_instances=50)
        #8 跳舞
        cls.scheduler.add_job(func=cls.danceCore.check_dance, args=[cls.scheduler], trigger="interval", seconds=1, id="dance", max_instances=10)
        #9 时间判断场景[白天黑夜切换]
        cls.scheduler.add_job(func=cls.actionOper.check_scene_time, trigger="cron", hour="6,17,18", id="scene_time")
        #10 欢迎语
        cls.scheduler.add_job(func=cls.llmCore.check_welcome_room, trigger="interval", seconds=20, id="welcome_room", max_instances=50)

# 创建sched实例
sched_instance = sched()

def bind_sched(app):
    """
    绑定调度任务到Flask应用
    
    Args:
        app: Flask应用实例
    """
    print("sched------bind_sched--------------")
    
    # 使用全局sched实例
    global sched_instance
    
    # 绑定调度任务到Flask应用
    sched_instance.scheduler.init_app(app)
    
    # 添加定时任务
    sched_instance._add_check_jobs()
    
    # 启动调度器
    sched_instance.scheduler.start()