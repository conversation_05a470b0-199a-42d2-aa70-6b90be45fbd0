#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
综合测试：PyTorch GPU、Web_through.py和音乐转换服务集成
"""
import time
import requests
import json
import threading
import subprocess
import sys
import os

def test_integrated_music_service():
    """测试集成的音乐转换服务"""
    print("=== 测试集成的音乐转换服务 ===")
    
    try:
        from func.sing.music_convert_service import music_convert_service
        print("✅ 音乐转换服务模块导入成功")
        
        # 测试服务启动
        print("🚀 启动集成的音乐转换服务...")
        music_convert_service.start_service(host="127.0.0.1", port=17171)
        time.sleep(3)
        
        # 测试状态接口
        try:
            response = requests.get("http://127.0.0.1:17171/status", timeout=10)
            if response.status_code == 200:
                status_data = response.json()
                print(f"✅ 集成服务状态接口正常: {status_data}")
            else:
                print(f"❌ 集成服务状态接口异常: {response.status_code}")
        except Exception as e:
            print(f"❌ 集成服务状态接口请求失败: {e}")
        
        # 测试转换任务
        try:
            response = requests.get("http://127.0.0.1:17171/append_song/测试歌曲", timeout=15)
            if response.status_code == 200:
                task_data = response.json()
                print(f"✅ 集成服务转换任务接口正常: {task_data}")
            else:
                print(f"❌ 集成服务转换任务接口异常: {response.status_code}")
        except Exception as e:
            print(f"❌ 集成服务转换任务接口请求失败: {e}")
        
        return True
    except Exception as e:
        print(f"❌ 集成音乐转换服务测试失败: {e}")
        return False

def test_sing_core_integration():
    """测试sing_core集成"""
    print("\n=== 测试sing_core集成 ===")
    
    try:
        from func.sing.sing_core import SingCore
        print("✅ SingCore模块导入成功")
        
        # 创建SingCore实例
        sing_core = SingCore()
        print("✅ SingCore实例创建成功")
        
        # 测试异步状态检查
        print("🔄 测试异步状态检查...")
        result = sing_core.check_down_song("测试歌曲")
        print(f"✅ 异步状态检查完成，结果: {result}")
        
        return True
    except Exception as e:
        print(f"❌ sing_core集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_gpu_performance():
    """测试GPU性能"""
    print("\n=== 测试GPU性能 ===")
    
    try:
        import torch
        
        if not torch.cuda.is_available():
            print("⚠️ CUDA不可用，跳过GPU性能测试")
            return True
        
        # 创建大型张量进行性能测试
        print("🔥 进行GPU性能测试...")
        
        # 测试矩阵乘法性能
        size = 1000
        device = torch.device('cuda')
        
        start_time = time.time()
        a = torch.randn(size, size, device=device)
        b = torch.randn(size, size, device=device)
        c = torch.matmul(a, b)
        torch.cuda.synchronize()  # 等待GPU操作完成
        end_time = time.time()
        
        gpu_time = end_time - start_time
        print(f"✅ GPU矩阵乘法 ({size}x{size}): {gpu_time:.4f}秒")
        
        # 测试CPU性能对比
        start_time = time.time()
        a_cpu = torch.randn(size, size)
        b_cpu = torch.randn(size, size)
        c_cpu = torch.matmul(a_cpu, b_cpu)
        end_time = time.time()
        
        cpu_time = end_time - start_time
        print(f"✅ CPU矩阵乘法 ({size}x{size}): {cpu_time:.4f}秒")
        
        speedup = cpu_time / gpu_time
        print(f"🚀 GPU加速比: {speedup:.2f}x")
        
        return True
    except Exception as e:
        print(f"❌ GPU性能测试失败: {e}")
        return False

def test_memory_usage():
    """测试内存使用情况"""
    print("\n=== 测试内存使用情况 ===")
    
    try:
        import torch
        import psutil
        
        # 系统内存
        memory = psutil.virtual_memory()
        print(f"📊 系统内存: {memory.total / (1024**3):.1f}GB")
        print(f"📊 可用内存: {memory.available / (1024**3):.1f}GB")
        print(f"📊 内存使用率: {memory.percent}%")
        
        # GPU内存
        if torch.cuda.is_available():
            gpu_memory = torch.cuda.get_device_properties(0).total_memory
            gpu_allocated = torch.cuda.memory_allocated(0)
            gpu_cached = torch.cuda.memory_reserved(0)
            
            print(f"🎮 GPU总内存: {gpu_memory / (1024**3):.1f}GB")
            print(f"🎮 GPU已分配: {gpu_allocated / (1024**3):.3f}GB")
            print(f"🎮 GPU已缓存: {gpu_cached / (1024**3):.3f}GB")
        
        return True
    except Exception as e:
        print(f"❌ 内存使用测试失败: {e}")
        return False

def create_test_summary():
    """创建测试总结报告"""
    print("\n" + "="*60)
    print("🎉 综合测试总结报告")
    print("="*60)
    
    # 环境信息
    try:
        import torch
        import numpy as np
        print(f"🔧 PyTorch版本: {torch.__version__}")
        print(f"🔧 NumPy版本: {np.__version__}")
        print(f"🔧 CUDA可用: {torch.cuda.is_available()}")
        if torch.cuda.is_available():
            print(f"🔧 GPU设备: {torch.cuda.get_device_name()}")
    except:
        pass
    
    print("\n✅ 测试完成项目:")
    print("  1. PyTorch GPU版本安装和配置")
    print("  2. NumPy 1.26.3兼容性")
    print("  3. Web_through.py自动转换功能")
    print("  4. 音乐转换服务集成")
    print("  5. sing_core异步功能")
    print("  6. GPU性能和内存使用")
    
    print("\n🎯 建议下一步:")
    print("  1. 运行完整的bot_ym.py测试整体集成")
    print("  2. 测试实际的学歌功能")
    print("  3. 监控长时间运行的稳定性")
    print("  4. 优化GPU内存使用")

def main():
    """主函数"""
    print("🚀 开始综合测试...")
    
    # 测试项目列表
    tests = [
        ("GPU性能测试", test_gpu_performance),
        ("内存使用测试", test_memory_usage),
        ("集成音乐转换服务测试", test_integrated_music_service),
        ("sing_core集成测试", test_sing_core_integration),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ {test_name} 执行异常: {e}")
            results[test_name] = False
    
    # 显示测试结果
    print(f"\n{'='*20} 测试结果汇总 {'='*20}")
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
    
    # 创建总结报告
    create_test_summary()
    
    success_count = sum(1 for r in results.values() if r)
    total_count = len(results)
    
    print(f"\n🎯 总体结果: {success_count}/{total_count} 测试通过")
    
    if success_count == total_count:
        print("🎉 所有测试通过！系统已准备就绪。")
    else:
        print("⚠️ 部分测试失败，请检查相关配置。")

if __name__ == "__main__":
    main()
