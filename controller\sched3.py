from flask_apscheduler import APScheduler
from sympy import true
from func.draw.draw_core import DrawCore
from func.search.search_core import SearchCore
from func.sing.sing_core import SingCore
from func.tts.tts_core import TTsCore
from func.dance.dance_core import DanceCore
from func.image.image_core import ImageCore
from func.llm.llm_core import LLmCore
from func.vtuber.action_oper import ActionOper
from func.obs.obs_init import ObsInit
from func.task.idle import IdleCore
from func.tools.common_websocket import CommonWebsocket

import asyncio.coroutines

# from func.tools.singleton_mode import singleton

# @singleton
class sched(object):

    scheduler = APScheduler()

    # self.drawCore = DrawCore()
    # self.searchCore = SearchCore()
    # self.singCore = SingCore()
    # self.ttsCore = TTsCore()
    # self.danceCore = DanceCore()
    # self.imageCore = ImageCore()
    # self.llmCore = LLmCore()
    # self.actionOper = ActionOper()
    # self.idleCore = IdleCore()
    # self.commonWebsocket = CommonWebsocket()

    def __init__(self):

        print("sched--------init----------")

        self.scheduler = APScheduler()

        # ============= OBS直播软件控制 ================
        # obs直播软件连接
        obs = ObsInit().get_ws()

        # 初始化各个核心组件
        self.drawCore = DrawCore()
        self.searchCore = SearchCore()
        self.singCore = SingCore()
        self.ttsCore = TTsCore()
        self.danceCore = DanceCore()
        self.imageCore = ImageCore()
        self.llmCore = LLmCore()
        self.actionOper = ActionOper()
        self.idleCore = IdleCore()
        self.commonWebsocket = CommonWebsocket()
        
    def bind_sched(self, app):

        print("bind_sched--------------")
        
        """绑定调度任务到Flask应用"""
        self.scheduler.init_app(app) # init_app(app)

        # 定时任务
        self._add_check_jobs()
        
        self.scheduler.start()
        
    def _add_check_jobs(self):
        """添加各种检查任务"""
        # check_jobs = [
        #     ('check_text_search', self.searchCore.check_text_search),#
        #     ('check_tts_play', self.ttsCore.check_tts_play),#
        #     # ('check_scene_time', self.actionOper.check_scene_time),#
        #     ('check_sing', self.singCore.check_sing),#
        #     # ('check_dance', self.danceCore.check_dance),#
        #     ('check_answer', self.llmCore.check_answer),
        #     ('check_welcome_room', self.llmCore.check_welcome_room),
        #     ('check_img_search', self.imageCore.check_img_search),
        #     ('check_tts', self.ttsCore.check_tts),
        #     ('check_playSongMenuList', self.singCore.check_playSongMenuList),
        #     ('rnd_idle_task', self.idleCore.rnd_idle_task)
        # ]
        
        # for job_id, job_func in check_jobs:
        #     print(f"Adding job: {job_id}")
        #     self.scheduler.add_job(
        #         id=job_id,
        #         func=self.job_func,
        #         trigger='interval',
        #         seconds=30,
        #         max_instances=1
        #     )

        #1 LLM回复
        self.scheduler.add_job(func=self.llmCore.check_answer, trigger="interval", seconds=1, id="answer", max_instances=100)
        #2 tts语音合成
        self.scheduler.add_job(func=self.ttsCore.check_tts, trigger="interval", seconds=1, id="tts", max_instances=1000)

        self.scheduler.add_job(func=self.ttsCore.check_tts_play, trigger="interval", seconds=1, id="tts0", max_instances=1000)

        #3 绘画
        self.scheduler.add_job(func=self.drawCore.check_draw, trigger="interval", seconds=1, id="draw", max_instances=50)
        #4 搜索资料
        self.scheduler.add_job(func=self.searchCore.check_text_search, trigger="interval", seconds=1, id="text_search", max_instances=50)
        #5 搜图
        self.scheduler.add_job(func=self.imageCore.check_img_search, trigger="interval", seconds=1, id="img_search", max_instances=50)
        #6 唱歌转换
        self.scheduler.add_job(func=self.singCore.check_sing, trigger="interval", seconds=1, id="sing", max_instances=50)
        #7 歌曲清单播放
        self.scheduler.add_job(func=self.singCore.check_playSongMenuList, trigger="interval", seconds=1, id="playSongMenuList", max_instances=50)
        #8 跳舞
        self.scheduler.add_job(func=self.danceCore.check_dance, args=[self.scheduler], trigger="interval", seconds=1, id="dance", max_instances=10)
        #9 时间判断场景[白天黑夜切换]
        self.scheduler.add_job(func=self.actionOper.check_scene_time, trigger="cron", hour="6,17,18", id="scene_time")
        #10 欢迎语
        self.scheduler.add_job(func=self.llmCore.check_welcome_room, trigger="interval", seconds=20, id="welcome_room", max_instances=50)

    
    # def check_draw(self):
    #     """检查是否有待处理的绘画任务"""
    #     try:
    #         self.drawCore.process_draw_tasks()
    #     except Exception as e:
    #         print(f"Error in check_draw: {e}")

    # def check_text_search(self):
    #     """检查文本搜索请求"""
    #     try:
    #         self.searchCore.process_text_search()
    #     except Exception as e:
    #         print(f"Error in check_text_search: {e}")

    # def check_tts_play(self):
    #     """检查TTS播放队列"""
    #     try:
    #         self.ttsCore.process_play_queue()
    #     except Exception as e:
    #         print(f"Error in check_tts_play: {e}")

    # def check_scene_time(self):
    #     """检查场景时间，可能用于切换不同时段的场景"""
    #     try:
    #         self.actionOper.check_and_switch_scene()
    #     except Exception as e:
    #         print(f"Error in check_scene_time: {e}")

    # def check_sing(self):
    #     """检查歌唱请求"""
    #     try:
    #         self.singCore.process_sing_requests()
    #     except Exception as e:
    #         print(f"Error in check_sing: {e}")

    # def check_dance(self):
    #     """检查舞蹈动作请求"""
    #     try:
    #         self.danceCore.process_dance_requests()
    #     except Exception as e:
    #         print(f"Error in check_dance: {e}")

    # def check_answer(self):
    #     """检查需要回答的问题"""
    #     try:
    #         self.llmCore.process_pending_questions()
    #     except Exception as e:
    #         print(f"Error in check_answer: {e}")

    # def check_welcome_room(self):
    #     """检查房间欢迎消息"""
    #     try:
    #         self.commonWebsocket.process_welcome_messages()
    #     except Exception as e:
    #         print(f"Error in check_welcome_room: {e}")

    # def check_img_search(self):
    #     """检查图片搜索请求"""
    #     try:
    #         self.imageCore.process_image_search()
    #     except Exception as e:
    #         print(f"Error in check_img_search: {e}")

    # def check_tts(self):
    #     """检查TTS转换请求"""
    #     try:
    #         self.ttsCore.process_tts_requests()
    #     except Exception as e:
    #         print(f"Error in check_tts: {e}")

    # def check_playSongMenuList(self):
    #     """检查歌单播放列表"""
    #     try:
    #         self.singCore.process_song_menu_list()
    #     except Exception as e:
    #         print(f"Error in check_playSongMenuList: {e}")

    # def rnd_idle_task(self):
    #     """执行随机空闲任务"""
    #     try:
    #         self.idleCore.execute_random_idle_action()
    #     except Exception as e:
    #         print(f"Error in rnd_idle_task: {e}")

# 创建sched实例
sched = sched()