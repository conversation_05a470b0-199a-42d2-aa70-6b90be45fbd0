#coding=UTF-8
"""
音乐转换服务模块
整合Web_through.py的功能，提供音乐转换服务
"""
import glob
import os
import threading
import time
from flask import Flask, jsonify, send_file, abort
from func.log.default_log import DefaultLog
from func.tools.singleton_mode import singleton

# 尝试导入AutoConvertMusic，如果失败则提供错误信息
try:
    from AutoConvertMusic import convert_music
    AUTOCONVERT_AVAILABLE = True
except ImportError as e:
    print(f"警告: AutoConvertMusic导入失败: {e}")
    print("请确保安装了所有必要的依赖，包括selenium等")
    AUTOCONVERT_AVAILABLE = False
    convert_music = None

@singleton
class MusicConvertService:
    """音乐转换服务类"""
    
    def __init__(self):
        self.log = DefaultLog().getLogger()
        self.app = None
        self.server_thread = None
        self.is_running = False
        
        # SVC配置 - 从Web_through.py迁移
        self.svc_config = {
            "model_path": r"sovits4.1\logs\草神\caoshen1_57000.pth",
            "config_path": r"sovits4.1\logs\草神\config.json",
            "cluster_model_path": r"sovits4.1\logs\yinmei\kmeans_10000.pt",
            "cluster_infer_ratio": 0,
            "diffusion_model_path": r"sovits4.1\logs\yinmei\diffusion\model_44000.pt",
            "diffusion_config_path": r"sovits4.1\logs\yinmei\diffusion\config.yaml"
        }
        
        # 音乐平台选择
        self.choose_music_platform = ["kugou", "netease", "bilibili", "youtube"]
        
        # 默认任务配置
        self.default_task_dict = {'ms':'bs-roformer-1296','vr1':'6-HP','vr2': 'De-Echo-Normal'}
        
        # 初始化音乐转换模块
        self.music_module = None
        self.speaker = "草神"
        
        self._init_music_module()
        self._init_flask_app()
    
    def _init_music_module(self):
        """初始化音乐转换模块"""
        if not AUTOCONVERT_AVAILABLE:
            self.log.error("AutoConvertMusic模块不可用，请检查依赖")
            self.music_module = None
            return

        try:
            self.music_module = convert_music(
                music_platform=self.choose_music_platform[0],
                svc_config=self.svc_config,
                default_task_dict=self.default_task_dict
            )
            self.log.info("音乐转换模块初始化成功")
        except Exception as e:
            self.log.error(f"音乐转换模块初始化失败: {e}")
            self.music_module = None
    
    def _init_flask_app(self):
        """初始化Flask应用"""
        self.app = Flask(__name__)
        
        # 注册路由
        self.app.add_url_rule('/status', 'get_status', self.get_status, methods=['GET'])
        self.app.add_url_rule('/append_song/<song_name>', 'convert_task', self.convert_task, methods=['GET'])
        self.app.add_url_rule('/get_audio/<song_name>', 'get_audio', self.get_audio, methods=['GET'])
        
        self.log.info("Flask应用初始化成功")
    
    def get_status(self):
        """获取转换状态"""
        try:
            speaker1 = self.speaker.replace("[中]", "[[]中[]]")
            file_list = glob.glob(f"output/*/*[!Vocals]*_{speaker1}.wav")
            file_name = []
            for f in file_list:
                filename = os.path.basename(f)
                file_name.append(filename.replace(f"_{self.speaker}.wav", ""))

            # 返回converting和converted的状态
            return jsonify({
                'converting': self.music_module.converting if self.music_module else [],
                'converted': self.music_module.converted if self.music_module else [],
                'convertfail': self.music_module.convertfail if self.music_module else [],
                'converted_file': file_name
            })
        except Exception as e:
            self.log.error(f"获取状态失败: {e}")
            return jsonify({'error': str(e)}), 500
    
    def convert_task(self, song_name):
        """转换任务接口"""
        try:
            if not self.music_module:
                return jsonify({"status": "error", "message": "音乐转换模块未初始化"}), 500
                
            status, song_name = self.music_module.add_conversion_task(
                music_info=song_name, 
                speaker=self.speaker
            )
            return jsonify({"status": status, "songName": song_name})
        except Exception as e:
            self.log.error(f"转换任务失败: {e}")
            return jsonify({"status": "error", "message": str(e)}), 500
    
    def get_audio(self, song_name):
        """获取音频文件"""
        try:
            search_pattern = os.path.join(f"output/{song_name}/{song_name}*.wav")
            file_list = glob.glob(search_pattern)
            if len(file_list) > 0:
                matching_files = file_list[0]
                try:
                    return send_file(matching_files, as_attachment=False)
                except Exception as e:
                    self.log.error(f"发送文件失败: {e}")
                    abort(404, description="Audio file not found")
            else:
                abort(404, description="Audio file not found")
        except Exception as e:
            self.log.error(f"获取音频文件失败: {e}")
            abort(500, description="Internal server error")
    
    def start_service(self, host="127.0.0.1", port=17171):
        """启动服务"""
        if self.is_running:
            self.log.warning("音乐转换服务已经在运行")
            return
        
        def run_server():
            try:
                self.log.info(f"启动音乐转换服务: {host}:{port}")
                self.app.run(host=host, port=port, debug=False, use_reloader=False)
            except Exception as e:
                self.log.error(f"音乐转换服务启动失败: {e}")
        
        self.server_thread = threading.Thread(target=run_server, daemon=True)
        self.server_thread.start()
        self.is_running = True
        self.log.info("音乐转换服务线程已启动")
    
    def stop_service(self):
        """停止服务"""
        if not self.is_running:
            return
        
        self.is_running = False
        self.log.info("音乐转换服务已停止")
    
    def get_service_url(self, host="127.0.0.1", port=17171):
        """获取服务URL"""
        return f"http://{host}:{port}"

# 全局实例
music_convert_service = MusicConvertService()
