J:\ai\�������\ai-yinmei\func\task\idle.c       idle.py __init__        idle.IdleCore.__init__  rnd_idle_task   idle.IdleCore.rnd_idle_task idle        Interpreter change detected - this module can only be loaded into one interpreter per process.  name    __loader__  loader      __file__    origin      __package__ parent      __path__        submodule_search_locations      Module 'idle' has already been imported. Re-initialisation is not supported.    builtins        cython_runtime  __builtins__    init idle       %s() got multiple values for keyword argument '%U'      %.200s() keywords must be strings       %s() got an unexpected keyword argument '%U'    exactly %.200s() takes %.8s %zd positional argument%.1s (%zd given)      while calling a Python object  NULL result without error in PyObject_Call      name '%U' is not defined        cannot fit '%.200s' into an index-sized integer '%.200s' object is not subscriptable    cannot import name %S   _cython_3_0_11  Shared Cython type %.200s is not a type object  Shared Cython type %.200s has the wrong size, try recompiling   keywords must be strings        __name__ must be set to a string object __qualname__ must be set to a string object     function's dictionary may not be deleted        setting function's dictionary to a non-dict     __defaults__ must be set to a tuple object      changes to cyfunction.__defaults__ will not currently affect the values used in function calls  __kwdefaults__ must be set to a dict object     changes to cyfunction.__kwdefaults__ will not currently affect the values used in function calls        __annotations__ must be set to a dict object    func_doc        __doc__ func_name       __name__        __qualname__    func_dict       __dict__        func_globals    __globals__     func_closure    __closure__     func_code       __code__        func_defaults   __defaults__    __kwdefaults__  __annotations__ _is_coroutine   __module__      __reduce__      Bad call flags for CyFunction   <cyfunction %U at %p>   %.200s() takes no arguments (%zd given) %.200s() takes exactly one argument (%zd given) %.200s() takes no keyword arguments     unbound method %.200S() needs an argument       %.200s() needs an argument      _cython_3_0_11.cython_function_or_method        %s (%s:%d)      does not match          compile time Python version %d.%d of module '%.100s' %s runtime version %d.%d   requests        find_user       func.score.oper_score           给大家说一个新的段子。提示：要换一个新的段子  info    other       Ai_Name cline_in_traceback      LLmData func.user.user_db       IdleData        func.tools.singleton_mode       SingData        x� �   querys_rnd      AnswerList      __prepare__     执行闲置任务:     CommonData      defaultConfig   func.gobal.data 闲置  task_rnd        SearchData      J:\ai\吟美打包\ai-yinmei\func\task\idle.py  BiliveApi       __main__        __qualname__    rs      func.config.default_config      __module__  self        username        msg_deal_score_rank     __dict__        H� �   _initializing   prompt  {Ai_Name}       llm_json    ?   IdleCore.rnd_idle_task  random  switch  BiliDanmakuData func.http.bilive        document        _is_coroutine   super   is_ai_ready     __spec__    intent      task_list   uuid4       drawData        userface        __import__  config      DanceData   query   log idle_time       __doc__ __class_getitem__       __set_name__    rnd_idle_task   imageData       data_rnd        给大家说一个新的笑话。提示：要换一个新的笑话  end uid     func.log.default_log    is_drawing      replace name    is_singing  *   DefaultLog      __init_subclass__       biliveApi       llmData danceData       singData        ImageData       get_config  .   idle    limit_time      channel data    text    __test__        IdleCore.__init__       voiceType       DrawData    播报  querys      commonData  item        broadcast_list  uface   put     is_SearchText   UserDB  chatStatus      QuestionList    AutoChange  openId      __metaclass__   task_name   uuid        randrange       queryContributionRank   getLogger       operScore       question        lanuage task    IdleCore        __init__        traceid 触发闲置任务      jsonStr 排行榜       asyncio.coroutines      __name__        searchData      is_dance    积分      biliDanmakuData singleton   聊天  userDB      is_SearchImg    room_id room_uid        idleData        OperScore