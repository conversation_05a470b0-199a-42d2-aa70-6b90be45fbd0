#coding=UTF-8
from hashlib import md5
import os
import re
import time
from urllib import parse
import requests
import json
from bs4 import BeautifulSoup

class Kugou_music():
    def __init__(self, address="http://localhost:3001"):
        self.kugou = requests.session()
        self.headers = {'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) '
                                      'AppleWebKit/537.36 (KHTML, like Gecko) Chrome/71.0.3578.98 Safari/537.36',
                        # 'cookies':"kg_mid=02926a3891e6ae4d9c2b50b1687af72d; kg_dfid=3f0Lfi0SSmUE3A4e6p4Gzzbk; Hm_lvt_aedee6983d4cfc62f509129360d6bb3d=**********; HMACCOUNT=13BE1998A5AFB016; CheckCode=czozMjoiNzI2OWIwNTAwN2FlNGQ1NmUyNGI5YzkyOTA2Yzk4NGUiOw%3D%3D; kg_dfid_collect=d41d8cd98f00b204e9800998ecf8427e; KuGoo=KugooID=*********&KugooPwd=9DC5CDEB9EFEB426962518982AFCF333&NickName=%u0020%u0020%u0020%u0020%u0020%u0020%u0020%u0020%u0020%u0020%u0020%u0020%u0020%u0020%u0020%u0020%u4e00%u6ef4%u6c34&Pic=http://imge.kugou.com/kugouicon/165/********/********160157424839.jpg&RegState=1&RegFrom=&t=32b4852b84d3af2d93b168da0248e03b8aecae0d7bf076ea70b319b633124f67&a_id=1014&ct=**********&UserName=%u006b%u0067%u006f%u0070%u0065%u006e%u0039%u0037%u0032%u0031%u0030%u0038%u0036%u0033%u0032&t1=; KugooID=*********; t=32b4852b84d3af2d93b168da0248e03b8aecae0d7bf076ea70b319b633124f67; a_id=1014; UserName=kgopen*********; mid=02926a3891e6ae4d9c2b50b1687af72d; dfid=3f0Lfi0SSmUE3A4e6p4Gzzbk; ACK_SERVER_10015=%7B%22list%22%3A%5B%5B%22bjlogin-user.kugou.com%22%5D%5D%7D; Hm_lpvt_aedee6983d4cfc62f509129360d6bb3d=**********"
                                      }
        self.address = address
        try:
            with open('kugou.txt', 'r') as f:
                pass
            self.load_cookie()
            print("已登录酷狗账号")
        except:
            print("请先登录酷狗账号")
            self.log_in()
    def load_cookie(self):
        with open('kugou-all.txt') as f:
            cookie = json.loads(f.read())
            self.kugou.cookies.update(cookie)

    def save_cookie(self):
        with open('kugou.txt','w') as f:
            cookie=json.dumps(self.kugou.cookies.get_dict())
            f.write(cookie)
    
    def search_music(self, keywords, limit=10, return_list=False):
        """
        搜索音乐，支持 "歌名" 或 "歌手 - 歌名" 格式
        返回:
        - return_list=False: (hash, name, artist) 或 (0, "", "") 表示未找到
        - return_list=True: 返回完整的搜索结果列表，按匹配度和URL可用性排序
        """
        try:
            # 使用与download_music相同的搜索方式
            search_url = self.MD5Encrypt(keywords)
            search_text = self.get_html(search_url)

            if search_text == '请求异常':
                return [] if return_list else (0, "", "")

            hash_list = self.parse_text(search_text[12:-2])

            if not hash_list:
                return [] if return_list else (0, "", "")

            # 计算匹配度并检查URL可用性
            scored_songs = []
            for hash_item in hash_list[:limit]:  # 限制处理数量
                # hash_item格式: [file_hash, hq_file_hash, sq_file_hash, album_id, song_name, singer_name, mix_song_id]
                if len(hash_item) >= 6:
                    song_name = hash_item[4] if hash_item[4] else ""
                    artist_name = hash_item[5] if hash_item[5] else ""
                    mix_song_id = hash_item[6] if len(hash_item) > 6 and hash_item[6] else ""
                    file_hash = hash_item[2] if hash_item[2] else hash_item[0]  # 优先使用SQ，然后普通

                    if file_hash and song_name:
                        # 计算匹配度分数
                        match_score = self._calculate_match_score(keywords, song_name, artist_name)

                        # 检查URL可用性
                        url_score, has_backup_url = self._check_url_availability(file_hash)

                        # 综合评分：匹配度 + URL可用性加分
                        total_score = match_score + url_score

                        scored_songs.append({
                            'total_score': total_score,
                            'match_score': match_score,
                            'url_score': url_score,
                            'has_backup_url': has_backup_url,
                            'file_hash': file_hash,
                            'song_name': song_name,
                            'artist_name': artist_name,
                            'mix_song_id': mix_song_id,
                            'hash_item': hash_item
                        })

            if not scored_songs:
                return [] if return_list else (0, "", "")

            # 按综合评分降序排序（优先backupUrl，然后匹配度）
            scored_songs.sort(key=lambda x: (x['has_backup_url'], x['total_score']), reverse=True)

            if return_list:
                return scored_songs
            else:
                # 返回评分最高的歌曲
                best_match = scored_songs[0]
                return best_match['file_hash'], best_match['song_name'], best_match['artist_name']

        except Exception as e:
            print(f"搜索音乐异常: {e}")
            return [] if return_list else (0, "", "")

    def _check_url_availability(self, file_hash):
        """
        检查歌曲URL的可用性
        返回: (url_score, has_backup_url)
        """
        try:
            response = self.kugou.get(self.address + f"/song/url?hash={file_hash}", timeout=3).text
            song_data = json.loads(response)

            # 检查是否有backupUrl（VIP用户优先）
            if 'backupUrl' in song_data and song_data['backupUrl']:
                return 100, True  # 最高优先级
            elif 'url' in song_data and song_data['url']:
                return 50, False  # 中等优先级
            elif 'data' in song_data and song_data['data'] and 'url' in song_data['data']:
                return 30, False  # 较低优先级
            else:
                return 0, False   # 无可用URL

        except Exception as e:
            print(f"检查URL可用性失败 {file_hash}: {e}")
            return 0, False

    def _calculate_match_score(self, keywords, song_name, artist_name):
        """
        计算搜索关键词与歌曲的匹配度分数
        支持多种搜索格式：
        1. 仅歌名：稻香
        2. 歌手-歌名：周杰伦-稻香 或 周杰伦 - 稻香
        3. 歌名 歌手：稻香 周杰伦
        4. 复杂格式：蓝光乐队 - 轻舟(dj阿卓版)
        """
        score = 0
        keywords_lower = keywords.lower().strip()
        song_name_lower = song_name.lower().strip()
        artist_name_lower = artist_name.lower().strip()

        # 预处理：统一分隔符
        keywords_normalized = keywords_lower.replace('、', '-').replace('，', '-').replace(',', '-')

        # 完全匹配歌名得最高分
        if keywords_lower == song_name_lower:
            score += 100
        elif keywords_lower in song_name_lower:
            score += 80
        elif song_name_lower in keywords_lower:
            score += 70

        # 解析搜索关键词的不同格式
        search_artist = ""
        search_song = ""

        # 格式1: 歌手-歌名 或 歌手 - 歌名
        if ' - ' in keywords_normalized or '-' in keywords_normalized:
            parts = keywords_normalized.replace(' - ', '-').split('-', 1)  # 只分割第一个-
            if len(parts) >= 2:
                search_artist = parts[0].strip()
                search_song = parts[1].strip()
        # 格式2: 歌名 歌手 (空格分隔，且歌手在后面)
        elif ' ' in keywords_lower and len(keywords_lower.split()) == 2:
            parts = keywords_lower.split()
            # 尝试判断哪个是歌手哪个是歌名
            if parts[1] in artist_name_lower or artist_name_lower in parts[1]:
                search_song = parts[0]
                search_artist = parts[1]
            elif parts[0] in artist_name_lower or artist_name_lower in parts[0]:
                search_artist = parts[0]
                search_song = parts[1]
            else:
                # 默认第一个是歌名，第二个是歌手
                search_song = parts[0]
                search_artist = parts[1]
        else:
            # 格式3: 仅歌名
            search_song = keywords_lower

        # 歌手匹配评分
        if search_artist:
            if search_artist == artist_name_lower:
                score += 50
            elif search_artist in artist_name_lower or artist_name_lower in search_artist:
                score += 30
            # 处理多歌手情况（如"蓝光乐队、孟西"）
            elif '、' in artist_name_lower or ',' in artist_name_lower:
                artist_parts = artist_name_lower.replace('、', ',').split(',')
                for artist_part in artist_parts:
                    artist_part = artist_part.strip()
                    if search_artist == artist_part or search_artist in artist_part:
                        score += 35
                        break

        # 歌名匹配评分
        if search_song:
            if search_song == song_name_lower:
                score += 50
            elif search_song in song_name_lower:
                score += 40
            elif song_name_lower in search_song:
                score += 35
            # 处理括号内容（如"轻舟(dj阿卓版)"）
            elif '(' in song_name_lower or '（' in song_name_lower:
                # 提取括号前的主要歌名
                main_song = re.sub(r'[（(].*?[）)]', '', song_name_lower).strip()
                if search_song == main_song or search_song in main_song:
                    score += 30

        # 如果没有明确的歌手信息，检查关键词是否匹配歌手
        if not search_artist and keywords_lower in artist_name_lower:
            score += 20

        # 字符相似度加分
        common_chars = set(keywords_lower) & set(song_name_lower)
        score += len(common_chars) * 2

        # 如果歌手和歌名都匹配，额外加分
        if search_artist and search_song:
            if (search_artist in artist_name_lower or artist_name_lower in search_artist) and \
               (search_song in song_name_lower or song_name_lower in search_song):
                score += 20

        return score

    def _try_alternative_api(self, mix_song_id, song_name, artist_name):
        """
        第二个方案：尝试使用替代API获取歌曲下载链接
        参数:
            mix_song_id: 歌曲的MixSongID
            song_name: 歌曲名（用于日志）
            artist_name: 歌手名（用于日志）
        返回:
            download_url: 歌曲下载链接，失败返回None
        """
        try:
            print(f"  尝试使用替代API获取下载链接 (MixSongID: {mix_song_id})")

            # 方法1: 尝试使用MixSongID构造不同的API请求
            alternative_apis = [
                f"https://wwwapi.kugou.com/yy/index.php?r=play/getdata&hash={mix_song_id}",
                f"https://www.kugou.com/yy/index.php?r=play/getdata&hash={mix_song_id}",
                f"https://m.kugou.com/app/i/getSongInfo.php?cmd=playInfo&hash={mix_song_id}",
            ]

            for api_url in alternative_apis:
                try:
                    print(f"    尝试API: {api_url}")
                    response = self.kugou.get(api_url, headers=self.headers, timeout=5)
                    response.raise_for_status()

                    # 尝试解析JSON响应
                    try:
                        data = json.loads(response.text)

                        # 查找可能的音频URL字段
                        url_fields = ['url', 'play_url', 'audio_url', 'src', 'play_backup_url']
                        for field in url_fields:
                            if field in data and data[field]:
                                url = data[field]
                                if isinstance(url, list) and len(url) > 0:
                                    url = url[0]
                                if isinstance(url, str) and url.startswith(('http://', 'https://')):
                                    print(f"    ✅ 从API获取到下载链接: {url[:100]}...")
                                    return url

                        # 检查嵌套的data字段
                        if 'data' in data and isinstance(data['data'], dict):
                            for field in url_fields:
                                if field in data['data'] and data['data'][field]:
                                    url = data['data'][field]
                                    if isinstance(url, list) and len(url) > 0:
                                        url = url[0]
                                    if isinstance(url, str) and url.startswith(('http://', 'https://')):
                                        print(f"    ✅ 从API data字段获取到下载链接: {url[:100]}...")
                                        return url

                    except json.JSONDecodeError:
                        # 如果不是JSON，尝试从文本中提取URL
                        url_pattern = r'https?://[^\s"\'<>]*\.(?:mp3|flac|m4a)(?:\?[^\s"\'<>]*)?'
                        matches = re.findall(url_pattern, response.text, re.IGNORECASE)
                        if matches:
                            url = matches[0]
                            print(f"    ✅ 从API文本响应获取到下载链接: {url[:100]}...")
                            return url

                except Exception as api_e:
                    print(f"    ❌ API请求失败: {api_e}")
                    continue

            # 方法2: 尝试使用歌曲名和歌手名进行其他搜索
            print(f"  尝试使用歌曲信息进行备用搜索...")
            try:
                # 构造更精确的搜索关键词
                search_keyword = f"{artist_name} {song_name}".strip()
                if search_keyword:
                    # 使用现有的搜索API，但限制结果数量
                    search_url = self.MD5Encrypt(search_keyword)
                    search_response = self.get_html(search_url)

                    if search_response != '请求异常':
                        hash_list = self.parse_text(search_response[12:-2])

                        # 查找完全匹配的结果
                        for hash_item in hash_list[:3]:  # 只检查前3个结果
                            if len(hash_item) >= 6:
                                item_song = hash_item[4] if hash_item[4] else ""
                                item_artist = hash_item[5] if hash_item[5] else ""
                                item_hash = hash_item[2] if hash_item[2] else hash_item[0]

                                # 检查是否完全匹配
                                if (item_song.lower().strip() == song_name.lower().strip() and
                                    item_artist.lower().strip() == artist_name.lower().strip()):

                                    # 尝试获取这个hash的URL
                                    try:
                                        url_response = self.kugou.get(self.address + f"/song/url?hash={item_hash}", timeout=3).text
                                        url_data = json.loads(url_response)

                                        if 'url' in url_data and url_data['url']:
                                            url = url_data['url'][0] if isinstance(url_data['url'], list) else url_data['url']
                                            print(f"    ✅ 从备用搜索获取到下载链接: {url[:100]}...")
                                            return url

                                    except Exception:
                                        continue

            except Exception as search_e:
                print(f"    ❌ 备用搜索失败: {search_e}")

            print(f"  ⚠️ 所有替代方案都未找到可用的下载链接")
            return None

        except Exception as e:
            print(f"  ❌ 替代API方案失败: {e}")
            return None

    def _is_exact_match(self, keywords, song_name, artist_name):
        """
        判断搜索关键词与歌曲信息是否完全匹配
        只有在歌名和歌手都完全匹配的情况下才返回True
        """
        try:
            keywords_lower = keywords.lower().strip()
            song_name_lower = song_name.lower().strip()
            artist_name_lower = artist_name.lower().strip()

            # 预处理：统一分隔符和清理HTML标签
            keywords_normalized = keywords_lower.replace('、', '-').replace('，', '-').replace(',', '-')
            song_name_clean = re.sub(r'<[^>]+>', '', song_name_lower)
            artist_name_clean = re.sub(r'<[^>]+>', '', artist_name_lower)

            # 解析搜索关键词的不同格式
            search_artist = ""
            search_song = ""

            # 格式1: 歌手-歌名 或 歌手 - 歌名
            if ' - ' in keywords_normalized or '-' in keywords_normalized:
                parts = keywords_normalized.replace(' - ', '-').split('-', 1)
                if len(parts) >= 2:
                    search_artist = parts[0].strip()
                    search_song = parts[1].strip()
            # 格式2: 歌名 歌手 (空格分隔)
            elif ' ' in keywords_lower and len(keywords_lower.split()) == 2:
                parts = keywords_lower.split()
                # 尝试判断哪个是歌手哪个是歌名
                if parts[1] in artist_name_clean or artist_name_clean in parts[1]:
                    search_song = parts[0]
                    search_artist = parts[1]
                elif parts[0] in artist_name_clean or artist_name_clean in parts[0]:
                    search_artist = parts[0]
                    search_song = parts[1]
                else:
                    # 如果无法判断，则不认为是完全匹配
                    return False
            else:
                # 格式3: 仅歌名 - 这种情况下不要求歌手匹配
                search_song = keywords_lower
                # 只检查歌名是否完全匹配
                return search_song == song_name_clean

            # 检查歌名和歌手是否都完全匹配
            song_match = search_song == song_name_clean
            artist_match = search_artist == artist_name_clean

            # 处理歌名中的括号内容（如"轻舟(dj阿卓版)"）
            # if not song_match and ('(' in song_name_clean or '（' in song_name_clean):
            #     main_song = re.sub(r'[（(].*?[）)]', '', song_name_clean).strip()
            #     song_match = search_song == main_song

            return song_match and artist_match

        except Exception as e:
            print(f"判断完全匹配时出错: {e}")
            return False

    def get_song_filename(self, keywords):
        """
        获取歌曲的标准化文件名（不包含扩展名）
        返回: (filename, full_song_name) 或 (None, None) 表示未找到
        """
        song_hash, song_name, artist_name = self.search_music(keywords)
        if song_hash == 0:
            return None, None

        # 清理文件名中的特殊字符
        clean_song_name = re.sub(r'[\[\]<>:"/\\|?*]', '_', song_name).rstrip('. ').replace('(', '_').replace(')', '').replace(' ', '')
        clean_artist_name = re.sub(r'[\[\]<>:"/\\|?*]', '_', artist_name).rstrip('. ').replace('(', '_').replace(')', '_').replace(' ', '')

        # 生成文件名
        if clean_artist_name:
            filename = f"{clean_artist_name}-{clean_song_name}"
            full_song_name = f"{artist_name}-{song_name}"
        else:
            filename = clean_song_name
            full_song_name = song_name

        return filename, full_song_name

    def search_download_music(self, keywords, level="exhigh"):
        """
        通过搜索下载音乐（备用方法，使用hash获取下载链接）
        """
        song_hash, name, artist = self.search_music(keywords)
        if song_hash == 0:
            raise Exception(f"未找到歌曲: {keywords}")

        if not os.path.exists('input'):
            os.mkdir('input')

        # song_hash = D792570A18CE4264876FE470E7448208
        
        # 使用hash获取歌曲URL
        response = self.kugou.get(self.address + f"/song/url?hash={song_hash}").text
        song_data = json.loads(response)

        # 尝试获取下载URL
        download_url = None
        if 'backupUrl' in song_data and song_data['backupUrl']:
            download_url = song_data['backupUrl'][0]
        elif 'url' in song_data and song_data['url']:
            download_url = song_data['url'][0] if isinstance(song_data['url'], list) else song_data['url']
        elif 'data' in song_data and song_data['data'] and 'url' in song_data['data']:
            download_url = song_data['data']['url']

        if not download_url:
            raise Exception("无法获取歌曲下载链接")

        song = self.kugou.get(download_url)

        # 确定文件扩展名
        if download_url.endswith('.mp3'):
            suffix = 'mp3'
        elif download_url.endswith('.flac'):
            suffix = 'flac'
        else:
            suffix = 'mp3'  # 默认mp3

        # 生成包含歌手信息的文件名
        clean_name = re.sub(r'[\[\]<>:"/\\|?*]', '_', name).rstrip('. ')
        clean_artist = re.sub(r'[\[\]<>:"/\\|?*]', '_', artist).rstrip('. ') if artist else ''

        if clean_artist:
            filename = f"{clean_artist}-{clean_name}.{suffix}"
        else:
            filename = f"{clean_name}.{suffix}"

        file_path = os.path.join('input', filename)

        with open(file_path, 'wb') as f:
            f.write(song.content)

        return f"{artist}-{name}" if artist else name, file_path

    def download_music(self, keywords, level="exhigh", search_results=None):
        """
        下载音乐主方法
        keywords: 搜索关键词，支持 "歌名" 或 "歌手 - 歌名" 格式
        search_results: 可选，预先搜索的结果列表，避免重复搜索
        """
        print(f"下载歌曲: {keywords}")

        # 如果没有提供搜索结果，则进行搜索
        if search_results is None:
            print(f"搜索歌曲: {keywords}")
            search_results = self.search_music(keywords, limit=10, return_list=True)

        if not search_results:
            print(f"未找到歌曲: {keywords}")
            raise Exception(f"未找到歌曲: {keywords}")

        if not os.path.exists('input'):
            os.mkdir('input')

        # 尝试获取歌曲URL和详细信息（使用已排序的搜索结果）
        song_url = None
        song_info = None
        used_result = None

        for i, result in enumerate(search_results[:5]):  # 尝试前5个结果
            file_hash = result['file_hash']
            mix_song_id = result.get('mix_song_id', '')

            try:
                print(f"尝试获取歌曲信息 (结果 {i+1}/{min(5, len(search_results))}): {result['song_name']} - {result['artist_name']}")
                print(f"  Hash: {file_hash}, MixSongID: {mix_song_id}, 匹配度: {result['match_score']}, URL分数: {result['url_score']}, 有BackupUrl: {result['has_backup_url']}")

                response = self.kugou.get(self.address + f"/song/url?hash={file_hash}").text
                song_data = json.loads(response)

                # 保存当前尝试的结果信息
                current_info = {
                    'song_name': result['song_name'],
                    'singer_name': result['artist_name'],
                    'hash': file_hash,
                    'mix_song_id': mix_song_id
                }

                # 优先获取基本信息，无论状态如何
                if not song_info:
                    song_info = current_info

                # 方案1：尝试获取下载URL（按优先级）
                if 'backupUrl' in song_data and song_data['backupUrl']:
                    song_url = song_data['backupUrl'][0]
                    used_result = result
                    print(f"✅ 成功获取URL (方案1-backupUrl) - {result['song_name']}")
                    break
                elif 'url' in song_data and song_data['url']:
                    song_url = song_data['url'][0] if isinstance(song_data['url'], list) else song_data['url']
                    used_result = result
                    print(f"✅ 成功获取URL (方案1-url) - {result['song_name']}")
                    break
                elif 'data' in song_data and song_data['data'] and 'url' in song_data['data']:
                    song_url = song_data['data']['url']
                    used_result = result
                    print(f"✅ 成功获取URL (方案1-data.url) - {result['song_name']}")
                    break
                else:
                    print(f"⚠️ 结果 {i+1} 方案1暂无可用URL (status: {song_data.get('status', 'unknown')}) - {result['song_name']}")

                    # 方案2：如果方案1失败且歌名和歌手完全匹配，尝试使用替代API
                    if mix_song_id and self._is_exact_match(keywords, result['song_name'], result['artist_name']):
                        print(f"  歌名和歌手完全匹配，尝试方案2：使用替代API获取下载链接")
                        alternative_url = self._try_alternative_api(mix_song_id, result['song_name'], result['artist_name'])
                        if alternative_url:
                            song_url = alternative_url
                            used_result = result
                            print(f"✅ 成功获取URL (方案2-替代API) - {result['song_name']}")
                            break

            except Exception as e:
                print(f"❌ 结果 {i+1} 请求失败: {e} - {result['song_name']}")
                continue

        if not song_url:
            raise Exception(f"无法获取到有效的音乐URL，已尝试 {min(5, len(search_results))} 个结果")

        # 下载音乐文件
        print(f"开始下载音乐文件...")
        song = self.kugou.get(song_url)

        # 生成文件名（使用实际下载的歌曲信息）
        if used_result:
            song_name = used_result['song_name']
            singer_name = used_result['artist_name']
            print(f"使用实际下载的歌曲信息: {singer_name} - {song_name}")
        elif song_info:
            song_name = song_info.get('song_name', keywords)
            singer_name = song_info.get('singer_name', '')
            print(f"使用备用歌曲信息: {singer_name} - {song_name}")
        else:
            song_name = keywords
            singer_name = ''
            print(f"使用搜索关键词作为歌曲名: {song_name}")

        # 清理文件名中的特殊字符，去除尖括号、引号、冒号、斜杠、问号、星号、句号等特殊字符
        # 左右圆括号也清除掉，左圆括号的地方用下划线代替
        # 去掉空格
        clean_song_name = re.sub(r'[\[\]<>:"/\\|?*]', '_', song_name).rstrip('. ').replace('(', '_').replace(')', '').replace(' ', '')
        clean_singer_name = re.sub(r'[\[\]<>:"/\\|?*]', '_', singer_name).rstrip('. ').replace('(', '_').replace(')', '_').replace(' ', '')

        # 确定文件扩展名
        if song_url.endswith('.mp3'):
            suffix = 'mp3'
        elif song_url.endswith('.flac'):
            suffix = 'flac'
        else:
            suffix = 'mp3'  # 默认mp3

        # 生成包含歌手信息的文件名
        if clean_singer_name:
            filename = f"{clean_singer_name}-{clean_song_name}.{suffix}"
            display_name = f"{clean_singer_name}-{clean_song_name}"
        else:
            filename = f"{clean_song_name}.{suffix}"
            display_name = clean_song_name

        file_path = os.path.join('input', filename)

        with open(file_path, 'wb') as f:
            f.write(song.content)

        print(f"✅ 下载完成: {filename}")
        return display_name, file_path

    def search_and_download_music(self, keywords, level="exhigh"):
        """
        搜索并下载音乐的组合方法
        优先下载有backupUrl的歌曲，确保歌曲名匹配
        """
        print(f"搜索并下载歌曲: {keywords}")

        # 先搜索，获取排序后的结果列表
        search_results = self.search_music(keywords, limit=10, return_list=True)

        if not search_results:
            raise Exception(f"未找到歌曲: {keywords}")

        print(f"找到 {len(search_results)} 个候选结果:")
        for i, result in enumerate(search_results[:5], 1):
            print(f"  {i}. {result['artist_name']} - {result['song_name']} "
                  f"(匹配度:{result['match_score']}, URL分数:{result['url_score']}, "
                  f"BackupUrl:{result['has_backup_url']})")

        # 使用搜索结果进行下载
        return self.download_music(keywords, level, search_results)

    def MD5Encrypt(self, text):
        # 返回当前时间的时间戳(1970纪元后经过的浮点秒数)
        k = time.time()
        k = int(round(k * 1000))
        info = ["NVPh5oo715z5DIWAeQlhMDsWXXQV4hwt", "bitrate=0", "callback=callback123",
                "clienttime={}".format(k), "clientver=2000", "dfid=-", "inputtype=0",
                "iscorrection=1", "isfuzzy=0", "keyword={}".format(text), "mid={}".format(k),
                "page=1", "pagesize=30", "platform=WebFilter", "privilege_filter=0",
                "srcappid=2919", "tag=em", "userid=-1", "uuid={}".format(k), "NVPh5oo715z5DIWAeQlhMDsWXXQV4hwt"]
        # 创建md5对象
        new_md5 = md5()
        info = ''.join(info)
        # 更新哈希对象
        new_md5.update(info.encode(encoding='utf-8'))
        # 加密
        signature = new_md5.hexdigest()
        url = 'https://complexsearch.kugou.com/v2/search/song?callback=callback123&keyword={0}' \
              '&page=1&pagesize=30&bitrate=0&isfuzzy=0&tag=em&inputtype=0&platform=WebFilter&userid=-1' \
              '&clientver=2000&iscorrection=1&privilege_filter=0&srcappid=2919&clienttime={1}&' \
              'mid={2}&uuid={3}&dfid=-&signature={4}'.format(parse.quote(text), k, k, k, signature.upper())
        return url

    def get_html(self, url):
        try:
            response = requests.get(url, headers=self.headers, cookies=self.kugou.cookies.get_dict(), verify=False)
            response.raise_for_status()
            response.encoding = 'utf-8'
            return response.text
        except Exception as err:
            print(err)
            return '请求异常'

    def parse_text(self, text):
        """
        解析搜索结果，提取歌曲信息和hash值
        返回格式: [(file_hash, hq_file_hash, sq_file_hash, album_id, song_name, singer_name, mix_song_id), ...]
        """
        hash_list = []
        print('{:*^80}'.format('搜索结果如下'))
        print('{0:{5}<5}{1:{5}<20}{2:{5}<15}{3:{5}<10}{4:{5}<15}'.format('序号', '歌名', '歌手', '时长(s)', '专辑', chr(12288)))
        print('{:-^84}'.format('-'))

        try:
            song_list = json.loads(text)['data']['lists']
        except (json.JSONDecodeError, KeyError) as e:
            print(f"解析搜索结果失败: {e}")
            return hash_list

        for count, song in enumerate(song_list):
            # 清理歌手名称中的HTML标签
            singer_name = song.get('SingerName', '')
            pattern = re.compile(r'<[^>]+>')
            singer_name = re.sub(pattern, '', singer_name)

            # 清理歌曲名称中的HTML标签
            song_name = song.get('SongName', '')
            song_name = re.sub(pattern, '', song_name)

            album_name = song.get('AlbumName', '')
            album_id = song.get('AlbumID', '')
            duration = song.get('Duration', 0)

            # 获取不同音质的hash值
            file_hash = song.get('FileHash', '')
            hq_file_hash = song.get('HQFileHash', '')
            sq_file_hash = song.get('SQFileHash', '')

            # 获取MixSongID用于第二个方案
            mix_song_id = song.get('EMixSongID', '')

            # 存储完整信息，包括歌曲名、歌手名和MixSongID
            hash_list.append([file_hash, hq_file_hash, sq_file_hash, album_id, song_name, singer_name, mix_song_id])

            print('{0:{5}<5}{1:{5}<20}{2:{5}<15}{3:{5}<10}{4:{5}<15}'.format(
                count, song_name[:18], singer_name[:13], duration, album_name[:13], chr(12288)))

            # 限制显示数量，避免输出过多
            if count >= 29:
                break

        print('{:*^80}'.format('*'))
        return hash_list

    def log_in(self):
        # 略，已有cookie
        pass

    def test_download(self, test_songs=None):
        """
        测试下载功能
        支持格式: ["歌名", "歌手 - 歌名", "歌名 歌手"]
        """
        if test_songs is None:
            test_songs = [
                "蓝光乐队 - 轻舟(dj阿卓版)",
                "草帽酱 - 戏说",
                "张杰 - 轻舟",
                "周杰伦 - 稻香"
            ]

        print(f"开始测试下载功能，共 {len(test_songs)} 首歌曲")
        print("支持搜索格式: '歌名' 或 '歌手 - 歌名'")
        success_count = 0

        for i, song in enumerate(test_songs, 1):
            print(f"\n{'='*60}")
            print(f"[{i}/{len(test_songs)}] 测试歌曲: {song}")
            print(f"{'='*60}")
            try:
                name, file_path = self.download_music(song)
                print(f"✅ 成功下载: {name}")
                print(f"   文件路径: {file_path}")
                success_count += 1
            except Exception as e:
                print(f"❌ 下载失败: {e}")

        print(f"\n{'='*60}")
        print(f"测试完成: {success_count}/{len(test_songs)} 首歌曲下载成功")
        print(f"{'='*60}")
        return success_count == len(test_songs)

if __name__ == '__main__':
    kugou = Kugou_music()

    # 示例用法:
    # 1. 直接下载单首歌曲
    # kugou.download_music("张杰 - 轻舟")
    # kugou.download_music("轻舟(dj阿卓版)")

    # 2. 运行测试（下载多首歌曲）
    kugou.test_download()

    # 3. 自定义测试歌曲列表
    # custom_songs = ["周杰伦 - 稻香", "邓紫棋 - 光年之外", "薛之谦 - 演员"]
    # kugou.test_download(custom_songs)