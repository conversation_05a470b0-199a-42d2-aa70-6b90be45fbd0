<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Title</title>
    <script src="vue2.6.14.min.js"></script>
    <script type="text/javascript" src="jquery-3.7.1.min.js"></script>
    <style>
        body {
            background-color: transparent;
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 0;
            overflow: hidden;
        }

        #rank {
            display: none;
            position: absolute;
            top: 30px;
            left: 30px;
            width: 650px;
            max-height: 90vh;
            background: linear-gradient(135deg,
                rgba(0, 0, 0, 0.95) 0%,
                rgba(20, 20, 40, 0.9) 50%,
                rgba(0, 0, 0, 0.95) 100%);
            border-radius: 25px;
            padding: 30px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.7);
            backdrop-filter: blur(20px);
            border: 2px solid rgba(255, 215, 0, 0.3);
            animation: rankSlideIn 0.8s ease-out;
            overflow-y: auto;
        }

        @keyframes rankSlideIn {
            from {
                opacity: 0;
                transform: translateX(-100px) scale(0.9);
            }
            to {
                opacity: 1;
                transform: translateX(0) scale(1);
            }
        }

        .rank-title {
            text-align: center;
            font-size: 36px;
            font-weight: bold;
            color: #FFD700;
            margin-bottom: 25px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
            background: linear-gradient(45deg, #FFD700, #FFA500);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .tip {
            width: 100%;
            height: 120px;
            margin-bottom: 15px;
            padding: 15px;
            background: linear-gradient(90deg,
                rgba(255, 255, 255, 0.05) 0%,
                rgba(255, 255, 255, 0.1) 50%,
                rgba(255, 255, 255, 0.05) 100%);
            border-radius: 15px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            display: flex;
            align-items: center;
        }

        .tip:hover {
            transform: translateX(10px);
            background: linear-gradient(90deg,
                rgba(255, 215, 0, 0.1) 0%,
                rgba(255, 215, 0, 0.2) 50%,
                rgba(255, 215, 0, 0.1) 100%);
            border-color: rgba(255, 215, 0, 0.3);
        }

        .tip:nth-child(1) { /* 第一名 */
            background: linear-gradient(90deg,
                rgba(255, 215, 0, 0.2) 0%,
                rgba(255, 215, 0, 0.3) 50%,
                rgba(255, 215, 0, 0.2) 100%);
            border: 2px solid rgba(255, 215, 0, 0.5);
            animation: goldGlow 2s ease-in-out infinite alternate;
        }

        .tip:nth-child(2) { /* 第二名 */
            background: linear-gradient(90deg,
                rgba(192, 192, 192, 0.2) 0%,
                rgba(192, 192, 192, 0.3) 50%,
                rgba(192, 192, 192, 0.2) 100%);
            border: 2px solid rgba(192, 192, 192, 0.5);
        }

        .tip:nth-child(3) { /* 第三名 */
            background: linear-gradient(90deg,
                rgba(205, 127, 50, 0.2) 0%,
                rgba(205, 127, 50, 0.3) 50%,
                rgba(205, 127, 50, 0.2) 100%);
            border: 2px solid rgba(205, 127, 50, 0.5);
        }

        @keyframes goldGlow {
            from {
                box-shadow: 0 0 20px rgba(255, 215, 0, 0.3);
            }
            to {
                box-shadow: 0 0 40px rgba(255, 215, 0, 0.6);
            }
        }

        .num {
            width: 60px;
            height: 80px;
            font-size: 42px;
            font-weight: bold;
            color: #FFFFFF;
            line-height: 80px;
            text-align: center;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
            margin-right: 15px;
        }

        .tip:nth-child(1) .num { /* 第一名 */
            color: #FFD700;
            text-shadow: 0 0 10px #FFD700;
        }

        .tip:nth-child(2) .num { /* 第二名 */
            color: #C0C0C0;
            text-shadow: 0 0 8px #C0C0C0;
        }

        .tip:nth-child(3) .num { /* 第三名 */
            color: #CD7F32;
            text-shadow: 0 0 8px #CD7F32;
        }

        .avatar {
            width: 80px;
            height: 80px;
            border-radius: 40px;
            object-fit: cover;
            background-position: center;
            background-size: cover;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.5);
            border: 3px solid rgba(255, 255, 255, 0.3);
            margin-right: 20px;
            transition: all 0.3s ease;
        }

        .avatar:hover {
            transform: scale(1.1);
            border-color: rgba(255, 215, 0, 0.6);
        }

        .rightArea {
            flex: 1;
            height: 80px;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .score {
            font-size: 32px;
            font-weight: bold;
            color: #FFD700;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
            line-height: 1.2;
            background: linear-gradient(45deg, #FFD700, #FFA500);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .score.good {
            background: linear-gradient(45deg, #FFD700, #FFA500);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .username {
            font-size: 24px;
            color: #FFFFFF;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
            font-weight: 500;
            line-height: 1.2;
            margin-top: 5px;
            max-width: 300px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            #rank {
                width: 90vw;
                left: 5vw;
                top: 20px;
                padding: 20px;
            }

            .avatar {
                width: 60px;
                height: 60px;
                border-radius: 30px;
            }

            .rightArea {
                height: 60px;
            }

            .score {
                font-size: 28px;
            }

            .username {
                font-size: 20px;
                max-width: 200px;
            }

            .num {
                width: 50px;
                height: 60px;
                font-size: 36px;
                line-height: 60px;
            }

            .tip {
                height: 100px;
            }
        }

        /* 滚动条样式 */
        #rank::-webkit-scrollbar {
            width: 8px;
        }

        #rank::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 4px;
        }

        #rank::-webkit-scrollbar-thumb {
            background: rgba(255, 215, 0, 0.5);
            border-radius: 4px;
        }

        #rank::-webkit-scrollbar-thumb:hover {
            background: rgba(255, 215, 0, 0.7);
        }
    </style>
</head>
<body>
<div id="rank">
    <div class="rank-title">🏆 积分排行榜 🏆</div>
    <div v-for="(item,index) in randlist" class="tip">
        <div class="num">{{index+1}}</div>
        <div class="avatar" :style="'background-image: url('+item.userface+')'"></div>
        <div class="rightArea">
            <div class="score good">{{item.score}}积分</div>
            <div class="username">{{item.username}}</div>
        </div>
    </div>
</div>
<script>
    var vm = new Vue({
            el: '#rank',
            data: {
                randlist: []
            },
            methods: {
                loadData(jsonstr) {
                    randlist = jsonstr
                }
            }

    })
    let ws
    function connect() {
        ws = new WebSocket("ws://localhost:18765");
        ws.onopen = function () {
            console.log("Connected!------------rank----------");
        };
        ws.onmessage = function (event) {
            console.log("Received message:", event.data);
            try {
                const jsonstr = JSON.parse(event.data);
                if (jsonstr["type"] !== "排行榜") {
                    return;
                }

                // 更新排行榜数据
                vm.$set(vm, "randlist", jsonstr["data"] || []);

                // 显示排行榜
                $("#rank").css("display", "block");

                // 添加进入动画
                $("#rank").removeClass("rank-exit").addClass("rank-enter");

                // 35秒后隐藏
                setTimeout(function () {
                    $("#rank").addClass("rank-exit");
                    setTimeout(function() {
                        $("#rank").css("display", "none").removeClass("rank-exit");
                    }, 500);
                }, 35000);

            } catch (error) {
                console.error("解析排行榜消息失败:", error);
            }
        }
        ws.onclose = function (event) {
            console.log('WebSocket连接已关闭');
            setTimeout(reconnect, 5000);
        };
    }

    function reconnect() {
        if (ws !== null && (ws.readyState === WebSocket.CLOSED || ws.readyState === WebSocket.CLOSING)) {
            console.log("尝试重连WebSocket...");
            connect();
        }
    }

    // 初始化连接
    connect();

</script>
</body>
</html>