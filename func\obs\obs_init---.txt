J:\ai\�������\ai-yinmei\func\obs\obs_init.c    obs_init.py     __init__        obs_init.ObsInit.__init__   get_ws      obs_init.ObsInit.get_ws obs_init                Interpreter change detected - this module can only be loaded into one interpreter per process.  name    __loader__  loader      __file__    origin      __package__ parent      __path__        submodule_search_locations      Module 'obs_init' has already been imported. Re-initialisation is not supported.        builtins        cython_runtime  __builtins__    init obs_init   %s() got multiple values for keyword argument '%U'      %.200s() keywords must be strings       %s() got an unexpected keyword argument '%U'    exactly %.200s() takes %.8s %zd positional argument%.1s (%zd given)     name '%U' is not defined         while calling a Python object  NULL result without error in PyObject_Call      cannot import name %S   _cython_3_0_11  Shared Cython type %.200s is not a type object  Shared Cython type %.200s has the wrong size, try recompiling   keywords must be strings        __name__ must be set to a string object __qualname__ must be set to a string object     function's dictionary may not be deleted        setting function's dictionary to a non-dict     __defaults__ must be set to a tuple object      changes to cyfunction.__defaults__ will not currently affect the values used in function calls  __kwdefaults__ must be set to a dict object     changes to cyfunction.__kwdefaults__ will not currently affect the values used in function calls        __annotations__ must be set to a dict object    func_doc        __doc__ func_name       __name__        __qualname__    func_dict       __dict__        func_globals    __globals__     func_closure    __closure__     func_code       __code__        func_defaults   __defaults__    __kwdefaults__  __annotations__ _is_coroutine   __module__      __reduce__      Bad call flags for CyFunction   <cyfunction %U at %p>   %.200s() takes no arguments (%zd given) %.200s() takes exactly one argument (%zd given) %.200s() takes no keyword arguments     unbound method %.200S() needs an argument       %.200s() needs an argument      _cython_3_0_11.cython_function_or_method        %s (%s:%d)      does not match          compile time Python version %d.%d of module '%.100s' %s runtime version %d.%d   connect exception       cline_in_traceback      func.tools.singleton_mode       �� �   __prepare__     defaultConfig   func.gobal.data __main__        __qualname__    func.config.default_config      __module__  self        __dict__        Ȅ �   ?   url _is_coroutine   J:\ai\吟美打包\ai-yinmei\func\obs\obs_init.py   super       __import__  log __doc__ ObsInit __set_name__    VideoControl    VideoStatus port        func.log.default_log    __name__        DefaultLog      __init_subclass__       obsData obs_init        __test__        ObsData e       func.obs.obs_websocket  host    ObsInit.get_ws  连接OBS错误！      __metaclass__   getLogger   obs __init__    .   ObsInit.__init__        asyncio.coroutines      password    get_ws      ObsWebSocket    singleton