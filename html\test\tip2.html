<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>提示</title>
</head>
<body>
<ul id="content"></ul>
<form class="form">
    <input type="button" value="连接" id="connect" class="connect"/>
    <br/>
    <input type="text" placeholder="请输入发送的消息" class="message" id="message"/>
    <input type="button" value="发送" id="send" class="connect"/>
</form>

<script type="text/javascript">
    var oUl=document.getElementById('content');
    var oConnect=document.getElementById('connect');
    var oSend=document.getElementById('send');
    var message=document.getElementById('message');
    var websocket=null;
    oConnect.onclick=function(){
        websocket=new WebSocket('ws://127.0.0.1:18765');

        <!--客户端链接后触发-->
         websocket.onopen=function(){
             oUl.innerHTML+="<li>客户端已连接</li>";
         }
         <!--收到消息后触发-->
        websocket.onmessage=function(evt){
            oUl.innerHTML+="<li>"+evt.data+"</li>";
        }
        <!--关闭后触发-->
        websocket.onclose=function(){
            oUl.innerHTML+="<li>客户端已断开连接</li>";
        };
        <!--出错后触发-->
        websocket.onerror=function(evt){
            oUl.innerHTML+="<li>"+evt.data+"</li>";
        };
    };
    oSend.onclick=function(){
        if(websocket){
             websocket.send(message.value)
        }
    }
</script>
</body>
</html>