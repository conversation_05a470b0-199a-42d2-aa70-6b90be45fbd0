"""
积分系统测试文件
用于验证积分系统的基本功能
"""

import unittest
from unittest.mock import Mock, patch, MagicMock
import sys
import os

# 添加项目根目录到path
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

# 尝试导入模块，如果失败则跳过相关测试
try:
    from func.score.score_db import ScoreDB
    SCORE_DB_AVAILABLE = True
except ImportError as e:
    print(f"Warning: Could not import ScoreDB: {e}")
    SCORE_DB_AVAILABLE = False

try:
    from func.score.oper_score import OperScore
    OPER_SCORE_AVAILABLE = True
except ImportError as e:
    print(f"Warning: Could not import OperScore: {e}")
    OPER_SCORE_AVAILABLE = False

try:
    from func.score.score_config import ScoreSystemConfig, ScoreOperationType
    CONFIG_AVAILABLE = True
except ImportError as e:
    print(f"Warning: Could not import config: {e}")
    CONFIG_AVAILABLE = False

class TestScoreSystem(unittest.TestCase):
    """积分系统测试类"""
    
    def setUp(self):
        """测试初始化"""
        # 模拟数据库连接
        self.mock_db = Mock()
        self.mock_collection = Mock()
        self.mock_client = Mock()
        
        # 创建测试数据
        self.test_user = {
            "openId": "test_user_001",
            "userName": "测试用户",
            "userface": "test_avatar.jpg",
            "score": 100,
            "updateTime": "2024-01-01 12:00:00"
        }
    
    @unittest.skipUnless(CONFIG_AVAILABLE, "Config modules not available")
    def test_score_config(self):
        """测试积分配置"""
        # 测试基础配置
        self.assertEqual(ScoreSystemConfig.CHAT_SCORE, 1)
        self.assertEqual(ScoreSystemConfig.SING_COST, 2)
        self.assertEqual(ScoreSystemConfig.DANCE_COST, 3)
        
        # 测试操作类型验证
        self.assertTrue(ScoreSystemConfig.validate_operation_type("聊天"))
        self.assertTrue(ScoreSystemConfig.validate_operation_type("唱歌"))
        self.assertFalse(ScoreSystemConfig.validate_operation_type("无效操作"))
        
        # 测试积分数量验证
        self.assertTrue(ScoreSystemConfig.validate_score_amount(100))
        self.assertTrue(ScoreSystemConfig.validate_score_amount(-100))
        self.assertFalse(ScoreSystemConfig.validate_score_amount(20000))
        
        # 测试消耗型操作判断
        self.assertTrue(ScoreSystemConfig.is_consumption_operation("唱歌"))
        self.assertTrue(ScoreSystemConfig.is_consumption_operation("跳舞"))
        self.assertFalse(ScoreSystemConfig.is_consumption_operation("聊天"))
    
    @unittest.skipUnless(SCORE_DB_AVAILABLE, "ScoreDB not available")
    def test_score_db_operations(self):
        """测试积分数据库操作"""
        # 直接测试验证方法，不依赖复杂的Mock
        from func.score.score_db import ScoreDB
        
        # 创建一个临时的ScoreDB实例用于测试验证方法
        score_db = ScoreDB()
        
        # 测试参数验证方法
        self.assertFalse(score_db._validate_score_params("", "user", 10, "test"))
        self.assertFalse(score_db._validate_score_params("user", "", 10, "test"))
        self.assertFalse(score_db._validate_score_params("user", "name", "invalid", "test"))
        self.assertTrue(score_db._validate_score_params("user", "name", 10, "test"))
        
        # 测试用户参数验证
        self.assertFalse(score_db._validate_user_params("", "user"))
        self.assertFalse(score_db._validate_user_params("user", ""))
        self.assertTrue(score_db._validate_user_params("user", "name"))
    
    @unittest.skipUnless(OPER_SCORE_AVAILABLE, "OperScore not available")
    @patch('func.score.oper_score.ScoreDB')
    @patch('func.score.oper_score.CommonWebsocket')
    @patch('func.score.oper_score.TTsCore')
    @patch('func.score.oper_score.DefaultLog')
    @patch('func.score.oper_score.CommonData')
    def test_score_operations(self, mock_common_data, mock_log, mock_tts, mock_websocket, mock_score_db):
        """测试积分操作"""
        # 模拟依赖
        mock_log.return_value.getLogger.return_value = Mock()
        mock_score_db.return_value.get_score.return_value = self.test_user
        mock_score_db.return_value.oper_score.return_value = True
        
        oper_score = OperScore()
        
        # 测试参数验证
        self.assertFalse(oper_score._validate_score_operation("", 10))
        self.assertFalse(oper_score._validate_score_operation("user", "invalid"))
        self.assertTrue(oper_score._validate_score_operation("user", 10))
        
        # 测试操作参数验证
        self.assertFalse(oper_score._validate_operation_params("", "user", 10, "test"))
        self.assertTrue(oper_score._validate_operation_params("user", "name", 10, "test"))
    
    @unittest.skipUnless(OPER_SCORE_AVAILABLE, "OperScore not available")
    def test_score_rank_operations(self):
        """测试排行榜操作"""
        # 测试排行榜逻辑而不依赖实际数据
        try:
            from func.score.oper_score import OperScore
            oper_score = OperScore()
            
            # 测试排行榜功能（即使没有数据也应该返回空列表而不是报错）
            rank_list = oper_score.find_score_rank(3)
            
            # 验证返回的是列表类型
            self.assertIsInstance(rank_list, list)
            
            # 由于可能没有实际数据，我们只验证方法能正常调用
            self.assertTrue(True, "排行榜方法调用成功")
            
        except Exception as e:
            # 如果初始化失败，跳过测试
            self.skipTest(f"OperScore初始化失败，跳过排行榜测试: {e}")
    
    @unittest.skipUnless(OPER_SCORE_AVAILABLE, "OperScore not available")
    @patch('func.score.oper_score.ScoreDB')
    @patch('func.score.oper_score.CommonWebsocket')
    @patch('func.score.oper_score.TTsCore')
    @patch('func.score.oper_score.DefaultLog')
    @patch('func.score.oper_score.CommonData')
    def test_user_score_query(self, mock_common_data, mock_log, mock_tts, mock_websocket, mock_score_db):
        """测试用户积分查询"""
        # 模拟依赖
        mock_log.return_value.getLogger.return_value = Mock()
        mock_score_db.return_value.get_score.return_value = self.test_user
        
        oper_score = OperScore()
        user_info = oper_score.find_score_user("test_user_001")
        
        # 验证用户信息
        self.assertIsNotNone(user_info)
        self.assertEqual(user_info["openId"], "test_user_001")
        self.assertEqual(user_info["userName"], "测试用户")
        self.assertEqual(user_info["score"], 100)
    
    @unittest.skipUnless(OPER_SCORE_AVAILABLE, "OperScore not available")
    def test_system_stats(self):
        """测试系统统计"""
        try:
            from func.score.oper_score import OperScore
            oper_score = OperScore()
            
            # 测试系统统计功能
            stats = oper_score.get_system_stats()
            
            # 验证返回的数据结构
            self.assertIsInstance(stats, dict)
            self.assertIn("total_users", stats)
            self.assertIn("total_score", stats)
            self.assertIn("average_score", stats)
            
            # 验证数据类型
            self.assertIsInstance(stats["total_users"], int)
            self.assertIsInstance(stats["total_score"], int)
            self.assertIsInstance(stats["average_score"], (int, float))
            
        except Exception as e:
            self.skipTest(f"OperScore初始化失败，跳过系统统计测试: {e}")
    
    @unittest.skipUnless(OPER_SCORE_AVAILABLE, "OperScore not available")
    @patch('func.score.oper_score.ScoreDB')
    @patch('func.score.oper_score.CommonWebsocket')
    @patch('func.score.oper_score.TTsCore')
    @patch('func.score.oper_score.DefaultLog')
    @patch('func.score.oper_score.CommonData')
    def test_score_sufficiency_check(self, mock_common_data, mock_log, mock_tts, mock_websocket, mock_score_db):
        """测试积分余额检查"""
        # 模拟依赖
        mock_log.return_value.getLogger.return_value = Mock()
        mock_score_db.return_value.get_score.return_value = self.test_user
        
        oper_score = OperScore()
        
        # 测试积分足够的情况
        self.assertTrue(oper_score.check_user_score_sufficient("test_user_001", 50))
        
        # 测试积分不足的情况
        self.assertFalse(oper_score.check_user_score_sufficient("test_user_001", 150))

def run_basic_tests():
    """运行基础测试"""
    print("开始积分系统基础测试...")
    
    # 测试配置
    print("1. 测试积分配置...")
    try:
        if CONFIG_AVAILABLE:
            config = ScoreSystemConfig.get_config_dict()
            assert "score_rules" in config
            assert "limits" in config
            print("   ✅ 配置测试通过")
        else:
            print("   ⚠️  配置模块不可用，跳过测试")
    except Exception as e:
        print(f"   ❌ 配置测试失败: {e}")
    
    # 测试枚举
    print("2. 测试操作类型枚举...")
    try:
        if CONFIG_AVAILABLE:
            operations = [op.value for op in ScoreOperationType]
            assert "聊天" in operations
            assert "唱歌" in operations
            print("   ✅ 枚举测试通过")
        else:
            print("   ⚠️  配置模块不可用，跳过测试")
    except Exception as e:
        print(f"   ❌ 枚举测试失败: {e}")
    
    # 测试验证函数
    print("3. 测试验证函数...")
    try:
        if CONFIG_AVAILABLE:
            assert ScoreSystemConfig.validate_score_amount(100)
            assert not ScoreSystemConfig.validate_score_amount(20000)
            assert ScoreSystemConfig.validate_operation_type("聊天")
            assert not ScoreSystemConfig.validate_operation_type("无效操作")
            print("   ✅ 验证函数测试通过")
        else:
            print("   ⚠️  配置模块不可用，跳过测试")
    except Exception as e:
        print(f"   ❌ 验证函数测试失败: {e}")
    
    print("基础测试完成！")

if __name__ == "__main__":
    # 运行基础测试
    run_basic_tests()
    
    # 检查模块可用性
    print(f"\n模块可用性检查:")
    print(f"ScoreDB: {'✅' if SCORE_DB_AVAILABLE else '❌'}")
    print(f"OperScore: {'✅' if OPER_SCORE_AVAILABLE else '❌'}")
    print(f"Config: {'✅' if CONFIG_AVAILABLE else '❌'}")
    
    # 运行单元测试
    print("\n开始单元测试...")
    unittest.main(argv=[''], exit=False, verbosity=2) 