J:\ai\�������\ai-yinmei\controller\sence_init.c        sence_init.py   senceInit       sence_init.senceInit    sence_init              Interpreter change detected - this module can only be loaded into one interpreter per process.  name    __loader__  loader      __file__    origin      __package__ parent      __path__        submodule_search_locations      Module 'sence_init' has already been imported. Re-initialisation is not supported.      builtins        cython_runtime  __builtins__    init sence_init name '%U' is not defined         while calling a Python object  NULL result without error in PyObject_Call      cannot import name %S   _cython_3_0_11  Shared Cython type %.200s is not a type object  Shared Cython type %.200s has the wrong size, try recompiling   keywords must be strings        __name__ must be set to a string object __qualname__ must be set to a string object     function's dictionary may not be deleted        setting function's dictionary to a non-dict     __defaults__ must be set to a tuple object              changes to cyfunction.__defaults__ will not currently affect the values used in function calls  __kwdefaults__ must be set to a dict object     changes to cyfunction.__kwdefaults__ will not currently affect the values used in function calls        __annotations__ must be set to a dict object    func_doc        __doc__ func_name       __name__        __qualname__    func_dict       __dict__        func_globals    __globals__     func_closure    __closure__     func_code       __code__        func_defaults   __defaults__    __kwdefaults__  __annotations__ _is_coroutine   __module__      __reduce__      Bad call flags for CyFunction   <cyfunction %U at %p>   %.200s() takes no arguments (%zd given) %.200s() takes exactly one argument (%zd given) %.200s() takes no keyword arguments     unbound method %.200S() needs an argument       %.200s() needs an argument      _cython_3_0_11.cython_function_or_method        %s (%s:%d)      does not match          compile time Python version %d.%d of module '%.100s' %s runtime version %d.%d   唱歌视频    exception       visible_input   video   actionOper      cline_in_traceback      control_video   �� �   emoteOper       CommonData      check_scene_time        func.gobal.data __main__    value       �� �   play_video      func.vtuber.action_oper emote_ws    rooms       _is_coroutine   __import__  log ObsInit func.obs.obs_init       VideoControl    func.log.default_log    __name__    波形      func.vtuber.emote_oper  DefaultLog      状态提示    ?       初始化场景错误   show_text       __test__        VtuberData  表情      init_scene  e   commonData      senceInit   .   func.obs.obs_websocket  getLogger       J:\ai\吟美打包\ai-yinmei\controller\sence_init.py   now_clothes obs sence_init      初始化       asyncio.coroutines  get_ws      vtuberData  STOP    伴奏  便衣      EmoteOper       ActionOper