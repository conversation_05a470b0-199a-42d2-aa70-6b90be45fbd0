#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试音乐转换服务整合
"""
import time
import requests
import json
from func.sing.music_convert_service import music_convert_service

def test_music_service():
    """测试音乐转换服务"""
    print("=== 测试音乐转换服务整合 ===")

    try:
        # 1. 测试服务模块导入
        print("1. 测试服务模块导入...")
        print(f"✓ 音乐转换服务模块导入成功")
        print(f"  - AutoConvertMusic可用: {hasattr(music_convert_service, 'music_module') and music_convert_service.music_module is not None}")

        # 2. 测试服务启动（如果AutoConvertMusic可用）
        if music_convert_service.music_module is not None:
            print("2. 启动音乐转换服务...")
            music_convert_service.start_service(host="127.0.0.1", port=17171)
            time.sleep(2)  # 等待服务启动
            print("✓ 服务启动成功")

            # 3. 测试状态接口
            print("3. 测试状态接口...")
            try:
                response = requests.get("http://127.0.0.1:17171/status", timeout=5)
                if response.status_code == 200:
                    status_data = response.json()
                    print(f"✓ 状态接口正常: {status_data}")
                else:
                    print(f"✗ 状态接口异常: {response.status_code}")
            except Exception as e:
                print(f"✗ 状态接口请求失败: {e}")
        else:
            print("2. 跳过服务启动测试（AutoConvertMusic不可用）")
            print("   这通常是因为缺少selenium等依赖")

        print("=== 测试完成 ===")

    except Exception as e:
        print(f"✗ 测试过程中发生异常: {e}")
        import traceback
        traceback.print_exc()

def test_sing_core_integration():
    """测试sing_core的整合"""
    print("\n=== 测试sing_core整合 ===")
    
    try:
        from func.sing.sing_core import SingCore
        
        # 创建SingCore实例
        sing_core = SingCore()
        print("✓ SingCore实例创建成功")
        
        # 测试check_down_song方法
        result = sing_core.check_down_song("测试歌曲")
        print(f"✓ check_down_song方法调用成功: {result}")
        
        print("=== sing_core整合测试完成 ===")
        
    except Exception as e:
        print(f"✗ sing_core整合测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    # 测试音乐服务
    test_music_service()
    
    # 等待一段时间
    time.sleep(3)
    
    # 测试sing_core整合
    test_sing_core_integration()
    
    print("\n所有测试完成！")
