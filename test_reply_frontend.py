#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试回复消息前端显示功能
"""

import sys
import os
import time
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from func.llm.chat_db import ChatDB

def test_reply_frontend_display():
    """测试回复消息前端显示功能"""
    
    print("🚀 测试回复消息前端显示功能")
    print("=" * 60)
    
    try:
        # 创建ChatDB实例
        chat_db = ChatDB()
        
        # 步骤1: 插入原始问题
        print("\n📝 步骤1: 插入原始问题")
        current_time = time.time()
        formatted_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(current_time))
        
        original_question = {
            "traceid": f"question_{int(current_time)}",
            "query": "前端显示测试问题",
            "uid": "frontend_test_user_123",
            "user_name": "前端测试用户",
            "uface": "",
            "channel": "webili",
            "submitTime": formatted_time
        }
        
        print(f"原始问题数据: {original_question}")
        result1 = chat_db.insert_request(original_question)
        print(f"✅ 原始问题插入结果: {result1}")
        
        # 步骤2: 解析并插入回复消息
        print("\n🔍 步骤2: 解析并插入回复消息")
        reply_message = "[回复<前端测试用户:frontend_test_user_123> 的消息：前端显示测试问题] 这是前端显示的回复内容"
        print(f"回复消息: {reply_message}")
        
        reply_info = chat_db.parse_reply_message(reply_message)
        if not reply_info:
            print("❌ 回复消息解析失败")
            return
            
        print(f"✅ 回复消息解析成功: {reply_info}")
        
        # 构建回复数据
        reply_data = {
            "traceid": f"reply_{int(time.time())}",
            "sender_name": reply_info["sender_name"],
            "sender_id": reply_info["sender_id"],
            "question": reply_info["question"],
            "reply_content": reply_info["reply_content"]
        }
        
        result2 = chat_db.insert_reply(reply_data)
        print(f"✅ 回复记录插入结果: {result2}")
        
        # 步骤3: 查询验证前端数据格式
        print("\n🔎 步骤3: 查询验证前端数据格式")
        
        # 模拟前端查询逻辑
        recent_records = list(
            chat_db.chat_request.find(
                {"user_name": {"$regex": "前端测试用户"}}
            ).sort("timestamp", -1).limit(10)
        )
        
        print(f"找到 {len(recent_records)} 条记录:")
        for i, record in enumerate(recent_records, 1):
            print(f"\n记录 {i}:")
            print(f"  用户名 (user_name): {record.get('user_name', 'N/A')}")
            print(f"  渠道 (channel): {record.get('channel', 'N/A')}")
            print(f"  意图 (intent): {record.get('intent', 'N/A')}")
            print(f"  问题 (query): {record.get('query', 'N/A')}")
            print(f"  回复 (content): {record.get('content', 'N/A')}")
            print(f"  创建时间 (submitTime): {record.get('submitTime', 'N/A')}")
            print(f"  是否回复 (is_reply): {record.get('is_reply', False)}")
        
        # 步骤4: 模拟前端API调用
        print("\n📡 步骤4: 模拟前端API调用")
        api_result = chat_db.find_chat_list_page(
            username="前端测试用户",
            page_number=1,
            page_size=10
        )
        
        print(f"API查询结果:")
        print(f"  总记录数: {api_result.get('total_documents', 0)}")
        print(f"  总页数: {api_result.get('total_pages', 0)}")
        print(f"  当前页: {api_result.get('current_page', 1)}")
        
        if api_result.get('data'):
            print(f"  数据记录数: {len(api_result['data'])}")
            for i, record in enumerate(api_result['data'], 1):
                print(f"\n  API记录 {i}:")
                print(f"    用户名: {record.get('user_name', 'N/A')}")
                print(f"    问题: {record.get('query', 'N/A')}")
                print(f"    回复: {record.get('content', 'N/A')}")
                print(f"    时间: {record.get('submitTime', 'N/A')}")
        
        print("\n" + "=" * 60)
        print("🎉 测试完成!")
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_reply_frontend_display()
