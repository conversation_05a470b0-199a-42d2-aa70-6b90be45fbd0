"""
积分系统指标收集器
收集和统计系统运行指标
"""

import time
import threading
from collections import defaultdict, deque
from typing import Dict, Any, List
from func.log.default_log import DefaultLog


class ScoreMetrics:
    """积分系统指标收集器"""

    def __init__(self):
        """初始化指标收集器"""
        self.log = DefaultLog().getLogger()
        self.start_time = time.time()
        self.lock = threading.Lock()

        # 操作计数器
        self.operation_counts = defaultdict(int)
        self.success_counts = defaultdict(int)
        self.error_counts = defaultdict(int)

        # 响应时间统计
        self.response_times = defaultdict(list)
        self.max_response_time_samples = 1000  # 最多保留1000个样本

        # 错误统计
        self.error_details = defaultdict(list)
        self.max_error_samples = 100  # 最多保留100个错误样本

        # 用户操作统计
        self.user_operations = defaultdict(int)
        self.score_operations = defaultdict(int)

        # 最近活动记录
        self.recent_activities = deque(maxlen=1000)

        self.log.info("积分系统指标收集器初始化完成")

    def record_operation(self, operation: str, success: bool, response_time: float,
                        openId: str = "", score: int = 0, error_type: str = ""):
        """
        记录操作指标
        :param operation: 操作名称
        :param success: 是否成功
        :param response_time: 响应时间
        :param openId: 用户ID
        :param score: 积分变化
        :param error_type: 错误类型
        """
        with self.lock:
            try:
                # 基础计数
                self.operation_counts[operation] += 1

                if success:
                    self.success_counts[operation] += 1
                else:
                    self.error_counts[operation] += 1
                    if error_type:
                        error_info = {
                            "timestamp": time.time(),
                            "operation": operation,
                            "openId": openId,
                            "error_type": error_type,
                            "response_time": response_time
                        }
                        self.error_details[operation].append(error_info)
                        # 限制错误样本数量
                        if len(self.error_details[operation]) > self.max_error_samples:
                            self.error_details[operation].pop(0)

                # 响应时间统计
                self.response_times[operation].append(response_time)
                # 限制响应时间样本数量
                if len(self.response_times[operation]) > self.max_response_time_samples:
                    self.response_times[operation].pop(0)

                # 用户操作统计
                if openId:
                    self.user_operations[openId] += 1

                # 积分操作统计
                if score != 0:
                    if score > 0:
                        self.score_operations["add"] += 1
                    else:
                        self.score_operations["subtract"] += 1

                # 记录最近活动
                activity = {
                    "timestamp": time.time(),
                    "operation": operation,
                    "success": success,
                    "response_time": response_time,
                    "openId": openId,
                    "score": score
                }
                self.recent_activities.append(activity)

            except Exception as e:
                self.log.error(f"记录操作指标失败: {e}")

    def get_basic_stats(self) -> Dict[str, Any]:
        """获取基础统计信息"""
        with self.lock:
            try:
                total_operations = sum(self.operation_counts.values())
                total_success = sum(self.success_counts.values())
                total_errors = sum(self.error_counts.values())

                success_rate = (total_success / total_operations * 100) if total_operations > 0 else 0
                uptime_seconds = time.time() - self.start_time

                return {
                    "uptime_seconds": uptime_seconds,
                    "total_operations": total_operations,
                    "total_success": total_success,
                    "total_errors": total_errors,
                    "success_rate": round(success_rate, 2),
                    "operations_per_second": round(total_operations / uptime_seconds, 2) if uptime_seconds > 0 else 0
                }
            except Exception as e:
                self.log.error(f"获取基础统计失败: {e}")
                return {}

    def get_operation_stats(self) -> Dict[str, Any]:
        """获取操作统计信息"""
        with self.lock:
            try:
                operation_stats = {}

                for operation in self.operation_counts:
                    total = self.operation_counts[operation]
                    success = self.success_counts[operation]
                    errors = self.error_counts[operation]
                    success_rate = (success / total * 100) if total > 0 else 0

                    # 计算平均响应时间
                    response_times = self.response_times.get(operation, [])
                    avg_response_time = sum(response_times) / len(response_times) if response_times else 0

                    operation_stats[operation] = {
                        "total": total,
                        "success": success,
                        "errors": errors,
                        "success_rate": round(success_rate, 2),
                        "avg_response_time": round(avg_response_time, 4)
                    }

                return operation_stats
            except Exception as e:
                self.log.error(f"获取操作统计失败: {e}")
                return {}

    def get_user_stats(self, limit: int = 10) -> Dict[str, Any]:
        """
        获取用户统计信息
        :param limit: 返回数量限制
        :return: 用户统计
        """
        with self.lock:
            try:
                # 最活跃用户
                top_users = sorted(
                    self.user_operations.items(),
                    key=lambda x: x[1],
                    reverse=True
                )[:limit]

                return {
                    "total_active_users": len(self.user_operations),
                    "top_active_users": [{"openId": openId, "operations": count}
                                       for openId, count in top_users]
                }
            except Exception as e:
                self.log.error(f"获取用户统计失败: {e}")
                return {}

    def get_score_stats(self) -> Dict[str, Any]:
        """获取积分操作统计"""
        with self.lock:
            try:
                return {
                    "score_add_operations": self.score_operations.get("add", 0),
                    "score_subtract_operations": self.score_operations.get("subtract", 0),
                    "total_score_operations": sum(self.score_operations.values())
                }
            except Exception as e:
                self.log.error(f"获取积分统计失败: {e}")
                return {}

    def get_error_stats(self, limit: int = 10) -> Dict[str, Any]:
        """
        获取错误统计信息
        :param limit: 返回数量限制
        :return: 错误统计
        """
        with self.lock:
            try:
                error_stats = {}
                recent_errors = []

                for operation, errors in self.error_details.items():
                    if errors:
                        # 获取最近的错误
                        recent_operation_errors = sorted(errors, key=lambda x: x["timestamp"], reverse=True)[:limit]
                        error_stats[operation] = {
                            "total_errors": len(errors),
                            "recent_errors": recent_operation_errors
                        }
                        recent_errors.extend(recent_operation_errors)

                # 按时间排序所有最近错误
                recent_errors.sort(key=lambda x: x["timestamp"], reverse=True)

                return {
                    "error_by_operation": error_stats,
                    "recent_errors": recent_errors[:limit]
                }
            except Exception as e:
                self.log.error(f"获取错误统计失败: {e}")
                return {}

    def get_recent_activities(self, limit: int = 50) -> List[Dict[str, Any]]:
        """
        获取最近活动
        :param limit: 返回数量限制
        :return: 最近活动列表
        """
        with self.lock:
            try:
                activities = list(self.recent_activities)
                activities.sort(key=lambda x: x["timestamp"], reverse=True)
                return activities[:limit]
            except Exception as e:
                self.log.error(f"获取最近活动失败: {e}")
                return []

    def get_all_metrics(self) -> Dict[str, Any]:
        """获取所有指标"""
        try:
            return {
                "basic_stats": self.get_basic_stats(),
                "operation_stats": self.get_operation_stats(),
                "user_stats": self.get_user_stats(),
                "score_stats": self.get_score_stats(),
                "error_stats": self.get_error_stats(),
                "recent_activities": self.get_recent_activities(20)
            }
        except Exception as e:
            self.log.error(f"获取所有指标失败: {e}")
            return {}

    def reset_metrics(self):
        """重置所有指标"""
        with self.lock:
            try:
                self.operation_counts.clear()
                self.success_counts.clear()
                self.error_counts.clear()
                self.response_times.clear()
                self.error_details.clear()
                self.user_operations.clear()
                self.score_operations.clear()
                self.recent_activities.clear()
                self.start_time = time.time()
                self.log.info("指标已重置")
            except Exception as e:
                self.log.error(f"重置指标失败: {e}")

    def cleanup_old_data(self, max_age_hours: int = 24):
        """
        清理旧数据
        :param max_age_hours: 最大保留时间（小时）
        """
        with self.lock:
            try:
                cutoff_time = time.time() - (max_age_hours * 3600)

                # 清理旧的错误记录
                for operation in list(self.error_details.keys()):
                    self.error_details[operation] = [
                        error for error in self.error_details[operation]
                        if error["timestamp"] > cutoff_time
                    ]
                    if not self.error_details[operation]:
                        del self.error_details[operation]

                # 清理旧的活动记录
                self.recent_activities = deque(
                    [activity for activity in self.recent_activities
                     if activity["timestamp"] > cutoff_time],
                    maxlen=1000
                )

                self.log.info(f"清理了 {max_age_hours} 小时前的旧数据")
            except Exception as e:
                self.log.error(f"清理旧数据失败: {e}")