from flask import jsonify
from controller.baseview import BaseView
from func.danmaku.blivedm.blivedm_core import BlivedmCore
from func.log.default_log import DefaultLog
import subprocess
import sys
import asyncio
import time

log = DefaultLog().getLogger()

class Restart(BaseView):
    """
    重启服务相关的控制类
    """
    
    def post(self):
        """
        重启服务的POST处理方法
        """
        try:
            # 关闭弹幕监听
            log.info("关闭b站弹幕监听")
            BlivedmCore().close()
            
            # 暂停一下等待资源释放
            time.sleep(1)
            
            # 执行重启脚本
            subprocess.Popen("restart.bat", shell=True)
            
            # 退出当前进程
            sys.exit(0)
            
            return jsonify({
                "status": "成功"
            })
            
        except Exception as e:
            print(e)
            log.exception(e)
            return jsonify({
                "status": "失败",
                "error": str(e)
            })