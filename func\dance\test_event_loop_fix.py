#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试事件循环管理器是否解决了WebSocket连接冲突
"""

import asyncio
import time
import threading
from pathlib import Path

def test_traditional_method():
    """测试传统的事件循环方法（会导致冲突）"""
    print("\n🔧 测试传统事件循环方法...")
    
    async def simple_websocket_test():
        import websockets
        import json
        
        try:
            uri = "ws://localhost:8765"
            websocket = await websockets.connect(uri, open_timeout=3)
            
            # 发送状态请求
            message = {"type": "get_status", "data": {}}
            await websocket.send(json.dumps(message))
            
            # 接收响应
            response = await asyncio.wait_for(websocket.recv(), timeout=5)
            result = json.loads(response)
            
            await websocket.close()
            return result
            
        except Exception as e:
            return {"error": str(e)}
    
    # 模拟多次调用（类似舞蹈系统中的情况）
    results = []
    for i in range(3):
        print(f"   尝试 {i+1}/3...")
        try:
            # 每次创建新的事件循环
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            result = loop.run_until_complete(simple_websocket_test())
            results.append(result)
            
            loop.close()
            print(f"   ✅ 尝试 {i+1} 成功")
            
        except Exception as e:
            print(f"   ❌ 尝试 {i+1} 失败: {e}")
            results.append({"error": str(e)})
        
        time.sleep(1)  # 间隔1秒
    
    return results

def test_event_loop_manager():
    """测试事件循环管理器方法"""
    print("\n🔧 测试事件循环管理器方法...")
    
    try:
        from async_event_loop_manager import run_async_safe_global, ensure_event_loop
        
        async def simple_websocket_test():
            import websockets
            import json
            
            try:
                uri = "ws://localhost:8765"
                websocket = await websockets.connect(uri, open_timeout=3)
                
                # 发送状态请求
                message = {"type": "get_status", "data": {}}
                await websocket.send(json.dumps(message))
                
                # 接收响应
                response = await asyncio.wait_for(websocket.recv(), timeout=5)
                result = json.loads(response)
                
                await websocket.close()
                return result
                
            except Exception as e:
                return {"error": str(e)}
        
        # 确保事件循环已启动
        ensure_event_loop()
        time.sleep(1)  # 等待循环启动
        
        # 模拟多次调用
        results = []
        for i in range(3):
            print(f"   尝试 {i+1}/3...")
            try:
                result = run_async_safe_global(
                    simple_websocket_test(),
                    timeout=10.0,
                    default_value={"error": "超时"}
                )
                results.append(result)
                print(f"   ✅ 尝试 {i+1} 成功")
                
            except Exception as e:
                print(f"   ❌ 尝试 {i+1} 失败: {e}")
                results.append({"error": str(e)})
            
            time.sleep(1)  # 间隔1秒
        
        return results
        
    except ImportError as e:
        print(f"❌ 无法导入事件循环管理器: {e}")
        return [{"error": "模块导入失败"}]

def test_dance_core_integration():
    """测试dance_core集成"""
    print("\n🔧 测试dance_core集成...")
    
    try:
        # 添加当前目录到Python路径
        import sys
        current_dir = Path(__file__).parent
        if str(current_dir) not in sys.path:
            sys.path.insert(0, str(current_dir))
        
        # 修改导入路径
        sys.path.insert(0, str(current_dir.parent))
        
        # 尝试导入dance_core（可能需要模拟一些依赖）
        try:
            # 先模拟一些必要的模块
            import types
            
            # 模拟gobal.data模块
            gobal_data = types.ModuleType('func.gobal.data')
            
            class MockDanceData:
                def __init__(self):
                    self.websocket_switch = True
                    self.mmd_server_host = 'localhost'
                    self.mmd_server_port = 8765
                    self.mmd_server_enabled = True
            
            gobal_data.DanceData = MockDanceData
            sys.modules['func.gobal.data'] = gobal_data
            
            # 模拟其他必要模块
            for module_name in [
                'func.log.default_log', 'func.cmd.cmd_core', 'func.tts.tts_core',
                'func.score.oper_score', 'func.obs.obs_init', 'func.obs.obs_websocket',
                'func.tools.string_util', 'func.tools.singleton_mode'
            ]:
                mock_module = types.ModuleType(module_name)
                # 添加一些基本的mock类
                setattr(mock_module, 'DefaultLog', lambda: types.SimpleNamespace(getLogger=lambda: types.SimpleNamespace(info=print, warning=print, error=print, debug=print)))
                setattr(mock_module, 'CmdCore', lambda: types.SimpleNamespace(cmd=lambda *args: None))
                setattr(mock_module, 'TTsCore', lambda: types.SimpleNamespace(tts_say=lambda x: print(f"TTS: {x}")))
                setattr(mock_module, 'OperScore', lambda: types.SimpleNamespace(oper_score=lambda *args: None))
                setattr(mock_module, 'ObsInit', lambda: types.SimpleNamespace(get_ws=lambda: types.SimpleNamespace()))
                setattr(mock_module, 'VideoControl', types.SimpleNamespace(PAUSE=types.SimpleNamespace(value="pause")))
                setattr(mock_module, 'VideoStatus', types.SimpleNamespace(END=types.SimpleNamespace(value="end")))
                setattr(mock_module, 'StringUtil', types.SimpleNamespace(
                    has_string_reg_list=lambda pattern, text: pattern if any(p in text for p in (pattern if isinstance(pattern, list) else [pattern])) else None,
                    is_index_contain_string=lambda patterns, text: 0,
                    fuzzy_match_list=lambda query, items: [item for item in items if query.lower() in item.lower()][:5]
                ))
                setattr(mock_module, 'singleton', lambda cls: cls)
                sys.modules[module_name] = mock_module
            
            # 现在尝试导入dance_core
            from dance_core import DanceCore
            
            print("✅ dance_core导入成功")
            
            # 创建实例并测试
            dance_core = DanceCore()
            
            # 测试连接状态
            if hasattr(dance_core, 'mmd_websocket_client') and dance_core.mmd_websocket_client:
                print("✅ MMD WebSocket客户端已初始化")
                
                # 测试同步方法
                print("🔧 测试同步方法...")
                if hasattr(dance_core, 'execute_mmd_dance_sync'):
                    result = dance_core.execute_mmd_dance_sync("test_dance", "test_user")
                    print(f"   执行结果: {result}")
                
                return {"success": True, "message": "dance_core集成测试成功"}
            else:
                return {"error": "MMD WebSocket客户端未初始化"}
                
        except Exception as e:
            print(f"❌ dance_core导入/测试失败: {e}")
            import traceback
            traceback.print_exc()
            return {"error": f"dance_core测试失败: {e}"}
            
    except Exception as e:
        print(f"❌ 集成测试失败: {e}")
        return {"error": str(e)}

def main():
    """主测试函数"""
    print("🔧 事件循环冲突修复测试")
    print("=" * 50)
    
    # 首先检查服务器是否运行
    print("🔍 检查服务器状态...")
    import socket
    
    server_available = False
    for port in range(8765, 8775):
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(1)
            result = sock.connect_ex(('localhost', port))
            sock.close()
            
            if result == 0:
                print(f"✅ 发现服务器在端口 {port}")
                server_available = True
                break
        except:
            continue
    
    if not server_available:
        print("❌ 未发现可用的MMD服务器")
        print("💡 请先启动UPBGE MMD服务器，然后重新运行此测试")
        return
    
    # 测试1: 传统方法
    print("\n" + "="*50)
    print("测试1: 传统事件循环方法")
    traditional_results = test_traditional_method()
    
    success_count = sum(1 for r in traditional_results if "error" not in r)
    print(f"传统方法成功率: {success_count}/{len(traditional_results)}")
    
    # 测试2: 事件循环管理器
    print("\n" + "="*50)
    print("测试2: 事件循环管理器方法")
    manager_results = test_event_loop_manager()
    
    success_count = sum(1 for r in manager_results if "error" not in r)
    print(f"管理器方法成功率: {success_count}/{len(manager_results)}")
    
    # 测试3: dance_core集成
    print("\n" + "="*50)
    print("测试3: dance_core集成测试")
    integration_result = test_dance_core_integration()
    print(f"集成测试结果: {integration_result}")
    
    # 总结
    print("\n" + "="*50)
    print("🎯 测试总结:")
    
    traditional_success = sum(1 for r in traditional_results if "error" not in r)
    manager_success = sum(1 for r in manager_results if "error" not in r)
    
    print(f"传统方法: {traditional_success}/{len(traditional_results)} 成功")
    print(f"管理器方法: {manager_success}/{len(manager_results)} 成功")
    
    if manager_success > traditional_success:
        print("✅ 事件循环管理器改善了连接稳定性!")
    elif manager_success == traditional_success and manager_success > 0:
        print("✅ 事件循环管理器保持了连接稳定性")
    else:
        print("⚠️ 事件循环管理器需要进一步优化")
    
    if "success" in integration_result:
        print("✅ dance_core集成成功")
    else:
        print("⚠️ dance_core集成需要优化")
    
    print("\n💡 建议:")
    print("1. 如果管理器方法成功率更高，说明修复有效")
    print("2. 在舞蹈系统中使用修复后的dance_core.py")
    print("3. 确保async_event_loop_manager.py在Python路径中")

if __name__ == "__main__":
    main() 