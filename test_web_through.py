#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Web_through.py功能
"""
import requests
import time
import json
import threading
import subprocess
import sys
import os

def test_pytorch_gpu():
    """测试PyTorch GPU功能"""
    print("=== 测试PyTorch GPU功能 ===")
    
    try:
        import torch
        print(f"✅ PyTorch版本: {torch.__version__}")
        print(f"✅ CUDA可用: {torch.cuda.is_available()}")
        
        if torch.cuda.is_available():
            print(f"✅ CUDA设备数量: {torch.cuda.device_count()}")
            print(f"✅ 当前CUDA设备: {torch.cuda.current_device()}")
            print(f"✅ 设备名称: {torch.cuda.get_device_name()}")
            
            # 测试GPU张量操作
            x = torch.randn(3, 3).cuda()
            y = torch.randn(3, 3).cuda()
            z = torch.matmul(x, y)
            print(f"✅ GPU张量运算测试成功: {z.shape}")
        else:
            print("⚠️ CUDA不可用，使用CPU模式")
            
        import numpy as np
        print(f"✅ NumPy版本: {np.__version__}")
        
        return True
    except Exception as e:
        print(f"❌ PyTorch测试失败: {e}")
        return False

def test_dependencies():
    """测试必要依赖"""
    print("\n=== 测试必要依赖 ===")
    
    dependencies = [
        "torch", "numpy", "librosa", "soundfile", 
        "pydub", "flask", "requests", "selenium"
    ]
    
    success_count = 0
    for dep in dependencies:
        try:
            __import__(dep)
            print(f"✅ {dep}: 可用")
            success_count += 1
        except ImportError:
            print(f"❌ {dep}: 不可用")
    
    print(f"📊 依赖可用性: {success_count}/{len(dependencies)}")
    return success_count == len(dependencies)

def start_web_through_server():
    """启动Web_through服务器"""
    print("\n=== 启动Web_through服务器 ===")
    
    try:
        # 使用项目环境的Python启动服务
        python_exe = r"H:/baidudown/AI-YinMei-v2.0.0/MB7-YM2-runtime/python.exe"
        
        print("🚀 正在启动Web_through.py服务...")
        process = subprocess.Popen(
            [python_exe, "Web_through.py"],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            cwd=os.getcwd()
        )
        
        # 等待服务启动
        time.sleep(5)
        
        # 检查进程是否还在运行
        if process.poll() is None:
            print("✅ Web_through服务启动成功")
            return process
        else:
            stdout, stderr = process.communicate()
            print(f"❌ Web_through服务启动失败")
            print(f"stdout: {stdout}")
            print(f"stderr: {stderr}")
            return None
            
    except Exception as e:
        print(f"❌ 启动Web_through服务异常: {e}")
        return None

def test_web_through_api():
    """测试Web_through API"""
    print("\n=== 测试Web_through API ===")
    
    base_url = "http://127.0.0.1:1717"
    
    # 测试状态接口
    try:
        print("🔍 测试状态接口...")
        response = requests.get(f"{base_url}/status", timeout=10)
        if response.status_code == 200:
            status_data = response.json()
            print(f"✅ 状态接口正常: {status_data}")
        else:
            print(f"❌ 状态接口异常: {response.status_code}")
    except Exception as e:
        print(f"❌ 状态接口请求失败: {e}")
    
    # 测试转换任务接口
    try:
        print("🎵 测试转换任务接口...")
        test_song = "测试歌曲"
        response = requests.get(f"{base_url}/append_song/{test_song}", timeout=15)
        if response.status_code == 200:
            task_data = response.json()
            print(f"✅ 转换任务接口正常: {task_data}")
        else:
            print(f"❌ 转换任务接口异常: {response.status_code}")
    except Exception as e:
        print(f"❌ 转换任务接口请求失败: {e}")

def monitor_conversion_status():
    """监控转换状态"""
    print("\n=== 监控转换状态 ===")
    
    base_url = "http://127.0.0.1:1717"
    
    for i in range(10):  # 监控10次
        try:
            response = requests.get(f"{base_url}/status", timeout=5)
            if response.status_code == 200:
                status_data = response.json()
                print(f"📊 状态更新 {i+1}: ")
                print(f"  - 转换中: {len(status_data.get('converting', []))}")
                print(f"  - 已转换: {len(status_data.get('converted', []))}")
                print(f"  - 转换失败: {len(status_data.get('convertfail', []))}")
                print(f"  - 可用文件: {len(status_data.get('converted_file', []))}")
            else:
                print(f"❌ 状态查询失败: {response.status_code}")
        except Exception as e:
            print(f"❌ 状态查询异常: {e}")
        
        time.sleep(3)

def main():
    """主函数"""
    print("🎵 Web_through.py 功能测试开始")
    
    # 1. 测试PyTorch GPU
    if not test_pytorch_gpu():
        print("⚠️ PyTorch GPU测试失败，但继续进行其他测试")
    
    # 2. 测试依赖
    if not test_dependencies():
        print("⚠️ 部分依赖不可用，可能影响功能")
    
    # 3. 启动Web_through服务
    server_process = start_web_through_server()
    
    if server_process:
        try:
            # 4. 测试API
            test_web_through_api()
            
            # 5. 监控转换状态
            monitor_conversion_status()
            
        finally:
            # 清理：终止服务进程
            print("\n🛑 正在停止Web_through服务...")
            server_process.terminate()
            try:
                server_process.wait(timeout=5)
                print("✅ Web_through服务已停止")
            except subprocess.TimeoutExpired:
                server_process.kill()
                print("🔪 强制终止Web_through服务")
    
    print("\n🎉 Web_through.py 功能测试完成！")

if __name__ == "__main__":
    main()
