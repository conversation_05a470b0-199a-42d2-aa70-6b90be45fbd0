#coding=UTF-8
"""
演示酷狗音乐搜索改进和歌曲存在性检查的功能
"""
import os
import sys
import re

# 添加项目根目录到路径
sys.path.append('.')

def demo_filename_generation():
    """演示文件名生成逻辑"""
    print("=" * 60)
    print("演示文件名生成逻辑")
    print("=" * 60)
    
    # 模拟歌曲信息
    test_songs = [
        ("稻香", "周杰伦"),
        ("轻舟", "张杰"),
        ("轻舟(DJ阿卓版)", "蓝光乐队"),
        ("戏说", "草帽酱"),
        ("花桥流水", "乌兰托娅"),
        ("いつも何度でも", "伊藤サチコ"),
    ]
    
    for i, (song_name, artist_name) in enumerate(test_songs, 1):
        print(f"\n[{i}] 原始信息:")
        print(f"   歌曲名: {song_name}")
        print(f"   歌手名: {artist_name}")
        
        # 清理文件名中的特殊字符（模拟kugou.py的逻辑）
        clean_song_name = re.sub(r'[\[\]<>:"/\\|?*]', '_', song_name).rstrip('. ').replace('(', '_').replace(')', '').replace(' ', '')
        clean_artist_name = re.sub(r'[\[\]<>:"/\\|?*]', '_', artist_name).rstrip('. ').replace('(', '_').replace(')', '_').replace(' ', '')
        
        # 生成文件名
        if clean_artist_name:
            filename = f"{clean_artist_name}-{clean_song_name}"
            full_song_name = f"{artist_name}-{song_name}"
        else:
            filename = clean_song_name
            full_song_name = song_name
        
        print(f"   清理后歌曲名: {clean_song_name}")
        print(f"   清理后歌手名: {clean_artist_name}")
        print(f"   生成文件名: {filename}")
        print(f"   完整歌曲名: {full_song_name}")
        
        # 检查是否存在对应的文件
        possible_files = [
            f"./input/{filename}.mp3",
            f"./input/{filename}.flac",
            f"./input/{filename}.wav"
        ]
        
        existing_files = [f for f in possible_files if os.path.exists(f)]
        if existing_files:
            print(f"   ✅ 找到文件: {existing_files}")
        else:
            print(f"   ❌ 文件不存在")

def demo_file_existence_logic():
    """演示文件存在性检查逻辑"""
    print("\n" + "=" * 60)
    print("演示文件存在性检查逻辑")
    print("=" * 60)
    
    # 模拟sing_core.py中的检查逻辑
    test_cases = [
        {
            "query": "轻舟",
            "real_songname": "张杰-轻舟",
            "kugou_filename": "张杰-轻舟"
        },
        {
            "query": "蓝光乐队 - 轻舟(dj阿卓版)",
            "real_songname": "蓝光乐队-轻舟(DJ阿卓版)",
            "kugou_filename": "蓝光乐队、孟西-轻舟_DJ阿卓版"
        },
        {
            "query": "稻香",
            "real_songname": "周杰伦-稻香",
            "kugou_filename": "周杰伦-稻香"
        }
    ]
    
    speaker = "草神"  # 模拟默认说话人
    
    for i, case in enumerate(test_cases, 1):
        query = case["query"]
        real_songname = case["real_songname"]
        kugou_filename = case["kugou_filename"]
        song_path = f"./output/{real_songname}/"
        
        print(f"\n[{i}] 测试用例:")
        print(f"   查询: {query}")
        print(f"   真实歌名: {real_songname}")
        print(f"   酷狗文件名: {kugou_filename}")
        print(f"   输出路径: {song_path}")
        
        # 检查多种可能的文件格式
        possible_files = []
        
        # 1. 检查基于酷狗文件名格式的文件（在input目录）
        if kugou_filename:
            input_files = [
                f"./input/{kugou_filename}.mp3",
                f"./input/{kugou_filename}.flac",
                f"./input/{kugou_filename}.wav"
            ]
            possible_files.extend(input_files)
        
        # 2. 检查转换后的文件（在output目录）
        output_files = [
            f"{song_path}/Chord.wav",
            f"{song_path}/Vocals_{speaker}.wav",
            f"{song_path}/{real_songname}_{speaker}.wav"
        ]
        possible_files.extend(output_files)
        
        # 检查是否存在任何一个文件
        file_exists = False
        existing_files = []
        for file_path in possible_files:
            if os.path.exists(file_path):
                file_exists = True
                existing_files.append(file_path)
        
        print(f"   检查的文件路径:")
        for file_path in possible_files:
            exists = "✅" if os.path.exists(file_path) else "❌"
            print(f"     {exists} {file_path}")
        
        if file_exists:
            print(f"   🎵 结论: 歌曲已存在，可以直接播放")
            print(f"   📁 存在的文件: {existing_files}")
        else:
            print(f"   📥 结论: 歌曲不存在，需要下载和转换")

def main():
    """主演示函数"""
    print("🎵 酷狗音乐搜索改进功能演示")
    print("演示时间:", __import__('datetime').datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
    
    # 演示文件名生成
    demo_filename_generation()
    
    # 演示文件存在性检查
    demo_file_existence_logic()
    
    print("\n" + "=" * 60)
    print("演示完成")
    print("=" * 60)
    print("\n💡 改进总结:")
    print("1. ✅ 酷狗搜索增加了匹配度排序，提高搜索准确性")
    print("2. ✅ 增加了get_song_filename方法，获取标准化文件名")
    print("3. ✅ sing_core.py整合了酷狗搜索，获取真实歌名")
    print("4. ✅ 改进了文件存在性检查，支持多种文件格式和路径")
    print("5. ✅ 优化了播放逻辑，支持直接播放已下载的文件")

if __name__ == "__main__":
    main()
