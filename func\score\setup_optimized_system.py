"""
积分系统优化后的安装和配置脚本
"""

import os
import sys
import subprocess
import json
from typing import Dict, Any, List


class OptimizedSystemSetup:
    """优化系统安装器"""
    
    def __init__(self):
        self.required_packages = [
            "redis>=4.0.0",
            "pymongo>=4.0.0",
            "flask>=2.0.0"
        ]
        
        self.config_template = {
            "redis": {
                "host": "localhost",
                "port": 6379,
                "db": 0,
                "password": None,
                "socket_timeout": 5,
                "connection_pool_size": 10
            },
            "security": {
                "max_requests_per_minute": 60,
                "max_requests_per_hour": 1000,
                "max_requests_per_day": 10000,
                "enable_blacklist": True,
                "suspicious_threshold": 100
            },
            "cache": {
                "daily_score_ttl": 86400,
                "user_score_ttl": 300,
                "rank_cache_ttl": 600,
                "enable_cache": True
            },
            "monitoring": {
                "enable_metrics": True,
                "enable_alerts": True,
                "alert_thresholds": {
                    "error_rate": 5.0,
                    "response_time": 2.0,
                    "operations_per_second": 100
                }
            }
        }
    
    def check_python_version(self) -> bool:
        """检查Python版本"""
        print("检查Python版本...")
        
        if sys.version_info < (3, 7):
            print("❌ Python版本过低，需要Python 3.7+")
            return False
        
        print(f"✅ Python版本: {sys.version}")
        return True
    
    def install_dependencies(self) -> bool:
        """安装依赖包"""
        print("安装依赖包...")
        
        try:
            for package in self.required_packages:
                print(f"安装 {package}...")
                result = subprocess.run([
                    sys.executable, "-m", "pip", "install", package
                ], capture_output=True, text=True)
                
                if result.returncode != 0:
                    print(f"❌ 安装 {package} 失败: {result.stderr}")
                    return False
                else:
                    print(f"✅ {package} 安装成功")
            
            return True
            
        except Exception as e:
            print(f"❌ 安装依赖包时发生异常: {e}")
            return False
    
    def check_redis_connection(self) -> bool:
        """检查Redis连接"""
        print("检查Redis连接...")
        
        try:
            import redis
            
            client = redis.Redis(
                host=self.config_template["redis"]["host"],
                port=self.config_template["redis"]["port"],
                db=self.config_template["redis"]["db"],
                socket_timeout=5
            )
            
            client.ping()
            print("✅ Redis连接正常")
            return True
            
        except ImportError:
            print("⚠️  Redis包未安装，将使用内存缓存降级")
            return False
        except Exception as e:
            print(f"⚠️  Redis连接失败，将使用内存缓存降级: {e}")
            return False
    
    def check_mongodb_connection(self) -> bool:
        """检查MongoDB连接"""
        print("检查MongoDB连接...")
        
        try:
            from func.database.mongodb import Mongodb
            
            db = Mongodb().get_db()
            db.command('ping')
            print("✅ MongoDB连接正常")
            return True
            
        except Exception as e:
            print(f"❌ MongoDB连接失败: {e}")
            return False
    
    def create_config_file(self, config_path: str = "func/score/optimized_config.json") -> bool:
        """创建配置文件"""
        print("创建配置文件...")
        
        try:
            os.makedirs(os.path.dirname(config_path), exist_ok=True)
            
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(self.config_template, f, indent=2, ensure_ascii=False)
            
            print(f"✅ 配置文件已创建: {config_path}")
            return True
            
        except Exception as e:
            print(f"❌ 创建配置文件失败: {e}")
            return False
    
    def setup_database_indexes(self) -> bool:
        """设置数据库索引"""
        print("设置数据库索引...")
        
        try:
            from func.score.score_db import ScoreDB
            
            score_db = ScoreDB()
            # 索引在初始化时自动创建
            print("✅ 数据库索引设置完成")
            return True
            
        except Exception as e:
            print(f"❌ 设置数据库索引失败: {e}")
            return False
    
    def run_system_test(self) -> bool:
        """运行系统测试"""
        print("运行系统测试...")
        
        try:
            from func.score.test_optimized_system import OptimizedSystemTester
            
            tester = OptimizedSystemTester()
            
            # 运行基础测试
            print("执行缓存性能测试...")
            cache_result = tester.test_cache_performance()
            
            print("执行系统健康检查...")
            health_result = tester.test_system_health()
            
            print("✅ 系统测试完成")
            return True
            
        except Exception as e:
            print(f"❌ 系统测试失败: {e}")
            return False
    
    def display_setup_summary(self, results: Dict[str, bool]):
        """显示安装摘要"""
        print("=" * 60)
        print("🎉 积分系统优化安装摘要")
        print("=" * 60)
        
        for check, status in results.items():
            status_icon = "✅" if status else "❌"
            print(f"{status_icon} {check}")
        
        print("=" * 60)
        
        if all(results.values()):
            print("🎊 恭喜！积分系统优化安装完成！")
            print("\n📋 下一步操作:")
            print("1. 检查配置文件: func/score/optimized_config.json")
            print("2. 启动应用服务")
            print("3. 访问监控接口: /score/health")
            print("4. 查看系统指标: /score/metrics")
        else:
            print("⚠️  安装过程中遇到一些问题，请检查上述错误信息")
            print("\n🔧 故障排除:")
            print("1. 确保Python版本 >= 3.7")
            print("2. 确保MongoDB服务正在运行")
            print("3. 检查Redis服务状态（可选）")
            print("4. 检查网络连接和防火墙设置")
    
    def run_setup(self) -> Dict[str, bool]:
        """运行完整安装流程"""
        print("🚀 开始积分系统优化安装")
        print("=" * 60)
        
        results = {}
        
        # 1. 检查Python版本
        results["Python版本检查"] = self.check_python_version()
        
        # 2. 安装依赖包
        if results["Python版本检查"]:
            results["依赖包安装"] = self.install_dependencies()
        else:
            results["依赖包安装"] = False
        
        # 3. 检查数据库连接
        results["MongoDB连接"] = self.check_mongodb_connection()
        results["Redis连接"] = self.check_redis_connection()
        
        # 4. 创建配置文件
        results["配置文件创建"] = self.create_config_file()
        
        # 5. 设置数据库索引
        if results["MongoDB连接"]:
            results["数据库索引设置"] = self.setup_database_indexes()
        else:
            results["数据库索引设置"] = False
        
        # 6. 运行系统测试
        if results["MongoDB连接"] and results["依赖包安装"]:
            results["系统测试"] = self.run_system_test()
        else:
            results["系统测试"] = False
        
        # 显示安装摘要
        self.display_setup_summary(results)
        
        return results


def main():
    """主函数"""
    setup = OptimizedSystemSetup()
    results = setup.run_setup()
    
    # 返回安装结果
    success = all(results.values())
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
