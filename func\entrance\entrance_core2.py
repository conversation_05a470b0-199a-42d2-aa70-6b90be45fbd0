from threading import Thread
import datetime
import uuid
from func.tools.singleton_mode import singleton
from func.log.default_log import Default<PERSON>og
from func.llm.llm_core import LLm<PERSON>ore
from func.sing.sing_core import SingCore
from func.draw.draw_core import DrawCore
from func.dance.dance_core import DanceCore
from func.tools.string_util import StringUtil

@singleton
class EntranceCore:
    # 设置控制台日志
    log = DefaultLog().getLogger()

    def __init__(self):
        print("EntranceCore初始化入口--------")
        self.llmCore = LLmCore()
        self.singCore = SingCore()
        self.drawCore = DrawCore()
        self.danceCore = DanceCore()

    def msg_deal(self, traceid, query, uid, user_name):
        """
        处理消息入口
        :param traceid: 追踪ID
        :param query: 消息内容
        :param uid: 用户ID
        :param user_name: 用户名
        :return: bool
        """
        try:
            print(f"{datetime.datetime.now()} --msg_deal---收到消息: {query}")
            # 处理唱歌请求
            if self.singCore.msg_deal(traceid, query, uid, user_name):
                return True
                
            # 处理绘画请求
            if self.drawCore.msg_deal(traceid, query, uid, user_name):
                return True
                
            # 处理跳舞请求
            if self.danceCore.msg_deal_dance(traceid, query, uid, user_name):
                return True
                
            # 处理LLM聊天请求
            if self.llmCore.msg_deal(traceid, query, uid, user_name):
                return True
                
            return False
            
        except Exception as e:
            print(e)
            self.log.error(f"处理消息异常: {str(e)}")
            return False

    def insert_request(self, traceid, query, uid, user_name):
        """
        插入请求到队列
        :param traceid: 追踪ID
        :param query: 消息内容
        :param uid: 用户ID
        :param user_name: 用户名
        """
        try:
            print(f"{datetime.datetime.now()} ---insert_request--收到消息: {query}")
            # 创建请求线程
            request_thread = Thread(target=self.msg_deal, args=(traceid, query, uid, user_name))
            request_thread.start()
        except Exception as e:
            print(e)
            self.log.error(f"插入请求异常: {str(e)}") 