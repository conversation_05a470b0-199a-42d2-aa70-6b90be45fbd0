#coding=UTF-8
"""
简单测试关键词优化功能
"""

def _optimize_search_keywords(keywords):
    """
    优化搜索关键词，支持多种格式
    """
    if not keywords:
        return keywords
    
    # 去除多余的空格和标点
    keywords = keywords.strip()
    
    # 处理常见的分隔符
    keywords = keywords.replace('：', ':').replace('，', ',').replace('。', '')
    
    # 处理"的"字
    keywords = keywords.replace('的', '')
    
    # 处理引号
    keywords = keywords.replace('"', '').replace('"', '').replace('"', '').replace("'", '').replace("'", '').replace("'", '')
    
    # 处理《》书名号
    keywords = keywords.replace('《', '').replace('》', '')
    
    # 处理常见的连接词
    keywords = keywords.replace(' by ', ' - ').replace(' BY ', ' - ')
    
    # 统一中英文分隔符
    keywords = keywords.replace('—', '-').replace('–', '-')
    
    return keywords.strip()

def test_keyword_optimization():
    """测试关键词优化功能"""
    print("关键词优化测试:")
    print("=" * 50)
    
    test_cases = [
        '《稻香》',
        '"稻香"',
        '稻香的歌',
        '周杰伦：稻香',
        '周杰伦，稻香',
        '稻香 by 周杰伦',
        '稻香—周杰伦',
        '  稻香  ',
        '《周杰伦》—《稻香》',
    ]
    
    for i, original in enumerate(test_cases, 1):
        optimized = _optimize_search_keywords(original)
        print(f"[{i}] 原始: '{original}' -> 优化: '{optimized}'")

if __name__ == "__main__":
    test_keyword_optimization()
