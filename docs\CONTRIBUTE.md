# 如何给MaiCore做贡献v1.0

修改时间2025/4/5

如有修改建议或疑问，请在github上建立issue

首先，非常感谢你抽出时间来做贡献！❤️

这份文档是告诉你，当你想向MaiCore提交代码，或者想要以其他形式加入MaiCore的开发，或周边插件的开发，你可以怎么做。

我们鼓励并重视任何形式的贡献，但无序的贡献只会使麦麦的维护与更新变成一团糟。因此，我们建议您在做出贡献之前，先查看本指南。


> 另外，如果您喜欢这个项目，但只是没有时间做贡献，那也没关系。还有其他简单的方式来支持本项目并表达您的感激之情，我们也会非常高兴：
> - 给我们点一颗小星星（Star）
> - 在您的项目的readme中引用这个项目

## 目录

● [我有问题](#我有问题)
● [我想做贡献](#我想做贡献)
● [我想提出建议](#提出建议)

## 我有问题

> 如果你想问一个问题，我们会假设你已经阅读了现有的文档。

在你提问之前，最好先搜索现有的[issue](/issues)，看看是否有助于解决你的问题。如果你找到了匹配的issue，但仍需要追加说明，你可以在该issue下提出你的问题。同时，我们还建议你先在互联网上搜索答案。

如果你仍然觉得有必要提问并需要进行说明，我们建议你：

● 开一个[新Issue](/issues/new)。并尽可能详细地描述你的问题。

● 提供尽可能多的上下文信息，让我们更好地理解你遇到的问题。比如：提供版本信息（哪个分支，版本号是多少，运行环境有哪些等），具体取决于你认为相关的内容。

只要你提出的issue明确且合理，我们都会回复并尝试解决您的问题。


## 我想做贡献

> ### 项目所有权与维护
> MaiMBot项目（现更名为MaiBot，核心为MaiCore）由千石可乐SengokuCola创建，采用GPL3开源协议。
> MaiBot项目现已移动至MaiM-with-u组织下，目前主要内容由核心开发组维护，整体由核心开发组、reviewer和所有贡献者共同维护（该部分在未来将会明确）。
> 为了保证设计的统一和节省不必要的精力，以及为了对项目有整体的把控，我们对不同类型的贡献采取不同的审核策略：
> 
> #### 功能新增
> - 定义：涉及新功能添加、架构调整、重要模块重构等
> - 要求：原则上暂不接收，你可以发布issue提供新功能建议。
> 
> #### Bug修复
> - 定义：修复现有功能中的错误，包括非预期行为（需要发布issue进行确认）和运行错误，不涉及新功能或架构变动
> - 要求：由核心组成员或2名及以上reviewer同时确认才会被合并
> - 关闭：包含预期行为改动，新功能，破坏原有功能，数据库破坏性改动等的pr将会被关闭
> 
> #### 文档修补
> - 定义：修复现有文档中的错误，提供新的帮助文档
> - 要求：现需要提交至组织下docs仓库，由reviewer确认后合并


> ### 法律声明
> 当你为本项目贡献代码/文档时，你必须确认：
> 1. 你贡献的内容100%是由你创作；
> 2. 你对这些内容拥有相应的权利；
> 3. 你贡献的内容将按项目许可协议使用。


## 提出建议

这一部分指导您如何为MaiCore/MaiBot提交一个建议，包括全新的功能和对现有功能的小改进。遵循这些指南将有助于维护人员和社区了解您的建议并找到相关的建议。

在提交建议之前

● 请确保您正在使用最新版本（正式版请查看main分支，测试版查看dev分支）。

● 请确保您已经阅读了文档，以确认您的建议是否已经被实现，也许是通过单独的配置。

● 仔细阅读文档并了解项目目前是否支持该功能，也许可以通过单独的配置来实现。

● 进行一番[搜索](/issues)以查看是否已经有人提出了这个建议。如果有，请在现有的issue下添加评论，而不是新开一个issue。

● 请确保您的建议符合项目的范围和目标。你需要提出一个强有力的理由来说服项目的开发者这个功能的优点。请记住，我们希望的功能是对大多数用户有用的，而不仅仅是少数用户。如果你只是针对少数用户，请考虑编写一个插件。

### 附（暂定）：
核心组成员：@SengokuCola @tcmofashi @Rikki-Zero

reviewer：核心组+MaiBot主仓库合作者/权限者

贡献者：所有提交过贡献的用户
