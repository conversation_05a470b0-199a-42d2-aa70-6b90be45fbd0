#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的MMD连接测试
验证禁用心跳后的基本连接功能
"""

import asyncio
import sys
from func.dance.websocket_client import MMDWebSocketClient

async def test_simple_connection():
    """测试简单连接（无心跳）"""
    print("🔧 简单连接测试 (心跳已禁用)")
    print("=" * 40)
    
    client = MMDWebSocketClient()
    
    # 确认心跳状态
    print(f"心跳状态: {'已启用' if client.enable_heartbeat else '已禁用'}")
    
    try:
        # 1. 测试连接
        print("\n1. 测试连接...")
        success = await client.connect(show_messages=True)
        if not success:
            print("❌ 连接失败")
            return False
        
        print("✅ 连接成功")
        
        # 2. 测试基本请求
        print("\n2. 测试状态请求...")
        status = await client.get_status()
        if "error" in status:
            print(f"❌ 状态请求失败: {status['error']}")
            return False
        
        print(f"✅ 状态请求成功")
        print(f"   服务端状态: {status.get('status', {}).get('server_status', '未知')}")
        
        # 3. 测试搜索请求
        print("\n3. 测试搜索请求...")
        search_result = await client.search_dances("test", "both")
        if "error" in search_result:
            print(f"❌ 搜索请求失败: {search_result['error']}")
            return False
        
        dances = search_result.get("dances", [])
        print(f"✅ 搜索请求成功，找到 {len(dances)} 个舞蹈")
        
        # 4. 测试连续请求
        print("\n4. 测试连续请求...")
        for i in range(3):
            status = await client.get_status()
            if "error" in status:
                print(f"❌ 第{i+1}次请求失败: {status['error']}")
                return False
            print(f"   第{i+1}次请求成功")
        
        print("✅ 连续请求测试通过")
        
        # 5. 显示连接信息
        print("\n5. 连接信息:")
        conn_info = client.get_connection_info()
        print(f"   状态: {conn_info['status']}")
        print(f"   运行时间: {conn_info['uptime']}")
        print(f"   服务器地址: {conn_info['server_address']}")
        print(f"   心跳状态: {conn_info['heartbeat_status']}")
        print(f"   重连次数: {conn_info['reconnect_attempts']}")
        
        print("\n🎉 所有测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        print("\n6. 断开连接...")
        await client.disconnect()
        print("✅ 已断开连接")

async def test_with_heartbeat():
    """测试启用心跳的连接"""
    print("\n" + "=" * 40)
    print("🔧 心跳连接测试 (临时启用心跳)")
    print("=" * 40)
    
    client = MMDWebSocketClient()
    
    try:
        # 连接后启用心跳
        success = await client.connect(show_messages=True)
        if not success:
            print("❌ 连接失败")
            return False
        
        print("✅ 连接成功，现在启用心跳...")
        client.enable_heartbeat_monitoring(True)
        
        # 等待一段时间，观察心跳是否工作
        print("⏳ 等待心跳测试 (10秒)...")
        await asyncio.sleep(10)
        
        # 测试请求是否仍然正常
        print("🔍 测试心跳期间的请求...")
        status = await client.get_status()
        if "error" in status:
            print(f"❌ 心跳期间请求失败: {status['error']}")
            return False
        
        print("✅ 心跳期间请求正常")
        
        # 显示连接质量
        conn_info = client.get_connection_info()
        print(f"连接质量: {conn_info['connection_quality']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 心跳测试失败: {e}")
        return False
    
    finally:
        await client.disconnect()

async def main():
    """主测试流程"""
    try:
        # 首先测试简单连接
        simple_ok = await test_simple_connection()
        
        if simple_ok:
            # 如果简单连接成功，测试心跳连接
            choice = input("\n是否测试心跳连接？(y/n): ").strip().lower()
            if choice in ['y', 'yes', '是']:
                await test_with_heartbeat()
        
        print(f"\n📋 测试总结:")
        print(f"   简单连接: {'✅ 通过' if simple_ok else '❌ 失败'}")
        print(f"   建议: {'使用当前配置（心跳已禁用）' if simple_ok else '检查服务端状态'}")
        
    except KeyboardInterrupt:
        print(f"\n👋 测试被中断")
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")

if __name__ == "__main__":
    asyncio.run(main()) 