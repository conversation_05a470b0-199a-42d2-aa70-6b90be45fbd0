#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复过多DEBUG日志输出的问题
主要解决scheduler和websocket相关的DEBUG日志
"""
import logging
import os

def fix_debug_logs():
    """修复DEBUG日志问题"""
    print("🔧 开始修复DEBUG日志问题...")
    
    # 1. 设置APScheduler日志级别
    logging.getLogger('apscheduler').setLevel(logging.WARNING)
    logging.getLogger('apscheduler.scheduler').setLevel(logging.WARNING)
    logging.getLogger('apscheduler.executors').setLevel(logging.WARNING)
    logging.getLogger('apscheduler.jobstores').setLevel(logging.WARNING)
    
    # 2. 设置WebSocket相关日志级别
    logging.getLogger('websockets').setLevel(logging.WARNING)
    logging.getLogger('websockets.server').setLevel(logging.WARNING)
    logging.getLogger('websockets.protocol').setLevel(logging.WARNING)
    
    # 3. 设置其他可能产生大量DEBUG日志的模块
    logging.getLogger('urllib3').setLevel(logging.WARNING)
    logging.getLogger('requests').setLevel(logging.WARNING)
    logging.getLogger('asyncio').setLevel(logging.WARNING)
    
    print("✅ DEBUG日志级别已调整")

def create_logging_config():
    """创建日志配置文件"""
    config_content = """
# 日志配置文件
# 用于控制各个模块的日志级别

[loggers]
keys=root,apscheduler,websockets,urllib3,requests,asyncio

[handlers]
keys=consoleHandler,fileHandler

[formatters]
keys=simpleFormatter

[logger_root]
level=INFO
handlers=consoleHandler,fileHandler

[logger_apscheduler]
level=WARNING
handlers=consoleHandler
qualname=apscheduler
propagate=0

[logger_websockets]
level=WARNING
handlers=consoleHandler
qualname=websockets
propagate=0

[logger_urllib3]
level=WARNING
handlers=consoleHandler
qualname=urllib3
propagate=0

[logger_requests]
level=WARNING
handlers=consoleHandler
qualname=requests
propagate=0

[logger_asyncio]
level=WARNING
handlers=consoleHandler
qualname=asyncio
propagate=0

[handler_consoleHandler]
class=StreamHandler
level=INFO
formatter=simpleFormatter
args=(sys.stdout,)

[handler_fileHandler]
class=FileHandler
level=DEBUG
formatter=simpleFormatter
args=('logs/app.log',)

[formatter_simpleFormatter]
format=%(asctime)s - %(name)s - %(levelname)s - %(message)s
"""
    
    with open('logging.conf', 'w', encoding='utf-8') as f:
        f.write(config_content.strip())
    
    print("✅ 日志配置文件已创建: logging.conf")

def patch_scheduler_init():
    """修补调度器初始化，降低日志级别"""
    patch_content = '''
# 在controller/sched.py的开头添加以下代码来降低日志级别
import logging

# 降低APScheduler的日志级别
logging.getLogger('apscheduler').setLevel(logging.WARNING)
logging.getLogger('apscheduler.scheduler').setLevel(logging.WARNING)
logging.getLogger('apscheduler.executors').setLevel(logging.WARNING)
logging.getLogger('apscheduler.jobstores').setLevel(logging.WARNING)

# 降低WebSocket相关日志级别
logging.getLogger('websockets').setLevel(logging.WARNING)
logging.getLogger('websockets.server').setLevel(logging.WARNING)
logging.getLogger('websockets.protocol').setLevel(logging.WARNING)
'''
    
    print("📝 建议在controller/sched.py文件开头添加以下代码:")
    print(patch_content)

if __name__ == "__main__":
    print("=== 修复DEBUG日志问题 ===")
    
    # 立即修复当前会话的日志级别
    fix_debug_logs()
    
    # 创建日志配置文件
    create_logging_config()
    
    # 提供修补建议
    patch_scheduler_init()
    
    print("\n=== 修复完成 ===")
    print("建议重启应用程序以使所有更改生效")
