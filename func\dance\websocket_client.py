#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MMD舞蹈系统 - WebSocket客户端
用于查询舞蹈信息和发送执行命令到UPBGE服务端
"""

import asyncio
import websockets
import json
import sys
import time
from typing import Dict, List, Optional
from pathlib import Path
import threading

class MMDWebSocketClient:
    def __init__(self, server_host="localhost", server_port=8765):
        self.server_host = server_host
        self.server_port = server_port
        self.original_port = server_port  # 保存原始端口
        self.websocket = None
        self.is_connected = False
        self.reconnect_attempts = 0
        self.max_reconnect_attempts = 10  # 增加重连次数
        self.reconnect_interval = 3  # 秒
        self.last_connect_time = 0
        self.port_file = Path("mmd_server_port.txt")  # 端口状态文件
        
        # 心跳保活配置（暂时禁用，避免干扰服务端）
        self.heartbeat_interval = 45  # 心跳间隔（秒）- 增加间隔，避免与其他心跳冲突
        self.heartbeat_timeout = 15   # 心跳超时（秒）
        self.last_heartbeat = 0
        self.heartbeat_task = None
        self.auto_reconnect_enabled = True
        self.enable_heartbeat = False  # 🔥 临时禁用心跳机制
        
        # 🔥 修复：添加连接标识，避免多个客户端实例冲突
        self.client_id = f"mmd_client_{id(self)}"
        
        # 连接质量监控
        self.connection_quality = {
            "ping_failures": 0,
            "max_ping_failures": 3,
            "last_successful_ping": 0,
            "total_reconnects": 0
        }
        
        # 🔥 新增：同步操作支持
        self.sync_timeout = 10.0  # 同步操作超时时间
        self._sync_lock = threading.Lock()  # 同步操作锁
    
    async def connect(self, show_messages=True):
        """连接到UPBGE服务端（支持智能端口发现）"""
        # 总是进行智能端口扫描，优先选择最佳端口
        if show_messages:
            print("🔍 智能端口发现中...")
        
        # 智能端口扫描：优先选择连续端口中的最大值
        available_ports = await self._scan_available_ports(show_messages)
        
        if available_ports:
            # 选择最佳端口
            best_port = self._select_best_port(available_ports)
            if show_messages:
                print(f"🎯 选择最佳端口: {best_port} (从 {available_ports} 中选择)")
            
            self.server_port = best_port
            if await self._try_connect(show_messages=False):
                if show_messages:
                    print(f"✅ 成功连接到端口 {best_port}")
                return True
        else:
            # 如果扫描没有发现端口，尝试从端口文件读取
            if self._discover_server_port():
                if show_messages:
                    print(f"🔍 回退到端口文件: {self.server_port}")
                
                # 尝试连接端口文件中的端口
                success = await self._try_connect(show_messages)
                if success:
                    return True
        
        # 还原原始端口
        self.server_port = self.original_port
        if show_messages:
            print(f"❌ 无法找到可用的服务器")
        return False
    
    def _discover_server_port(self):
        """从端口文件发现服务器实际端口"""
        try:
            if self.port_file.exists():
                content = self.port_file.read_text().strip()
                if ':' in content:
                    host, port = content.split(':', 1)
                    self.server_host = host
                    self.server_port = int(port)
                    return True
        except Exception:
            pass
        return False
    
    async def _try_connect(self, show_messages=True):
        """尝试连接指定端口"""
        try:
            uri = f"ws://{self.server_host}:{self.server_port}"
            
            # 使用asyncio.wait_for来实现超时，兼容更多websockets版本
            try:
                # 🔥 修复：增加连接超时但不限制连接后的通信时间
                self.websocket = await asyncio.wait_for(
                    websockets.connect(
                        uri,
                        #ping_interval=None,  # 禁用自动ping，我们手动管理心跳
                        #ping_timeout=None,   # 禁用ping超时
                        close_timeout=10,    # 关闭超时10秒
                        max_size=10**7,      # 增加最大消息大小
                        max_queue=100        # 增加消息队列大小
                    ), 
                    timeout=5.0  # 连接超时5秒
                )
            except asyncio.TimeoutError:
                raise Exception("连接超时")
            
            self.is_connected = True
            self.reconnect_attempts = 0
            self.last_connect_time = time.time()
            self.last_heartbeat = time.time()
            self.connection_quality["last_successful_ping"] = time.time()
            self.connection_quality["ping_failures"] = 0
            
            # 启动心跳保活任务（如果启用）
            if self.enable_heartbeat:
                if self.heartbeat_task:
                    self.heartbeat_task.cancel()
                    try:
                        await self.heartbeat_task
                    except asyncio.CancelledError:
                        pass
                
                self.heartbeat_task = asyncio.create_task(self._heartbeat_loop())
                if show_messages:
                    print(f"🔄 心跳保活已启动 (客户端: {self.client_id}, 间隔: {self.heartbeat_interval}秒)")
            else:
                if show_messages:
                    print(f"⚠️ 心跳保活已禁用，使用简单连接模式")
            
            if show_messages:
                print(f"✅ 已连接到MMD服务端: {uri}")
            return True
        except Exception as e:
            self.is_connected = False
            if show_messages:
                print(f"❌ 连接 {self.server_host}:{self.server_port} 失败: {e}")
            return False
    
    async def _scan_available_ports(self, show_messages=True):
        """扫描可用的WebSocket端口"""
        import socket
        available_ports = []
        
        if show_messages:
            print("   扫描WebSocket端口 8765-8774...")
        
        for port in range(8765, 8775):
            # 快速TCP端口检查
            try:
                sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                sock.settimeout(0.5)  # 短超时
                result = sock.connect_ex((self.server_host, port))
                sock.close()
                
                if result == 0:
                    # TCP端口开放，尝试WebSocket连接验证
                    if await self._verify_websocket_port(port, show_messages):
                        available_ports.append(port)
                        if show_messages:
                            print(f"   ✅ WebSocket服务器: {port}")
                    elif show_messages:
                        print(f"   ⚠️ TCP开放但非WebSocket: {port}")
            except:
                pass
        
        return available_ports
    
    async def _verify_websocket_port(self, port, show_messages=False):
        """验证端口是否为WebSocket服务器"""
        try:
            temp_port = self.server_port
            self.server_port = port
            
            # 尝试简单的WebSocket连接（很短的超时）
            uri = f"ws://{self.server_host}:{port}"
            websocket = await asyncio.wait_for(
                websockets.connect(uri), 
                timeout=0.8
            )
            await websocket.close()
            
            self.server_port = temp_port  # 恢复原端口
            return True
            
        except:
            self.server_port = temp_port  # 恢复原端口
            return False
    
    def _select_best_port(self, available_ports):
        """选择最佳端口：优先选择连续端口中的最大值"""
        if not available_ports:
            return None
        
        # 如果只有一个端口，直接返回
        if len(available_ports) == 1:
            print(f"   只有一个可用端口: {available_ports[0]}")
            return available_ports[0]
        
        # 寻找从8765开始的连续端口序列
        available_ports.sort()
        print(f"   分析端口序列: {available_ports}")
        
        # 检查是否有从8765开始的连续序列
        continuous_from_8765 = []
        for port in available_ports:
            if port == 8765 or (continuous_from_8765 and port == continuous_from_8765[-1] + 1):
                continuous_from_8765.append(port)
            else:
                break
        
        # 如果有从8765开始的连续序列，选择最大的
        if continuous_from_8765 and len(continuous_from_8765) >= 2:
            best_port = max(continuous_from_8765)
            print(f"   连续序列: {continuous_from_8765} → 选择最大值: {best_port}")
            return best_port
        
        # 否则选择最小的可用端口（更保守的选择）
        best_port = min(available_ports)
        print(f"   无连续序列 → 选择最小值: {best_port}")
        return best_port
    
    async def _heartbeat_loop(self):
        """心跳保活循环"""
        try:
            while self.is_connected and self.websocket:
                await asyncio.sleep(self.heartbeat_interval)
                
                if not self.is_connected:
                    break
                
                # 发送心跳ping。and not self.websocket.closed
                try:
                    if self.websocket:
                        pong_waiter = await self.websocket.ping()
                        await asyncio.wait_for(pong_waiter, timeout=self.heartbeat_timeout)
                        
                        self.last_heartbeat = time.time()
                        self.connection_quality["last_successful_ping"] = time.time()
                        self.connection_quality["ping_failures"] = 0
                        # print(f"💓 {self.client_id} 心跳正常")
                    else:
                        # WebSocket已关闭
                        print(f"⚠️ {self.client_id} WebSocket已关闭")
                        self.is_connected = False
                        break
                    
                except asyncio.TimeoutError:
                    self.connection_quality["ping_failures"] += 1
                    print(f"⚠️ {self.client_id} 心跳超时 ({self.connection_quality['ping_failures']}/{self.connection_quality['max_ping_failures']})")
                    
                    if self.connection_quality["ping_failures"] >= self.connection_quality["max_ping_failures"]:
                        print(f"❌ {self.client_id} 心跳失败次数过多，标记连接断开")
                        self.is_connected = False
                        break
                
                except asyncio.CancelledError:
                    print(f"🔄 {self.client_id} 心跳任务被取消")
                    break
                
                except Exception as e:
                    print(f"❌ {self.client_id} 心跳错误: {e}")
                    self.is_connected = False
                    break
        
        except asyncio.CancelledError:
            print(f"🔄 {self.client_id} 心跳循环被取消")
            return
        
        except Exception as e:
            print(f"❌ {self.client_id} 心跳循环异常: {e}")
        
        # 如果心跳循环结束且启用自动重连，尝试重连
        if not self.is_connected and self.auto_reconnect_enabled:
            print(f"🔄 {self.client_id} 心跳循环结束，启动自动重连...")
            # 使用更安全的方式启动后台重连
            try:
                asyncio.create_task(self._background_reconnect())
            except RuntimeError as e:
                print(f"⚠️ {self.client_id} 无法启动后台重连: {e}")
    
    async def _background_reconnect(self):
        """后台自动重连"""
        try:
            print(f"🔄 {self.client_id} 开始后台重连流程...")
            
            while not self.is_connected and self.auto_reconnect_enabled:
                if self.reconnect_attempts >= self.max_reconnect_attempts:
                    print(f"❌ {self.client_id} 已达到最大重连次数 ({self.max_reconnect_attempts})，暂停重连")
                    await asyncio.sleep(60)  # 等待1分钟后重置重连计数
                    self.reconnect_attempts = 0
                    continue
                
                self.connection_quality["total_reconnects"] += 1
                print(f"🔄 {self.client_id} 后台自动重连中... ({self.reconnect_attempts + 1}/{self.max_reconnect_attempts})")
                
                success = await self.auto_reconnect(show_messages=False)
                if success:
                    print(f"✅ {self.client_id} 后台重连成功!")
                    break
                
                # 指数退避重连间隔，避免频繁重连对系统造成压力
                wait_time = min(self.reconnect_interval * (2 ** min(self.reconnect_attempts, 5)), 300)  # 最多等待5分钟
                print(f"⏳ {self.client_id} 等待 {wait_time} 秒后重试...")
                await asyncio.sleep(wait_time)
        
        except asyncio.CancelledError:
            print(f"🔄 {self.client_id} 后台重连被取消")
        
        except Exception as e:
            print(f"❌ {self.client_id} 后台重连异常: {e}")

    async def disconnect(self):
        """断开连接"""
        self.auto_reconnect_enabled = False  # 禁用自动重连
        
        # 停止心跳任务
        if self.heartbeat_task:
            self.heartbeat_task.cancel()
            try:
                await self.heartbeat_task
            except asyncio.CancelledError:
                pass
            self.heartbeat_task = None
        
        if self.websocket:
            try:
                await self.websocket.close()
            except:
                pass
            self.websocket = None
            self.is_connected = False
            print("🔌 已断开连接")
    
    async def check_connection(self):
        """检查连接状态"""
        if not self.websocket:
            return False
        if not self.is_connected:
            return False
        try:
            # 快速检查WebSocket状态
            # if self.websocket.closed:
            #     self.is_connected = False
            #     return False
            
            # 检查最近心跳时间（如果启用心跳）
            if self.enable_heartbeat:
                current_time = time.time()
                if current_time - self.last_heartbeat > (self.heartbeat_interval + self.heartbeat_timeout):
                    print(f"⚠️ 心跳超时过久，连接可能已断开")
                    self.is_connected = False
                    return False
            return True
        except Exception as e:
            print(f"❌ 检查连接状态失败: {e}")
            self.is_connected = False
            return False
    
    async def auto_reconnect(self, show_messages=True):
        """自动重连（支持端口重新发现）"""
        if self.reconnect_attempts >= self.max_reconnect_attempts:
            if show_messages:
                print(f"❌ 已达到最大重连次数 ({self.max_reconnect_attempts})")
            return False
        
        self.reconnect_attempts += 1
        if show_messages:
            print(f"🔄 尝试重连 ({self.reconnect_attempts}/{self.max_reconnect_attempts})...")
        
        # 等待重连间隔
        await asyncio.sleep(self.reconnect_interval)
        
        # 重新尝试端口发现
        if self._discover_server_port():
            if show_messages:
                print(f"🔍 重连时发现新端口: {self.server_port}")
        
        # 尝试连接
        success = await self.connect(show_messages=False)
        if success:
            if show_messages:
                print(f"✅ 重连成功!")
            return True
        else:
            if show_messages:
                print(f"❌ 重连失败")
            return False
    
    async def send_request(self, request_type: str, data: Dict = None) -> Dict:
        """发送请求到服务端（带重连功能）"""
        # 检查连接状态
        if not await self.check_connection():
            print("🔄 连接已断开，尝试重连...")
            success = await self.auto_reconnect()
            if not success:
                return {"error": "无法连接到服务端"}
        
        message = {
            "type": request_type,
            "data": data or {}
        }
        
        try:
            print(f"🔄 发送请求: {message}")
            await self.websocket.send(json.dumps(message, ensure_ascii=False))
            
            # 🔥 修复：添加接收超时机制，避免无限等待
            try:
                response = await asyncio.wait_for(
                    self.websocket.recv(), 
                    timeout=30.0  # 30秒超时
                )
                return json.loads(response)
            except asyncio.TimeoutError:
                print("❌ 服务端响应超时")
                self.is_connected = False  # 标记连接断开
                return {"error": "服务端响应超时"}
        except websockets.exceptions.ConnectionClosed:
            print("🔄 连接已关闭，尝试重连...")
            self.is_connected = False
            success = await self.auto_reconnect()
            if success:
                # 重连成功后重试发送
                try:
                    await self.websocket.send(json.dumps(message, ensure_ascii=False))
                    
                    # 重试时也使用超时机制
                    try:
                        response = await asyncio.wait_for(
                            self.websocket.recv(), 
                            timeout=30.0
                        )
                        return json.loads(response)
                    except asyncio.TimeoutError:
                        print("❌ 重连后响应超时")
                        return {"error": "重连后响应超时"}
                        
                except Exception as e:
                    return {"error": f"重连后通信失败: {e}"}
            else:
                return {"error": "重连失败"}
        except Exception as e:
            return {"error": f"通信错误: {e}"}
    
    async def manual_reconnect(self, target_port=None):
        """手动重连"""
        print("🔄 手动重连中...")
        await self.disconnect()
        self.reconnect_attempts = 0  # 重置重连计数
        
        # 如果指定了目标端口，直接使用
        if target_port:
            print(f"🎯 尝试连接指定端口: {target_port}")
            self.server_port = target_port
            return await self._try_connect()
        
        return await self.connect()
    
    def enable_heartbeat_monitoring(self, enable=True):
        """启用或禁用心跳监控"""
        old_status = self.enable_heartbeat
        self.enable_heartbeat = enable
        
        if enable and not old_status and self.is_connected:
            # 立即启动心跳任务
            if self.heartbeat_task:
                self.heartbeat_task.cancel()
            self.heartbeat_task = asyncio.create_task(self._heartbeat_loop())
            print(f"✅ 心跳监控已启用")
        elif not enable and old_status:
            # 停止心跳任务
            if self.heartbeat_task:
                self.heartbeat_task.cancel()
                self.heartbeat_task = None
            print(f"⚠️ 心跳监控已禁用")

    def get_connection_info(self):
        """获取连接信息"""
        if self.is_connected:
            uptime = time.time() - self.last_connect_time
            last_heartbeat_ago = time.time() - self.last_heartbeat
            return {
                "status": "已连接",
                "uptime": f"{uptime:.3f}秒",
                "reconnect_attempts": self.reconnect_attempts,
                "server_address": f"{self.server_host}:{self.server_port}",
                "port_discovered": self.server_port != self.original_port,
                "heartbeat_status": f"{last_heartbeat_ago:.3f}秒前" if self.enable_heartbeat else "已禁用",
                "auto_reconnect": self.auto_reconnect_enabled,
                "heartbeat_enabled": self.enable_heartbeat,
                "connection_quality": self.connection_quality
            }
        else:
            return {
                "status": "未连接",
                "uptime": "0秒",
                "reconnect_attempts": self.reconnect_attempts,
                "server_address": f"{self.server_host}:{self.server_port}",
                "port_discovered": self.server_port != self.original_port,
                "heartbeat_status": "未连接",
                "auto_reconnect": self.auto_reconnect_enabled,
                "heartbeat_enabled": self.enable_heartbeat,
                "connection_quality": self.connection_quality
            }

    async def search_dances(self, query: str, search_type: str = "both") -> Dict:
        """搜索舞蹈"""
        return await self.send_request("search_dances", {
            "query": query,
            "search_type": search_type
        })
    
    async def get_dance_details(self, dance_name: str) -> Dict:
        """获取舞蹈详细信息"""
        return await self.send_request("get_dance_details", {
            "dance_name": dance_name
        })
    
    async def execute_dance(self, dance_name: str, options: Dict = None) -> Dict:
        """执行舞蹈动作"""
        default_options = {
            "play_audio": True,
            "loop": False,
            "speed": 1.0
        }
        if options:
            default_options.update(options)
        
        return await self.send_request("execute_dance", {
            "dance_name": dance_name,
            "options": default_options
        })
    
    async def stop_dance(self) -> Dict:
        """停止当前舞蹈"""
        return await self.send_request("stop_dance")
    
    async def pause_dance(self) -> Dict:
        """暂停当前舞蹈"""
        return await self.send_request("pause_dance")
    
    async def resume_dance(self) -> Dict:
        """继续播放当前舞蹈"""
        return await self.send_request("resume_dance")
    
    async def restart_dance(self) -> Dict:
        """重新开始当前舞蹈"""
        return await self.send_request("restart_dance")
    
    async def get_status(self) -> Dict:
        """获取服务端状态"""
        return await self.send_request("get_status")
    
    async def list_models(self) -> Dict:
        """获取可用模型列表"""
        return await self.send_request("list_models")
    
    async def switch_model(self, model_name: str) -> Dict:
        """切换MMD模型"""
        return await self.send_request("switch_model", {
            "model_name": model_name
        })
    
    async def seek_to_frame(self, frame: int, model_name: str = None) -> Dict:
        """跳转到指定帧"""
        data = {"frame": frame}
        if model_name:
            data["model_name"] = model_name
        return await self.send_request("seek_to_frame", data)
    
    async def set_playback_speed(self, speed: float, model_name: str = None) -> Dict:
        """设置播放速度"""
        data = {"speed": speed}
        if model_name:
            data["model_name"] = model_name
        return await self.send_request("set_speed", data)
    
    async def set_loop_mode(self, loop: bool, model_name: str = None) -> Dict:
        """设置循环播放"""
        data = {"loop": loop}
        if model_name:
            data["model_name"] = model_name
        return await self.send_request("set_loop", data)
    
    async def get_component_status(self, model_name: str = None) -> Dict:
        """获取组件详细状态"""
        data = {}
        if model_name:
            data["model_name"] = model_name
        return await self.send_request("get_component_status", data)
    
    async def quick_load_and_play(self, vmd_path: str, audio_path: str = None, 
                                 model_name: str = None, options: Dict = None) -> Dict:
        """快速加载并播放（直接使用文件路径）"""
        data = {"vmd_path": vmd_path}
        if audio_path:
            data["audio_path"] = audio_path
        if model_name:
            data["model_name"] = model_name
        if options:
            data["options"] = options
        else:
            data["options"] = {"speed": 1.0, "loop": False}
        
        return await self.send_request("quick_load_and_play", data)

    def send_command_sync(self, request_type: str, data: Dict = None) -> Dict:
        """
        同步发送命令（用于点舞等需要快速响应的操作）
        避免异步带来的延迟和复杂性
        """
        if not self.is_connected or not self.websocket:
            print("🔄 连接已断开，尝试直接重连...")
            # 直接重连，不用复杂的异步重连
            if not self._reconnect_sync():
                return {"error": "无法连接到服务器"}
        
        with self._sync_lock:
            try:
                message = {
                    "type": request_type,
                    "data": data or {}
                }
                
                # 🔥 使用简单的同步发送，避免异步开销
                loop = None
                try:
                    # 尝试获取当前事件循环
                    loop = asyncio.get_event_loop()
                    if not loop.is_running():
                        # 如果循环没有运行，直接用run_until_complete
                        result = loop.run_until_complete(
                            self._send_and_receive_sync(message)
                        )
                        return result
                    else:
                        # 如果循环正在运行，使用run_in_executor
                        future = asyncio.run_coroutine_threadsafe(
                            self._send_and_receive_sync(message), 
                            loop
                        )
                        return future.result(timeout=self.sync_timeout)
                        
                except RuntimeError:
                    # 没有事件循环，创建新的
                    new_loop = asyncio.new_event_loop()
                    try:
                        asyncio.set_event_loop(new_loop)
                        result = new_loop.run_until_complete(
                            self._send_and_receive_sync(message)
                        )
                        return result
                    finally:
                        try:
                            new_loop.close()
                        except:
                            pass
                
            except Exception as e:
                print(f"❌ 同步发送命令失败: {e}")
                # 🔥 连接失败时直接尝试重连
                if "连接" in str(e) or "connection" in str(e).lower() or "websocket" in str(e).lower():
                    print("🔄 检测到连接问题，尝试直接重连...")
                    if self._reconnect_sync():
                        print("✅ 重连成功，重试发送命令...")
                        # 重连成功后立即重试
                        try:
                            return self.send_command_sync(request_type, data)
                        except Exception as retry_e:
                            return {"error": f"重连后发送失败: {retry_e}"}
                    else:
                        return {"error": "重连失败"}
                return {"error": f"发送失败: {e}"}
    
    async def _send_and_receive_sync(self, message: Dict) -> Dict:
        """异步发送和接收（用于同步方法）"""
        try:
            await self.websocket.send(json.dumps(message, ensure_ascii=False))
            response = await asyncio.wait_for(
                self.websocket.recv(), 
                timeout=self.sync_timeout
            )
            return json.loads(response)
        except asyncio.TimeoutError:
            return {"error": "响应超时"}
        except Exception as e:
            return {"error": f"通信错误: {e}"}
    
    def _reconnect_sync(self) -> bool:
        """直接同步重连，不用复杂的异步重连"""
        try:
            print("🔄 开始直接重连...")
            
            # 先关闭旧连接
            if self.websocket:
                try:
                    # 简单粗暴地关闭
                    self.websocket = None
                except:
                    pass
            
            self.is_connected = False
            
            # 🔥 使用事件循环管理器进行重连（必要的异步操作）
            try:
                # 导入事件循环管理器
                from func.dance.async_event_loop_manager import run_async_safe_global
                
                # 使用事件循环管理器执行重连
                success = run_async_safe_global(
                    self.connect(show_messages=False),
                    timeout=8.0,  # 缩短超时时间，快速重连
                    default_value=False
                )
                
                if success:
                    print("✅ 直接重连成功!")
                    return True
                else:
                    print("❌ 直接重连失败")
                    return False
                    
            except ImportError:
                # 如果事件循环管理器不可用，回退到简单方式
                print("⚠️ 事件循环管理器不可用，使用简单重连...")
                return self._simple_reconnect()
                
        except Exception as e:
            print(f"❌ 直接重连异常: {e}")
            return False
    
    def _simple_reconnect(self) -> bool:
        """简单重连方式（不依赖事件循环管理器）"""
        try:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                success = loop.run_until_complete(self.connect(show_messages=False))
                return success
            finally:
                try:
                    loop.close()
                except:
                    pass
        except Exception as e:
            print(f"❌ 简单重连失败: {e}")
            return False
    
    def execute_dance_sync(self, dance_name: str, options: Dict = None) -> Dict:
        """同步执行舞蹈（快速响应）"""
        default_options = {
            "play_audio": True,
            "loop": False,
            "speed": 1.0
        }
        if options:
            default_options.update(options)
        
        return self.send_command_sync("execute_dance", {
            "dance_name": dance_name,
            "options": default_options
        })
    
    def stop_dance_sync(self) -> Dict:
        """同步停止舞蹈（快速响应）"""
        return self.send_command_sync("stop_dance")
    
    def search_dances_sync(self, query: str, search_type: str = "both") -> Dict:
        """同步搜索舞蹈（快速响应）"""
        return self.send_command_sync("search_dances", {
            "query": query,
            "search_type": search_type
        })
    
    def get_status_sync(self) -> Dict:
        """同步获取状态（快速响应）"""
        return self.send_command_sync("get_status")
    
    def stop_dance_action(self):
        """直接停止舞蹈动作（非异步）"""
        try:
            if not self.is_connected:
                print("🔄 未连接，尝试重连...")
                if not self._reconnect_sync():
                    return {"error": "无法连接到服务器"}
            
            # 🔥 简单直接的停止命令，避免异步复杂性
            result = self.stop_dance_sync()
            if "error" not in result:
                print("✅ 舞蹈已停止")
            return result
        except Exception as e:
            print(f"❌ 停止舞蹈失败: {e}")
            return {"error": f"停止失败: {e}"}

class MMDClientInterface:
    """交互式客户端界面"""
    
    def __init__(self):
        self.client = MMDWebSocketClient()
        self.running = True
        self.recent_dances = []  # 最近搜索的舞蹈
    
    async def start(self):
        """启动客户端界面"""
        print("🎭 MMD舞蹈控制客户端")
        print("=" * 40)
        
        await self.client.connect()
        if not self.client.is_connected:
            print("无法连接到服务端，请确保UPBGE服务端正在运行")
            print("提示：可以在菜单中选择重连")
        
        await self.show_menu()
    
    async def show_menu(self):
        """显示主菜单"""
        while self.running:
            # 显示连接状态
            conn_info = self.client.get_connection_info()
            status_icon = "🟢" if self.client.is_connected else "🔴"
            port_info = f"端口: {conn_info['server_address']}"
            if conn_info['port_discovered']:
                port_info += " (自动发现)"
            
            print("\n" + "=" * 40)
            print("MMD舞蹈控制:")
            print(f"{status_icon} 连接状态: {conn_info['status']} (运行: {conn_info['uptime']})")
            print(f"📡 {port_info}")
            print("1. 搜索并播放舞蹈")
            print("2. 播放控制")
            print("3. 查看状态")
            print("4. 切换模型")
            print("5. 高级播放控制")
            print("6. 快速播放")
            print("7. 重新连接")
            print("8. 连接诊断")
            print("0. 退出")
            print("=" * 40)
            
            try:
                choice = input("请选择 (0-8): ").strip()
                await self.handle_choice(choice)
            except KeyboardInterrupt:
                break
            except Exception as e:
                print(f"操作错误: {e}")
        
        await self.client.disconnect()
    
    async def handle_choice(self, choice: str):
        """处理用户选择"""
        if choice == "1":
            await self.search_and_play_menu()
        elif choice == "2":
            await self.playback_control_menu()
        elif choice == "3":
            await self.status_menu()
        elif choice == "4":
            await self.switch_model_menu()
        elif choice == "5":
            await self.advanced_playback_control_menu()
        elif choice == "6":
            await self.quick_play_menu()
        elif choice == "7":
            await self.reconnect_menu()
        elif choice == "8":
            await self.connection_diagnostic_menu()
        elif choice == "0":
            self.running = False
        else:
            print("无效选择，请重试")
    
    async def search_and_play_menu(self):
        """搜索并播放舞蹈菜单（简化版）"""
        if not self.client.is_connected:
            print("❌ 未连接到服务端，请先重连")
            return
        
        print("\n🔍 搜索舞蹈")
        query = input("输入舞蹈名称: ").strip()
        if not query:
            return
        
        print("搜索中...")
        result = await self.client.search_dances(query)
        
        if "error" in result:
            print(f"❌ 搜索失败: {result['error']}")
            return
        
        dances = result.get("dances", [])
        if not dances:
            print("❌ 未找到匹配的舞蹈")
            return
        
        # 显示搜索结果
        print(f"\n找到 {len(dances)} 个舞蹈:")
        for i, dance in enumerate(dances[:10], 1):  # 只显示前10个
            name = dance.get("name", "")
            translation = dance.get("translation", "")
            has_complete = "✅" if dance.get("has_complete_set") else "⚠️"
            print(f"{i}. {has_complete} {name}")
            if translation:
                print(f"   {translation}")
        
        # 选择并播放
        if len(dances) == 1:
            # 只有一个结果，直接播放
            await self.play_dance(dances[0]["name"])
        else:
            # 多个结果，让用户选择
            try:
                selection = input(f"\n选择要播放的舞蹈 (1-{min(len(dances), 10)}) 或按Enter取消: ").strip()
                if selection:
                    index = int(selection) - 1
                    if 0 <= index < min(len(dances), 10):
                        await self.play_dance(dances[index]["name"])
                    else:
                        print("无效选择")
            except ValueError:
                print("无效输入")
    
    async def play_dance(self, dance_name: str):
        """播放舞蹈（使用默认设置）"""
        print(f"\n🎭 开始播放: {dance_name}")
        
        # 先获取舞蹈详情
        details_result = await self.client.get_dance_details(dance_name)
        if "error" not in details_result:
            dance_info = details_result.get("dance_info", {})
            translation = dance_info.get("translation")
            if translation:
                print(f"   翻译: {translation}")
        
        # 执行舞蹈（使用默认设置：播放音频、不循环、正常速度）
        result = await self.client.execute_dance(dance_name)
        
        if "error" in result:
            print(f"❌ 播放失败0: {result['error']}")
        else:
            print(f"✅ 开始播放舞蹈")
            if result.get("estimated_duration"):
                print(f"预计时长: {result['estimated_duration']:.3f}秒")
            
            # 保存到最近播放列表
            if dance_name not in self.recent_dances:
                self.recent_dances.insert(0, dance_name)
                self.recent_dances = self.recent_dances[:5]  # 只保留最近5个
    
    async def playback_control_menu(self):
        """播放控制菜单"""
        if not self.client.is_connected:
            print("❌ 未连接到服务端，请先重连")
            return
        
        print("\n🎮 播放控制")
        print("1. 停止舞蹈")
        print("2. 暂停舞蹈")
        print("3. 继续播放")
        print("4. 重新开始")
        print("0. 返回主菜单")
        
        choice = input("请选择 (0-4): ").strip()
        
        if choice == "1":
            await self.stop_dance_action()
        elif choice == "2":
            await self.pause_dance_action()
        elif choice == "3":
            await self.resume_dance_action()
        elif choice == "4":
            await self.restart_dance_action()
    
    async def stop_dance_action(self):
        """停止舞蹈动作"""
        result = await self.client.stop_dance()
        
        if "error" in result:
            print(f"❌ 停止失败: {result['error']}")
        else:
            print("✅ 舞蹈已停止")
    
    async def pause_dance_action(self):
        """暂停舞蹈动作"""
        result = await self.client.pause_dance()
        
        if "error" in result:
            print(f"❌ 暂停失败: {result['error']}")
        else:
            print("⏸️ 舞蹈已暂停")
    
    async def resume_dance_action(self):
        """继续播放舞蹈动作"""
        result = await self.client.resume_dance()
        
        if "error" in result:
            print(f"❌ 继续播放失败: {result['error']}")
        else:
            print("▶️ 舞蹈已继续播放")
    
    async def restart_dance_action(self):
        """重新开始舞蹈动作"""
        result = await self.client.restart_dance()
        
        if "error" in result:
            print(f"❌ 重新开始失败: {result['error']}")
        else:
            print("🔄 舞蹈已重新开始")
    
    async def advanced_playback_control_menu(self):
        """高级播放控制菜单（原播放控制菜单）"""
        if not self.client.is_connected:
            print("❌ 未连接到服务端，请先重连")
            return
        
        print("\n🎛️ 高级播放控制")
        print("1. 设置播放速度")
        print("2. 设置循环播放")
        print("3. 跳转到指定帧")
        print("4. 获取详细状态")
        print("0. 返回主菜单")
        
        choice = input("请选择 (0-4): ").strip()
        
        if choice == "1":
            await self.set_speed_menu()
        elif choice == "2":
            await self.set_loop_menu()
        elif choice == "3":
            await self.seek_frame_menu()
        elif choice == "4":
            await self.detailed_status_menu()
    
    async def set_speed_menu(self):
        """设置播放速度"""
        try:
            speed_input = input("输入播放速度 (0.1-5.0, 默认1.0): ").strip()
            if not speed_input:
                speed = 1.0
            else:
                speed = float(speed_input)
                speed = max(0.1, min(5.0, speed))  # 限制范围
            
            model = input("指定模型名称 (留空=所有模型): ").strip() or None
            
            result = await self.client.set_playback_speed(speed, model)
            
            if "error" in result:
                print(f"❌ 设置速度失败: {result['error']}")
            else:
                print(f"✅ {result.get('message', '设置成功')}")
                
        except ValueError:
            print("❌ 无效的速度值")
    
    async def set_loop_menu(self):
        """设置循环播放"""
        loop_input = input("循环播放 (y/n, 默认n): ").strip().lower()
        loop = loop_input in ['y', 'yes', '是', 'true', '1']
        
        model = input("指定模型名称 (留空=所有模型): ").strip() or None
        
        result = await self.client.set_loop_mode(loop, model)
        
        if "error" in result:
            print(f"❌ 设置循环失败: {result['error']}")
        else:
            print(f"✅ {result.get('message', '设置成功')}")
    
    async def seek_frame_menu(self):
        """跳转到指定帧"""
        try:
            frame_input = input("输入目标帧数: ").strip()
            if not frame_input:
                return
            
            frame = int(frame_input)
            model = input("指定模型名称 (留空=默认模型): ").strip() or None
            
            result = await self.client.seek_to_frame(frame, model)
            
            if "error" in result:
                print(f"❌ 跳转失败: {result['error']}")
            else:
                print(f"✅ {result.get('message', '跳转成功')}")
                
        except ValueError:
            print("❌ 无效的帧数")
    
    async def detailed_status_menu(self):
        """详细状态菜单"""
        model = input("指定模型名称 (留空=所有模型): ").strip() or None
        
        result = await self.client.get_component_status(model)
        
        if "error" in result:
            print(f"❌ 获取状态失败: {result['error']}")
            return
        
        if "component_info" in result:
            # 单个组件状态
            info = result["component_info"]
            print(f"\n📱 组件详细状态:")
            print(f"模型名称: {info.get('model_name')}")
            print(f"播放状态: {'播放中' if info.get('is_playing') else '停止'}")
            print(f"VMD文件: {Path(info.get('vmd_file', '')).name if info.get('vmd_file') else '无'}")
            print(f"音频文件: {Path(info.get('audio_file', '')).name if info.get('audio_file') else '无'}")
            print(f"当前帧: {info.get('current_frame', 0)}")
            print(f"总帧数: {info.get('total_frames', 0)}")
            print(f"播放进度: {info.get('progress', 0):.1%}")
            print(f"播放速度: {info.get('speed', 1.0)}x")
            print(f"循环播放: {'是' if info.get('loop') else '否'}")
            print(f"播放模式: {info.get('playback_mode', '未知')}")
            print(f"动画时长: {info.get('duration_seconds', 0):.3f}秒")
            print(f"已播放时间: {info.get('elapsed_time', 0):.3f}秒")
        
        elif "all_components" in result:
            # 所有组件状态
            all_components = result["all_components"]
            print(f"\n📱 所有组件状态 ({len(all_components)}个):")
            for i, info in enumerate(all_components):
                status_icon = "▶️" if info.get('is_playing') else "⏹️"
                print(f"\n{i+1}. {info.get('model_name')}: {status_icon}")
                if info.get('vmd_file'):
                    print(f"   VMD: {Path(info['vmd_file']).name}")
                    print(f"   进度: {info.get('progress', 0):.1%} ({info.get('current_frame', 0)}/{info.get('total_frames', 0)}帧)")
                    print(f"   速度: {info.get('speed', 1.0)}x, 循环: {'是' if info.get('loop') else '否'}")
    
    async def quick_play_menu(self):
        """快速播放菜单"""
        if not self.client.is_connected:
            print("❌ 未连接到服务端，请先重连")
            return
        
        print("\n⚡ 快速播放 (直接使用文件路径)")
        
        vmd_path = input("VMD文件路径: ").strip()
        if not vmd_path:
            return
        
        audio_path = input("音频文件路径 (可选): ").strip() or None
        model = input("指定模型名称 (留空=默认): ").strip() or None
        
        # 播放选项
        try:
            speed = float(input("播放速度 (默认1.0): ").strip() or "1.0")
            speed = max(0.1, min(5.0, speed))
        except ValueError:
            speed = 1.0
        
        loop_input = input("循环播放 (y/n, 默认n): ").strip().lower()
        loop = loop_input in ['y', 'yes', '是', 'true', '1']
        
        options = {"speed": speed, "loop": loop}
        
        print("开始快速播放...")
        result = await self.client.quick_load_and_play(vmd_path, audio_path, model, options)
        
        if "error" in result:
            print(f"❌ 快速播放失败: {result['error']}")
        else:
            print(f"✅ {result.get('message', '快速播放成功')}")
    
    async def connection_diagnostic_menu(self):
        """连接诊断菜单"""
        print("\n🔧 连接诊断")
        
        # 检查端口文件
        if self.client.port_file.exists():
            try:
                content = self.client.port_file.read_text().strip()
                print(f"📄 端口文件存在: {content}")
            except Exception as e:
                print(f"❌ 读取端口文件失败: {e}")
        else:
            print("❌ 端口文件不存在")
        
        # 当前客户端配置
        print(f"🖥️ 客户端配置:")
        print(f"   主机: {self.client.server_host}")
        print(f"   端口: {self.client.server_port}")
        print(f"   原始端口: {self.client.original_port}")
        print(f"   连接状态: {self.client.is_connected}")
        
        # 端口扫描诊断
        print(f"\n🔍 端口扫描诊断 (8765-8774):")
        import socket
        
        available_ports = []
        for port in range(8765, 8775):
            try:
                sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                sock.settimeout(1)
                result = sock.connect_ex((self.client.server_host, port))
                sock.close()
                
                if result == 0:
                    available_ports.append(port)
                    print(f"   端口 {port}: ✅ 开放")
                else:
                    print(f"   端口 {port}: ❌ 关闭")
            except Exception as e:
                print(f"   端口 {port}: ❌ 错误 ({e})")
        
        # 显示端口选择逻辑
        if available_ports:
            print(f"\n🎯 端口选择分析:")
            print(f"   发现开放端口: {available_ports}")
            
            # 分析连续端口
            available_ports.sort()
            continuous_from_8765 = []
            for port in available_ports:
                if port == 8765 or (continuous_from_8765 and port == continuous_from_8765[-1] + 1):
                    continuous_from_8765.append(port)
                else:
                    break
            
            if continuous_from_8765 and len(continuous_from_8765) >= 2:
                best_port = max(continuous_from_8765)
                print(f"   连续端口序列: {continuous_from_8765}")
                print(f"   推荐端口: {best_port} (连续序列中的最大值)")
            elif available_ports:
                best_port = min(available_ports)
                print(f"   推荐端口: {best_port} (最小可用端口)")
        else:
            print(f"\n❌ 未发现任何开放端口")
        
        # WebSocket库版本信息
        print(f"\n📦 WebSocket库信息:")
        try:
            print(f"   websockets版本: {websockets.__version__}")
        except:
            print(f"   websockets版本: 未知")
        
        # 建议操作
        print(f"\n💡 建议操作:")
        print(f"   1. 检查UPBGE服务端是否运行")
        print(f"   2. 确认服务端端口 (可在UPBGE中运行 debug_mmd_system())")
        print(f"   3. 如果发现开放端口，使用选项7手动指定端口连接")
        
        input("\n按Enter返回主菜单...")
    
    async def reconnect_menu(self):
        """重连菜单"""
        print("\n🔄 重新连接")
        print("1. 自动重连（端口发现）")
        print("2. 手动指定端口")
        print("0. 返回主菜单")
        
        choice = input("请选择 (0-2): ").strip()
        
        if choice == "1":
            success = await self.client.manual_reconnect()
            if success:
                print("✅ 重连成功!")
            else:
                print("❌ 重连失败，请检查服务端是否运行")
        
        elif choice == "2":
            try:
                port_input = input("输入服务器端口 (例如: 8768): ").strip()
                if port_input:
                    port = int(port_input)
                    if 1024 <= port <= 65535:
                        success = await self.client.manual_reconnect(port)
                        if success:
                            print(f"✅ 成功连接到端口 {port}!")
                        else:
                            print(f"❌ 连接端口 {port} 失败")
                    else:
                        print("❌ 端口范围应在 1024-65535 之间")
                else:
                    print("❌ 请输入有效端口号")
            except ValueError:
                print("❌ 端口号必须是数字")
    
    async def status_menu(self):
        """显示状态"""
        print(f"\n📊 系统状态:")
        
        # 显示连接信息
        conn_info = self.client.get_connection_info()
        print(f"连接状态: {conn_info['status']}")
        print(f"连接时长: {conn_info['uptime']}")
        print(f"重连次数: {conn_info['reconnect_attempts']}")
        
        if not self.client.is_connected:
            print("❌ 未连接到服务端，无法获取服务端状态")
            return
        
        result = await self.client.get_status()
        
        if "error" in result:
            print(f"❌ 获取服务端状态失败: {result['error']}")
            return
        
        status = result.get("status", {})
        print(f"模型: {status.get('current_model', '无')}")
        print(f"播放中: {status.get('current_dance', '无')}")
        print(f"播放状态: {status.get('playback_status', '停止')}")
        print(f"已加载舞蹈: {status.get('loaded_dances_count', 0)} 个")
        print(f"活跃组件: {status.get('active_components_count', 0)} 个")
        
        # 显示组件详细状态
        components = status.get('components', [])
        if components:
            print(f"\n📱 组件状态:")
            for i, comp in enumerate(components):
                status_icon = "▶️" if comp.get('is_playing') else "⏹️"
                print(f"  {i+1}. {comp.get('model_name')}: {status_icon}")
                if comp.get('vmd_file'):
                    vmd_name = Path(comp['vmd_file']).name if comp['vmd_file'] else '无'
                    print(f"     VMD: {vmd_name}")
                    print(f"     进度: {comp.get('progress', 0):.1%}")
                    print(f"     速度: {comp.get('speed', 1.0)}x")
                    print(f"     循环: {'是' if comp.get('loop') else '否'}")
        
        # 显示最近播放的舞蹈
        if self.recent_dances:
            print(f"\n最近播放:")
            for i, dance in enumerate(self.recent_dances, 1):
                print(f"  {i}. {dance}")
    
    async def switch_model_menu(self):
        """切换模型（简化版）"""
        if not self.client.is_connected:
            print("❌ 未连接到服务端，请先重连")
            return
        
        print("\n👤 切换模型")
        
        # 获取模型列表
        result = await self.client.list_models()
        if "error" in result:
            print(f"❌ 获取模型列表失败: {result['error']}")
            return
        
        models = result.get("models", [])
        if not models:
            print("❌ 没有可用模型")
            return
        
        print(f"可用模型:")
        for i, model in enumerate(models, 1):
            print(f"{i}. {model}")
        
        try:
            selection = input(f"\n选择模型 (1-{len(models)}) 或按Enter取消: ").strip()
            if selection:
                index = int(selection) - 1
                if 0 <= index < len(models):
                    model_name = models[index]
                    result = await self.client.switch_model(model_name)
                    if "error" in result:
                        print(f"❌ 切换失败: {result['error']}")
                    else:
                        print(f"✅ 已切换到: {model_name}")
                else:
                    print("无效选择")
        except ValueError:
            print("无效输入")

async def main():
    """主函数"""
    if len(sys.argv) > 1 and sys.argv[1] == "--help":
        print("MMD舞蹈客户端")
        print("使用方法: python websocket_client.py")
        print("自动连接到 localhost:8765")
        print("具备自动重连功能")
        return
    
    interface = MMDClientInterface()
    
    try:
        await interface.start()
    except KeyboardInterrupt:
        print("\n👋 再见!")

if __name__ == "__main__":
    asyncio.run(main()) 