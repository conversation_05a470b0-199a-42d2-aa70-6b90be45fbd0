import yaml
from typing import Dict, Any, Optional

class YmlOper:
    """
    YAML 文件操作类，提供读取和写入 YAML 文件的功能
    """
    @staticmethod
    def read_yml(file_path: str) -> Optional[Dict[str, Any]]:
        """
        读取 YAML 文件内容
        :param file_path: YAML 文件路径
        :return: 解析后的字典数据，如果文件不存在或解析失败则返回 None
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except (FileNotFoundError, yaml.YAMLError) as e:
            print(f"Error reading YAML file: {e}")
            return None
    
    @staticmethod
    def write_yml(file_path: str, data: Dict[str, Any]) -> bool:
        """
        将数据写入 YAML 文件
        :param file_path: 要写入的 YAML 文件路径
        :param data: 要写入的字典数据
        :return: 是否成功写入
        """
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                yaml.safe_dump(data, f, allow_unicode=True)
            return True
        except Exception as e:
            print(f"Error writing YAML file: {e}")
            return False

    def save_config(self, config_data: Dict[str, Any], config_path: str) -> bool:
        """
        保存配置到 YAML 文件
        :param config_data: 配置数据字典
        :param config_path: 配置文件路径
        :return: 是否成功保存
        """
        return self.write_yml(config_path, config_data)
    
    def get_config(self, config_path: str) -> Optional[Dict[str, Any]]:
        """
        从 YAML 文件获取配置
        
        :param config_path: 配置文件路径
        :return: 配置字典数据，如果读取失败则返回 None
        """
        return self.read_yml(config_path)

# 示例用法
if __name__ == "__main__":
    yml_oper = YmlOper()
    
    # 测试读取
    config = yml_oper.get_config("config.yml")
    print("Current config:", config)
    
    # 测试写入
    new_config = {"key1": "value1", "key2": ["item1", "item2"]}
    if yml_oper.save_config(new_config, "config.yml"):
        print("Config saved successfully")