J:\ai\�������\ai-yinmei\func\danmaku\blivedm\handlers.c        handlers.py handle      handlers.HandlerInterface.handle        command on_client_stopped       handlers.HandlerInterface.on_client_stopped     _make_msg_callback      handlers._make_msg_callback     callback        handlers._make_msg_callback.callback    method_name     message_cls     __danmu_msg_callback    handlers.BaseHandler.__danmu_msg_callback       handlers.BaseHandler.handle     _on_heartbeat   handlers.BaseHandler._on_heartbeat      _on_danmaku     handlers.BaseHandler._on_danmaku        _on_gift        handlers.BaseHandler._on_gift   _on_buy_guard   handlers.BaseHandler._on_buy_guard      _on_super_chat  handlers.BaseHandler._on_super_chat     _on_super_chat_delete   handlers.BaseHandler._on_super_chat_delete      _on_open_live_danmaku   handlers.BaseHandler._on_open_live_danmaku      _on_open_live_gift      handlers.BaseHandler._on_open_live_gift _on_open_live_buy_guard handlers.BaseHandler._on_open_live_buy_guard    _on_open_live_super_chat        handlers.BaseHandler._on_open_live_super_chat   _on_open_live_super_chat_delete handlers.BaseHandler._on_open_live_super_chat_delete    _on_open_live_like      handlers.BaseHandler._on_open_live_like _on_room_enter  handlers.BaseHandler._on_room_enter     handlers.__pyx_scope_struct___make_msg_callback __pyx_scope_struct___make_msg_callback  Cannot overwrite C type %s      __all__ __dict__        from-import-* object has no __dict__ and no __all__     handlers        Interpreter change detected - this module can only be loaded into one interpreter per process.  name    __loader__  loader      __file__    origin      __package__ parent      __path__        submodule_search_locations      Module 'handlers' has already been imported. Re-initialisation is not supported.        builtins        cython_runtime  __builtins__    __orig_bases__  init handlers   name '%U' is not defined        exactly s       %.200s() takes %.8s %zd positional argument%.1s (%zd given)     %s() got multiple values for keyword argument '%U'      %.200s() keywords must be strings       %s() got an unexpected keyword argument '%U'    Missing type object     Argument '%.200s' has incorrect type (expected %.200s, got %.200s)              calling %R should have returned an instance of BaseException, not %R    raise: exception class must be a subclass of BaseException              free variable '%s' referenced before assignment in enclosing scope       while calling a Python object  NULL result without error in PyObject_Call      _cython_3_0_11  Shared Cython type %.200s is not a type object  Shared Cython type %.200s has the wrong size, try recompiling   keywords must be strings        __name__ must be set to a string object __qualname__ must be set to a string object     function's dictionary may not be deleted        setting function's dictionary to a non-dict     __defaults__ must be set to a tuple object      changes to cyfunction.__defaults__ will not currently affect the values used in function calls  __kwdefaults__ must be set to a dict object     changes to cyfunction.__kwdefaults__ will not currently affect the values used in function calls        __annotations__ must be set to a dict object    func_doc        __doc__ func_name       __name__        __qualname__    func_dict       func_globals    __globals__     func_closure    __closure__     func_code       __code__        func_defaults   __defaults__    __kwdefaults__  __annotations__ _is_coroutine   __module__      __reduce__      Bad call flags for CyFunction   <cyfunction %U at %p>   %.200s() takes no arguments (%zd given) %.200s() takes exactly one argument (%zd given) %.200s() takes no keyword arguments     unbound method %.200S() needs an argument       %.200s() needs an argument      _cython_3_0_11.cython_function_or_method        CythonUnboundCMethod    '%.200s' object is unsliceable  cannot fit '%.200s' into an index-sized integer '%.200s' object is not subscriptable    base class '%.200s' is not a heap type  extension type '%.200s' has no __dict__ slot, but base type '%.200s' has: either add 'cdef dict __dict__' to the extension type or add '__slots__ = [...]' to the base type     cannot import name %S           metaclass conflict: the metaclass of a derived class must be a (non-strict) subclass of the metaclasses of all its bases        __mro_entries__ must return a tuple     %s (%s:%d)      does not match          compile time Python version %d.%d of module '%.100s' %s runtime version %d.%d   BaseHandler._on_gift    HOT_RANK_CHANGED        open_models.SuperChatDeleteMessage      warning *       _on_gift        open_models.RoomEnterMessage    
        收到礼物
          _on_buy_guard   info    _on_open_live_danmaku   exception       RoomEnterMessage        
        收到弹幕
          LIVE_OPEN_PLATFORM_LIKE 
        有人上舰
          SUPER_CHAT_MESSAGE_JPN  Optional[Exception]     cline_in_traceback  enable      
        有人上舰
          COMBO_SEND  add 
        收到礼物
          
        删除醒目留言
            WIDGET_BANNER   
    一个简单的消息处理器实现，带消息分发和消息类型转换。继承并重写_on_xxx方法即可实现自己的处理器
          �E�   logged_unknown_cmds ?           Dict[str, Optional[Callable[['BaseHandler', ws_base.WebSocketClientBase, dict], Any]]]  PK_BATTLE_SETTLE        SuperChatMessage        __prepare__     on_client_stopped       BaseHandler._on_open_live_gift  web_models.DanmakuMessage       ENTRY_EFFECT    clients LIVE_OPEN_PLATFORM_SUPER_CHAT_DEL       ONLINE_RANK_COUNT       __dict__        __main__    client      GUARD_BUY       open_models.GuardBuyMessage     __qualname__    handle  __module__      __mro_entries__ self    HandlerInterface        BaseHandler._on_danmaku HeartbeatMessage    dict        �E�   web     _initializing   logger  .       LIVE_OPEN_PLATFORM_LIVE_ROOM_ENTER      HandlerInterface.on_client_stopped  models      ONLINE_RANK_V2  web_models.SuperChatDeleteMessage       DANMU_MSG       web_models.SuperChatMessage     LIVE_OPEN_PLATFORM_DM   open_models.SuperChatMessage    BaseHandler     SUPER_CHAT_MESSAGE      open_live       web_models      __danmu_msg_callback    web_models.GuardBuyMessage      _is_coroutine   open_models.GiftMessage super   __spec__        ROOM_REAL_TIME_MESSAGE_UPDATE   find    PREPARING   method      __all__ SEND_GIFT       PK_BATTLE_SETTLE_USER   _on_super_chat  callback        GuardBuyMessage __import__      open_models.LikeMessage __doc__ __class_getitem__       HandlerInterface.handle PK_BATTLE_FINAL_PROCESS gc      __set_name__    LIVE_INTERACTIVE_GAME   BaseHandler._on_heartbeat       _on_open_live_buy_guard 
        收到弹幕
          :       ws_base.WebSocketClientBase     _make_msg_callback      message 
        醒目留言
          __name__        DanmakuMessage  __init_subclass__       open_models.DanmakuMessage      
        删除醒目留言
            _on_open_live_super_chat        _CMD_CALLBACK_DICT  get _on_danmaku     SUPER_CHAT_MESSAGE_DELETE       __annotations__ BaseHandler._on_buy_guard       _HEARTBEAT      BaseHandler._on_open_live_super_chat    BaseHandler.__danmu_msg_callback    data        GiftMessage     _on_open_live_super_chat_delete __test__        
        收到心跳包
               ws_base pos     web_models.GiftMessage  method_name     BaseHandler._on_open_live_super_chat_delete     message_cls     PK_BATTLE_END   isenabled       ONLINE_RANK_TOP3        LIVE_OPEN_PLATFORM_SEND_GIFT    _BaseHandler__danmu_msg_callback        _make_msg_callback.<locals>.callback    SuperChatDeleteMessage  
        当客户端停止时调用。可以在这里close或者重新start
           typing  BaseHandler._on_open_live_danmaku       PK_BATTLE_PROCESS_NEW   blivedm PK_BATTLE_PROCESS       HOT_RANK_CHANGED_V2     LIVE_OPEN_PLATFORM_SUPER_CHAT   __metaclass__   getLogger       _on_open_live_like      NotImplementedError     BaseHandler._on_open_live_buy_guard     _on_room_enter  
        醒目留言
          command 
        点赞
                NOTICE_MSG      disable open_models     
        进入房间
          asyncio.coroutines      _on_super_chat_delete   BaseHandler._on_super_chat      web_models.HeartbeatMessage     from_command    STOP_LIVE_ROOM_LIST     
    直播消息处理器接口
       PK_BATTLE_SETTLE_V2 cmd LIVE    handlers        BaseHandler._on_room_enter      BaseHandler.handle      LIVE_OPEN_PLATFORM_GUARD        logging BaseHandler._on_super_chat_delete       BaseHandler._on_open_live_like  _on_open_live_gift      'BaseHandler'   INTERACT_WORD   room_id LikeMessage     _on_heartbeat   J:\ai\吟美打包\ai-yinmei\func\danmaku\blivedm\handlers.py   room=%d unknown cmd=%s, command=%s