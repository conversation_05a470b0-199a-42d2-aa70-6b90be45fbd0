import json
import requests
import asyncio
from func.tools.singleton_mode import singleton
from func.config.default_config import defaultConfig 
from func.log.default_log import DefaultLog
from func.gobal.data import CommonData

@singleton
class ExtractCore:
    def __init__(self):
        self.config0 = defaultConfig()
        self.log = DefaultLog().getLogger()
        self.commonData = CommonData()
        
        self.config = self.config0.get_config()
        # API配置
        self.fastgpt_url = self.config["llm"]["fastgpt"]["fastgpt_url"]
        self.fastgpt_authorization = self.config["llm"]["fastgpt"]["fastgpt_authorization"]
        
        # 提示词配置
        self.prompts = {
            "场景": "你是一个文本提取助手，你需要从用户文本中提取场景的名称放进去name字段,name字段与这些场景名称[粉色房间、神社、海岸花坊、花房、清晨房间]模糊匹配，只能返回上面这些场景名称",
            "换装": "你是一个文本提取助手，你需要从用户文本中提取更换服装的名称放进去name字段,name字段与这些服装名称[便衣、爱的翅膀、青春猫娘、眼镜猫娘]模糊匹配，只能返回上面这些服装名称",
            "唱歌": "你是一个文本提取助手，你需要从用户文本中提取歌曲的名称放进去name字段",
            "跳舞": "你是一个文本提取助手，你需要从用户文本中提取跳舞的舞蹈名称放进去name字段",
            "搜索": "你是一个文本提取助手，你需要从用户文本中提取搜索内容的标题放进去name字段和描述内容放进去content字段",
            "画画": "你是一个文本提取助手，你需要从用户文本中提取图画的名称放进去name字段和描述内容放进去content字段",
            "搜图": "你是一个文本提取助手，你需要从用户文本中提取图片名称放进去name字段"
        }

    async def extract(self, intent, message):
        """提取文本内容"""
        try:
            if intent not in self.prompts:
                return {"name": "我听不懂你说什么"}
                
            headers = {
                "Content-Type": "application/json",
                "Authorization": self.fastgpt_authorization
            }
            
            data = {
                "messages": [
                    {"role": "system", "content": self.prompts[intent]},
                    {"role": "user", "content": f"提取文本内容: {message}"}
                ],
                "stream": False
            }
            
            response = requests.post(
                self.fastgpt_url,
                headers=headers,
                json=data,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                if "choices" in result:
                    return json.loads(result["choices"][0]["message"]["content"])
            
            return {"name": "【信息回复异常】"}
            
        except Exception as e:
            print(e)
            self.log.exception(e)
            return {"name": "【信息回复异常】"}

    async def chat(self, username, chatId, message):
        """聊天功能"""
        try:
            headers = {
                "Content-Type": "application/json", 
                "Authorization": self.fastgpt_authorization
            }
            
            data = {
                "chatId": chatId,
                "messages": [
                    {"role": "user", "content": message}
                ],
                "stream": False
            }
            
            response = requests.post(
                self.fastgpt_url,
                headers=headers, 
                json=data,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                if "choices" in result:
                    return result["choices"][0]["message"]["content"]
            
            return "【信息回复异常】"
            
        except Exception as e:
            print(e)
            self.log.exception(e)
            return "【信息回复异常】" 