import requests
import json
from func.tools.singleton_mode import singleton
from func.config.default_config import defaultConfig
from func.log.default_log import DefaultLog

@singleton
class Tgw:
    def __init__(self):
        self.config = defaultConfig.get_config()
        self.log = DefaultLog.getLogger()
        self.tgw_url = "text-generation-webui"  # 实际URL需要从配置中获取
        
    async def chat(self, message, history=None, your_name="", character="", mode="Alpaca"):
        """
        与text-generation-webui进行对话
        
        Args:
            message (str): 用户输入的消息
            history (list, optional): 对话历史
            your_name (str, optional): 用户名称
            character (str, optional): 角色设定
            mode (str, optional): 对话模式，默认为"Alpaca"
            
        Returns:
            str: AI的回复内容
        """
        try:
            if history is None:
                history = []
                
            data = {
                "user_input": message,
                "max_new_tokens": 2048,  # 可配置
                "history": history,
                "mode": mode,
                "character": character,
                "your_name": your_name,
                "instruction_template": "Alpaca",
                "add_bos_token": True,
                "ban_eos_token": False,
                "skip_special_tokens": True,
                "do_sample": True,
                "seed": -1
            }
            
            headers = {
                "Content-Type": "application/json"
            }
            
            response = requests.post(
                self.tgw_url + "/chat",
                headers=headers,
                json=data,
                verify=False,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                if "choices" in result and len(result["choices"]) > 0:
                    return result["choices"][0]["message"]["content"]
                    
            return "我听不懂你说什么"
            
        except Exception as e:
            print(e)
            self.log.exception(f"【{character}】信息回复异常")
            return "我听不懂你说什么"