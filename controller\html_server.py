from http.server import SimpleHTTPRequestHandler
import socketserver
from func.gobal.data import CommonData

class MyHttpRequestHandler(SimpleHTTPRequestHandler):
    def __init__(self, *args, directory=None, **kwargs):
        self.commonData = CommonData()
        if directory is None:
            directory = './manage'
        super().__init__(*args, directory=directory, **kwargs)

def manage_run():
    DIRECTORY = "./manage"
    webport = 9000
    
    # 创建HTTP服务器
    handler = lambda *args, **kwargs: MyHttpRequestHandler(*args, directory=DIRECTORY, **kwargs)
    httpd = socketserver.TCPServer(("", webport), handler)
    
    print(f"网页服务启动0：http://127.0.0.1:{webport}")
    
    # 启动服务器
    httpd.serve_forever()

if __name__ == "__main__":
    manage_run()