from flask import Flask, jsonify, request
from flask_cors import CORS
from threading import Thread
import asyncio
# from apscheduler.schedulers.asyncio import AsyncIOScheduler
from func.gobal.data import CommonData
from .sched import bind_sched

# 初始化Flask应用
app = Flask(__name__)
CORS(app, resources={r"/*": {"origins": "*"}})

# 获取配置数据
commonData = CommonData()

# 初始化调度器
# sched1 = AsyncIOScheduler(timezone="Asia/Shanghai")

# 设置日志
logger = logging.getLogger(__name__)

app = Flask(__name__)
CORS(app, resources={r"/*": {"origins": "*"}})


def api_run():
    """启动Flask服务器"""
    app.logger.disabled = True  # 禁用Flask日志
    app.run(host="0.0.0.0", port=commonData.port)

# def bind_sched():
#     """绑定调度器任务"""
#     if "blivedm" in commonData.mode or "api" in commonData.mode:
#         # 这里可以添加定时任务
#         sched1.start()

if __name__ == "__main__":
    # 启动调度器
    bind_sched()
    
    # 在新线程中启动Flask服务器
    app_thread = Thread(target=api_run)
    app_thread.start()
    
    # 保持主线程运行
    while True:
        asyncio.sleep(10)