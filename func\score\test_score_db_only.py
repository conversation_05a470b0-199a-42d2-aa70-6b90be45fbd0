"""
单独测试 ScoreDB 模块
避免复杂依赖，专注测试数据库操作
"""

import sys
import os
import unittest
from unittest.mock import Mock, patch, MagicMock

# 添加项目根目录到path
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

def test_score_db_import():
    """测试 ScoreDB 模块导入"""
    print("=== 测试 ScoreDB 模块导入 ===")
    try:
        from func.score.score_db import ScoreDB
        print("✅ ScoreDB 模块导入成功")
        return True
    except Exception as e:
        print(f"❌ ScoreDB 模块导入失败: {e}")
        return False

def test_score_db_with_mock():
    """使用Mock测试ScoreDB功能"""
    print("\n=== 使用Mock测试ScoreDB ===")
    
    try:
        # Mock所有外部依赖
        with patch('func.score.score_db.Mongodb') as mock_mongodb, \
             patch('func.score.score_db.DefaultLog') as mock_log:
            
            # 设置Mock
            mock_log.return_value.getLogger.return_value = Mock()
            
            # Mock数据库
            mock_db = Mock()
            mock_mongodb.return_value.get_db.return_value = mock_db
            
            # Mock集合
            mock_users_col = Mock()
            mock_records_col = Mock()
            mock_chat_col = Mock()
            
            # 设置数据库集合
            def get_collection(key):
                collections = {
                    'users_list': mock_users_col,
                    'score_record': mock_records_col,
                    'chatList': mock_chat_col
                }
                return collections.get(key, Mock())
            
            mock_db.__getitem__ = Mock(side_effect=get_collection)
            
            # Mock客户端和会话
            mock_client = Mock()
            mock_session = Mock()
            mock_db.client = mock_client
            mock_client.start_session.return_value.__enter__.return_value = mock_session
            mock_session.start_transaction.return_value.__enter__.return_value = None
            
            # Mock集合操作
            mock_users_col.create_index = Mock()
            mock_records_col.create_index = Mock()
            mock_users_col.find_one.return_value = {
                "openId": "test_user",
                "userName": "测试用户",
                "score": 100,
                "userface": "avatar.jpg"
            }
            mock_users_col.update_one.return_value = Mock(matched_count=1)
            mock_records_col.insert_one.return_value = Mock(inserted_id="record_id")
            
            # 导入并测试ScoreDB
            from func.score.score_db import ScoreDB
            
            # 创建ScoreDB实例
            score_db = ScoreDB()
            
            # 测试参数验证
            assert score_db._validate_score_params("user", "name", 10, "test") == True
            assert score_db._validate_score_params("", "name", 10, "test") == False
            print("✅ 参数验证测试通过")
            
            # 测试用户参数验证
            assert score_db._validate_user_params("user", "name") == True
            assert score_db._validate_user_params("", "name") == False
            print("✅ 用户参数验证测试通过")
            
            # 测试获取积分
            result = score_db.get_score("test_user")
            assert result is not None
            print("✅ 获取积分测试通过")
            
            print("✅ ScoreDB Mock测试全部通过")
            return True
            
    except Exception as e:
        print(f"❌ ScoreDB Mock测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_score_db_validation_logic():
    """测试ScoreDB的验证逻辑"""
    print("\n=== 测试ScoreDB验证逻辑 ===")
    
    try:
        with patch('func.score.score_db.Mongodb') as mock_mongodb, \
             patch('func.score.score_db.DefaultLog') as mock_log:
            
            # 设置基本Mock
            mock_log.return_value.getLogger.return_value = Mock()
            mock_db = Mock()
            mock_mongodb.return_value.get_db.return_value = mock_db
            mock_db.__getitem__ = Mock(return_value=Mock())
            mock_db.client = Mock()
            
            from func.score.score_db import ScoreDB
            score_db = ScoreDB()
            
            # 测试各种验证情况
            test_cases = [
                # (openId, userName, score, oper, expected)
                ("user123", "用户", 10, "聊天", True),
                ("", "用户", 10, "聊天", False),
                ("user123", "", 10, "聊天", False),
                ("user123", "用户", "invalid", "聊天", False),
                ("user123", "用户", 10, "", False),
                (None, "用户", 10, "聊天", False),
                ("user123", None, 10, "聊天", False),
                ("user123", "用户", None, "聊天", False),
                ("user123", "用户", 10, None, False),
            ]
            
            for openId, userName, score, oper, expected in test_cases:
                result = score_db._validate_score_params(openId, userName, score, oper)
                assert result == expected, f"验证失败: {openId}, {userName}, {score}, {oper}"
            
            print("✅ 积分参数验证逻辑测试通过")
            
            # 测试用户参数验证
            user_test_cases = [
                ("user123", "用户", True),
                ("", "用户", False),
                ("user123", "", False),
                (None, "用户", False),
                ("user123", None, False),
            ]
            
            for openId, userName, expected in user_test_cases:
                result = score_db._validate_user_params(openId, userName)
                assert result == expected, f"用户验证失败: {openId}, {userName}"
            
            print("✅ 用户参数验证逻辑测试通过")
            return True
            
    except Exception as e:
        print(f"❌ ScoreDB验证逻辑测试失败: {e}")
        return False

def test_score_db_pagination():
    """测试分页功能"""
    print("\n=== 测试分页功能 ===")
    
    try:
        with patch('func.score.score_db.Mongodb') as mock_mongodb, \
             patch('func.score.score_db.DefaultLog') as mock_log:
            
            # 设置Mock
            mock_log.return_value.getLogger.return_value = Mock()
            mock_db = Mock()
            mock_mongodb.return_value.get_db.return_value = mock_db
            
            # Mock集合
            mock_records_col = Mock()
            mock_db.__getitem__.side_effect = lambda key: {
                'users_list': Mock(),
                'score_record': mock_records_col,
                'chatList': Mock()
            }[key]
            mock_db.client = Mock()
            
            # Mock分页查询
            mock_cursor = Mock()
            mock_records_col.find.return_value = mock_cursor
            mock_cursor.sort.return_value = mock_cursor
            mock_cursor.skip.return_value = mock_cursor
            mock_cursor.limit.return_value = [
                {"openId": "user1", "userName": "用户1", "score": 10},
                {"openId": "user2", "userName": "用户2", "score": 20}
            ]
            mock_records_col.count_documents.return_value = 25
            
            from func.score.score_db import ScoreDB
            score_db = ScoreDB()
            
            # 测试分页查询
            result = score_db.find_score_record_page("用户", 1, 10)
            
            assert "data" in result
            assert "total" in result
            assert "current_page" in result
            assert "total_pages" in result
            assert result["total"] == 25
            assert result["current_page"] == 1
            assert result["total_pages"] == 3  # ceil(25/10)
            
            print("✅ 分页功能测试通过")
            return True
            
    except Exception as e:
        print(f"❌ 分页功能测试失败: {e}")
        return False

def run_all_score_db_tests():
    """运行所有ScoreDB测试"""
    print("开始运行 ScoreDB 专项测试...\n")
    
    tests = [
        ("模块导入", test_score_db_import),
        ("Mock功能", test_score_db_with_mock),
        ("验证逻辑", test_score_db_validation_logic),
        ("分页功能", test_score_db_pagination)
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"✨ {test_name}测试通过\n")
            else:
                failed += 1
                print(f"❌ {test_name}测试失败\n")
        except Exception as e:
            failed += 1
            print(f"❌ {test_name}测试异常: {e}\n")
    
    print("=" * 50)
    print(f"ScoreDB 测试结果:")
    print(f"通过: {passed} 项")
    print(f"失败: {failed} 项")
    print(f"总计: {passed + failed} 项")
    print("=" * 50)
    
    if failed == 0:
        print("🎉 ScoreDB 所有测试都通过了！")
        return True
    else:
        print("⚠️  ScoreDB 有测试失败")
        return False

if __name__ == "__main__":
    success = run_all_score_db_tests()
    
    if success:
        print("\n🏆 ScoreDB 模块测试完全通过！")
        print("   - 模块导入正常")
        print("   - 参数验证完善")
        print("   - 数据库操作逻辑正确")
        print("   - 分页功能正常")
    else:
        print("\n❌ ScoreDB 模块测试未完全通过")
    
    exit(0 if success else 1) 