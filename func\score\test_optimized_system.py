"""
积分系统优化后的测试脚本
验证新功能和性能改进
"""

import time
import threading
import random
from typing import List, Dict, Any
from func.score.oper_score import OperScore
from func.score.score_db import ScoreDB
from func.score.score_cache import ScoreCacheManager
from func.score.score_limiter import ScoreRateLimiter, ScoreSecurityManager
from func.score.score_metrics import ScoreMetrics, ScoreAlertManager
from func.log.default_log import DefaultLog


class OptimizedSystemTester:
    """优化后系统测试器"""
    
    def __init__(self):
        self.log = DefaultLog().getLogger()
        self.oper_score = OperScore()
        self.score_db = ScoreDB()
        
    def test_cache_performance(self) -> Dict[str, Any]:
        """测试缓存性能"""
        print("=" * 50)
        print("测试缓存性能")
        print("=" * 50)
        
        test_user = "test_cache_user"
        results = {
            "cache_enabled": {},
            "cache_disabled": {},
            "improvement": {}
        }
        
        # 测试启用缓存的性能
        start_time = time.time()
        for i in range(100):
            self.score_db.get_score(test_user, use_cache=True)
        cache_enabled_time = time.time() - start_time
        results["cache_enabled"]["time"] = cache_enabled_time
        results["cache_enabled"]["operations"] = 100
        
        # 测试禁用缓存的性能
        start_time = time.time()
        for i in range(100):
            self.score_db.get_score(test_user, use_cache=False)
        cache_disabled_time = time.time() - start_time
        results["cache_disabled"]["time"] = cache_disabled_time
        results["cache_disabled"]["operations"] = 100
        
        # 计算性能提升
        if cache_disabled_time > 0:
            improvement = ((cache_disabled_time - cache_enabled_time) / cache_disabled_time) * 100
            results["improvement"]["percentage"] = round(improvement, 2)
        
        print(f"启用缓存: {cache_enabled_time:.3f}秒")
        print(f"禁用缓存: {cache_disabled_time:.3f}秒")
        print(f"性能提升: {results['improvement'].get('percentage', 0)}%")
        
        return results
    
    def test_concurrent_operations(self) -> Dict[str, Any]:
        """测试并发操作安全性"""
        print("=" * 50)
        print("测试并发操作安全性")
        print("=" * 50)
        
        test_user = "test_concurrent_user"
        initial_score = 1000
        operation_count = 50
        thread_count = 10
        
        # 初始化测试用户
        self.score_db.user_refresh(test_user, "测试用户", "test.jpg")
        self.score_db.oper_score(test_user, "测试用户", initial_score, "初始化")
        
        results = {
            "initial_score": initial_score,
            "expected_final_score": initial_score + (operation_count * thread_count),
            "actual_final_score": 0,
            "operations_completed": 0,
            "operations_failed": 0,
            "data_consistency": False
        }
        
        # 并发操作计数器
        completed_operations = threading.Event()
        operation_results = {"success": 0, "failed": 0}
        lock = threading.Lock()
        
        def concurrent_operation():
            """并发操作函数"""
            for i in range(operation_count):
                try:
                    result = self.score_db.oper_score(test_user, "测试用户", 1, "并发测试")
                    with lock:
                        if result:
                            operation_results["success"] += 1
                        else:
                            operation_results["failed"] += 1
                except Exception as e:
                    with lock:
                        operation_results["failed"] += 1
                    print(f"并发操作异常: {e}")
        
        # 启动并发线程
        threads = []
        start_time = time.time()
        
        for i in range(thread_count):
            thread = threading.Thread(target=concurrent_operation)
            threads.append(thread)
            thread.start()
        
        # 等待所有线程完成
        for thread in threads:
            thread.join()
        
        end_time = time.time()
        
        # 检查最终结果
        final_user_info = self.score_db.get_score(test_user)
        if final_user_info:
            results["actual_final_score"] = final_user_info.get("score", 0)
        
        results["operations_completed"] = operation_results["success"]
        results["operations_failed"] = operation_results["failed"]
        results["data_consistency"] = (results["actual_final_score"] == results["expected_final_score"])
        results["execution_time"] = round(end_time - start_time, 3)
        
        print(f"初始积分: {results['initial_score']}")
        print(f"预期最终积分: {results['expected_final_score']}")
        print(f"实际最终积分: {results['actual_final_score']}")
        print(f"成功操作: {results['operations_completed']}")
        print(f"失败操作: {results['operations_failed']}")
        print(f"数据一致性: {'✓' if results['data_consistency'] else '✗'}")
        print(f"执行时间: {results['execution_time']}秒")
        
        return results
    
    def test_rate_limiting(self) -> Dict[str, Any]:
        """测试限流功能"""
        print("=" * 50)
        print("测试限流功能")
        print("=" * 50)
        
        test_user = "test_rate_limit_user"
        results = {
            "requests_sent": 0,
            "requests_allowed": 0,
            "requests_blocked": 0,
            "rate_limiting_working": False
        }
        
        # 快速发送大量请求
        for i in range(100):
            try:
                result = self.oper_score.oper_score(test_user, "测试用户", 1, "test.jpg", "chat")
                results["requests_sent"] += 1
                
                if result is not None:
                    results["requests_allowed"] += 1
                else:
                    results["requests_blocked"] += 1
                    
            except Exception as e:
                results["requests_blocked"] += 1
                if "频率超限" in str(e) or "rate limit" in str(e).lower():
                    results["rate_limiting_working"] = True
        
        # 检查限流是否生效
        if results["requests_blocked"] > 0:
            results["rate_limiting_working"] = True
        
        print(f"发送请求: {results['requests_sent']}")
        print(f"允许请求: {results['requests_allowed']}")
        print(f"阻止请求: {results['requests_blocked']}")
        print(f"限流功能: {'✓' if results['rate_limiting_working'] else '✗'}")
        
        return results
    
    def test_metrics_collection(self) -> Dict[str, Any]:
        """测试指标收集"""
        print("=" * 50)
        print("测试指标收集")
        print("=" * 50)
        
        # 执行一些操作来生成指标
        test_users = [f"metrics_user_{i}" for i in range(10)]
        
        for user in test_users:
            self.oper_score.oper_score(user, f"用户{user}", 10, "test.jpg", "chat")
            time.sleep(0.1)  # 避免过快操作
        
        # 获取指标
        metrics = self.oper_score.get_metrics_summary()
        
        print("指标收集结果:")
        if "basic_stats" in metrics:
            basic = metrics["basic_stats"]
            print(f"  总操作数: {basic.get('total_operations', 0)}")
            print(f"  成功率: {basic.get('success_rate', 0)}%")
            print(f"  平均响应时间: {basic.get('avg_response_time', 0)}秒")
            print(f"  活跃用户: {basic.get('daily_active_users', 0)}")
        
        return metrics
    
    def test_system_health(self) -> Dict[str, Any]:
        """测试系统健康检查"""
        print("=" * 50)
        print("测试系统健康检查")
        print("=" * 50)
        
        health = self.oper_score.get_system_health()
        
        print(f"健康评分: {health.get('health_score', 0)}/100")
        print(f"系统状态: {health.get('status', 'unknown')}")
        print(f"数据库健康: {'✓' if health.get('database_health', {}).get('database_healthy', False) else '✗'}")
        print(f"缓存健康: {'✓' if health.get('database_health', {}).get('cache_healthy', False) else '✗'}")
        print(f"活跃告警: {health.get('active_alerts', 0)}")
        
        return health
    
    def run_all_tests(self) -> Dict[str, Any]:
        """运行所有测试"""
        print("🚀 开始积分系统优化测试")
        print("=" * 80)
        
        all_results = {}
        
        try:
            # 1. 缓存性能测试
            all_results["cache_performance"] = self.test_cache_performance()
            
            # 2. 并发安全测试
            all_results["concurrent_operations"] = self.test_concurrent_operations()
            
            # 3. 限流功能测试
            all_results["rate_limiting"] = self.test_rate_limiting()
            
            # 4. 指标收集测试
            all_results["metrics_collection"] = self.test_metrics_collection()
            
            # 5. 系统健康检查测试
            all_results["system_health"] = self.test_system_health()
            
            print("=" * 80)
            print("✅ 所有测试完成")
            
            # 生成测试报告
            self._generate_test_report(all_results)
            
        except Exception as e:
            print(f"❌ 测试过程中发生异常: {e}")
            all_results["error"] = str(e)
        
        return all_results
    
    def _generate_test_report(self, results: Dict[str, Any]):
        """生成测试报告"""
        print("=" * 80)
        print("📊 测试报告摘要")
        print("=" * 80)
        
        # 缓存性能
        if "cache_performance" in results:
            cache_improvement = results["cache_performance"].get("improvement", {}).get("percentage", 0)
            print(f"✓ 缓存性能提升: {cache_improvement}%")
        
        # 并发安全
        if "concurrent_operations" in results:
            consistency = results["concurrent_operations"].get("data_consistency", False)
            print(f"{'✓' if consistency else '✗'} 并发数据一致性: {'通过' if consistency else '失败'}")
        
        # 限流功能
        if "rate_limiting" in results:
            rate_limiting = results["rate_limiting"].get("rate_limiting_working", False)
            print(f"{'✓' if rate_limiting else '✗'} 限流功能: {'正常' if rate_limiting else '异常'}")
        
        # 系统健康
        if "system_health" in results:
            health_score = results["system_health"].get("health_score", 0)
            status = results["system_health"].get("status", "unknown")
            print(f"✓ 系统健康评分: {health_score}/100 ({status})")
        
        print("=" * 80)


def main():
    """主函数"""
    tester = OptimizedSystemTester()
    results = tester.run_all_tests()
    
    # 可以将结果保存到文件或发送到监控系统
    print(f"\n测试结果已保存，时间戳: {time.time()}")


if __name__ == "__main__":
    main()
