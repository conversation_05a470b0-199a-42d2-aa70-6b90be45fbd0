#coding=UTF-8
import glob
import os
from flask import Flask, jsonify, send_file,abort
from AutoConvertMusic import *

# svc_config = {
#     "model_path": r"sovits4.1\logs\44k\G_120000.pth",
#     "config_path": r"sovits4.1\logs\44k\config.json",
#     "cluster_model_path": r"sovits4.1\logs\44k\kmeans_10000.pt", # 这里填聚类模型的路径或特征索引文件的路径，如果没有就cluster_infer_ratio设置为 0
#     "cluster_infer_ratio": 0.5, # 注意：如果没有聚类或特征索引文件，就设置为 0
#     "diffusion_model_path": r"sovits4.1\logs\44k\diffusion\model_50000.pt",
#     "diffusion_config_path": r"sovits4.1\logs\44k\diffusion\config.yaml"
# }

svc_config = {
    "model_path": r"sovits4.1\logs\草神\caoshen1_57000.pth",
    "config_path": r"sovits4.1\logs\草神\config.json",
    "cluster_model_path": r"sovits4.1\logs\yinmei\kmeans_10000.pt", # 这里填聚类模型的路径或特征索引文件的路径，如果没有就cluster_infer_ratio设置为 0
    "cluster_infer_ratio": 0, # 注意：如果没有聚类或特征索引文件，就设置为 0
    "diffusion_model_path": r"sovits4.1\logs\yinmei\diffusion\model_44000.pt",
    "diffusion_config_path": r"sovits4.1\logs\yinmei\diffusion\config.yaml"
}

choose_music_platform = ["kugou", "netease", "bilibili", "youtube"]
default_task_dict = {'en':'bs-roformer-1296','vr1':'6-HP','vr2': 'De-Echo-Normal'}  # 这是走UVR5的默认配置
default_task_dict = {'ms':'bs-roformer-1296','vr1':'6-HP','vr2': 'De-Echo-Normal'}  # 这里ms会走Music-Source-Separation-Training

music_moudle=convert_music(music_platform=choose_music_platform[0], svc_config=svc_config, default_task_dict=default_task_dict)

app = Flask(__name__)
speaker = "草神"

@app.route('/status', methods=['GET'])
def get_status():
    speaker1 = speaker.replace("[中]", "[[]中[]]")
    file_list = glob.glob(f"output/*/*[!Vocals]*_{speaker1}.wav")
    file_name = []
    for f in file_list:
        filename = os.path.basename(f)
        file_name.append(filename.replace(f"_{speaker}.wav", ""))

    # 返回converting和converted的状态
    return jsonify({
        'converting': music_moudle.converting,
        'converted': music_moudle.converted,
        'convertfail': music_moudle.convertfail,
        'converted_file': file_name
    })

@app.route('/append_song/<song_name>', methods=['GET'])
def convert_task_app(song_name): #伊藤サチコ いつも何度でも，"轻舟(dj阿卓版)"
    """全流程转换（默认）"""
    status,song_name = music_moudle.add_conversion_task(music_info = song_name,
    speaker="草神")
    return jsonify({"status": status, "songName": song_name})

@app.route('/search_song/<song_name>', methods=['GET'])
def search_song_only(song_name):
    """仅搜索歌曲"""
    try:
        print(f"🔍 仅搜索歌曲: {song_name}")
        search_results = music_moudle.platform.search_music(song_name, limit=10, return_list=True)
        return jsonify({
            "status": "success",
            "songName": song_name,
            "results": search_results,
            "message": f"找到 {len(search_results)} 个搜索结果"
        })
    except Exception as e:
        print(f"❌ 搜索失败: {song_name} - {e}")
        return jsonify({
            "status": "error",
            "songName": song_name,
            "message": f"搜索失败: {str(e)}"
        })

@app.route('/download_song/<song_name>', methods=['GET'])
def download_song_only(song_name):
    """仅下载歌曲"""
    try:
        print(f"📥 仅下载歌曲: {song_name}")
        display_name, file_path = music_moudle.platform.search_and_download_music(song_name)
        return jsonify({
            "status": "success",
            "songName": display_name,
            "filePath": file_path,
            "message": f"下载成功: {display_name}"
        })
    except Exception as e:
        print(f"❌ 下载失败: {song_name} - {e}")
        return jsonify({
            "status": "error",
            "songName": song_name,
            "message": f"下载失败: {str(e)}"
        })

@app.route('/convert_song/<song_name>', methods=['GET'])
def convert_song_only(song_name):
    """仅转换歌曲（不合并）"""
    try:
        print(f"🔄 仅转换歌曲: {song_name}")

        # 检查是否已下载
        if not check_downloaded(song_name):
            return jsonify({
                "status": "error",
                "songName": song_name,
                "message": "歌曲未下载，请先执行下载步骤"
            })

        # 执行转换但不合并
        status, result_name = music_moudle.add_conversion_task_convert_only(
            music_info=song_name,
            speaker=speaker
        )

        return jsonify({
            "status": status,
            "songName": result_name,
            "message": "转换任务已添加（不包含合并）"
        })
    except Exception as e:
        print(f"❌ 转换失败: {song_name} - {e}")
        return jsonify({
            "status": "error",
            "songName": song_name,
            "message": f"转换失败: {str(e)}"
        })

@app.route('/full_process/<song_name>', methods=['GET'])
def full_process_song(song_name):
    """全流程处理（转换并合并）"""
    try:
        print(f"🎵 全流程处理: {song_name}")

        # 检查前面步骤是否完成
        check_result = check_previous_steps(song_name)
        if not check_result["ready"]:
            return jsonify({
                "status": "error",
                "songName": song_name,
                "message": f"前置步骤未完成: {check_result['missing']}"
            })

        # 执行完整流程（包含合并）
        status, result_name = music_moudle.add_conversion_task_full(
            music_info=song_name,
            speaker=speaker
        )

        return jsonify({
            "status": status,
            "songName": result_name,
            "message": "全流程任务已添加（包含转换和合并）"
        })
    except Exception as e:
        print(f"❌ 全流程处理失败: {song_name} - {e}")
        return jsonify({
            "status": "error",
            "songName": song_name,
            "message": f"全流程处理失败: {str(e)}"
        })

def check_downloaded(song_name):
    """检查歌曲是否已下载"""
    # 检查input目录中是否有对应的音频文件
    import glob
    audio_extensions = [".mp3", ".wav", ".flac", ".m4a", ".aac", ".ogg"]
    for ext in audio_extensions:
        pattern = f"input/*{song_name}*{ext}"
        if glob.glob(pattern):
            return True
    return False

def check_converted(song_name):
    """检查歌曲是否已转换（分离出人声、伴奏等）"""
    required_files = [
        f"output/{song_name}/Vocals.wav",
        f"output/{song_name}/Instrumental.wav",
        f"output/{song_name}/Chord.wav"
    ]
    return all(os.path.exists(f) for f in required_files)

def check_voice_converted(song_name, speaker):
    """检查人声是否已转换"""
    voice_file = f"output/{song_name}/Vocals_{speaker}.wav"
    return os.path.exists(voice_file)

def check_final_mixed(song_name, speaker):
    """检查最终合并文件是否存在"""
    final_file = f"output/{song_name}/{song_name}_{speaker}.wav"
    return os.path.exists(final_file)

def check_previous_steps(song_name):
    """检查前面的步骤是否都完成"""
    missing_steps = []

    if not check_downloaded(song_name):
        missing_steps.append("下载")

    if not check_converted(song_name):
        missing_steps.append("音频分离")

    if not check_voice_converted(song_name, speaker):
        missing_steps.append("人声转换")

    return {
        "ready": len(missing_steps) == 0,
        "missing": missing_steps
    }

def convert_task(song):
    try:
        print(f"🔄 正在添加转换任务: {song}")
        status, result_name = music_moudle.add_conversion_task(
            music_info=song,
            speaker=speaker
        )
        print(f"✅ 任务添加成功: {song} -> {result_name} (状态: {status})")
    except Exception as e:
        print(f"❌ 任务添加失败: {song} - {e}")
    return status, song

def auto_convert_songs():
    """自动执行转换任务"""
    print("🎵 开始自动转换歌曲...")

    # 测试歌曲列表
    test_songs = [
        "轻舟",
        # "伊藤サチコ いつも何度でも",
        # "轻舟(dj阿卓版)"
    ]

    for song in test_songs:
        # convert_task(song)
        # only_download(song)
        only_search(song)

        # url = "https://gateway.kugou.com/v5/url?dfid=3f0Lfi0SSmUE3A4e6p4Gzzbk&mid=f804fe654348c2d194390d9c3d942b6c&uuid=feee6e61e7f6f682123605ca30757522&appid=3116&clientver=11040&userid=0&clienttime=1753339380&album_id=0&area_code=1&hash=cc935b5f2dac38763b1c40e2849ae312&ssa_flag=is_fromtrack&version=11040&page_id=967177915&quality=128&album_audio_id=0&behavior=play&pid=411&cmd=26&pidversion=3001&IsFreePart=0&ppage_id=356753938%2C823673182%2C967485191&cdnBackup=1&kcard=0&module=&key=d6099df8ef981c453c3d2cfd14e4f03c&signature=b4a15e69d74f396262aa48c532d1c3ca"
        # url0 = "http://localhost:3001/song/url?hash=D792570A18CE4264876FE470E7448208"
        # response = music_moudle.platform.kugou.get(url).text
        # song_data = json.loads(response)

    print("🎉 自动转换任务添加完成！")

def only_search(song):
    try:
        print(f"🔄 正在搜索歌曲: {song}")
        search_results = music_moudle.platform.search_music(song, limit=10, return_list=True)
        print(f"🔍 搜索结果: {search_results}")
    except Exception as e:
        print(f"❌ 搜索失败: {song} - {e}")

def only_download(song):
    try:
        print(f"🔄 正在下载歌曲: {song}")
        display_name, file_path = music_moudle.platform.search_and_download_music(song)
        print(f"✅ 下载成功: {display_name} -> {file_path}")
    except Exception as e:
        print(f"❌ 下载失败: {song} - {e}")

@app.route('/check_status/<song_name>', methods=['GET'])
def check_song_status(song_name):
    """检查歌曲各个步骤的完成状态"""
    try:
        status = {
            "songName": song_name,
            "downloaded": check_downloaded(song_name),
            "converted": check_converted(song_name),
            "voiceConverted": check_voice_converted(song_name, speaker),
            "finalMixed": check_final_mixed(song_name, speaker),
            "isConverting": song_name in music_moudle.converting,
            "isConverted": song_name in music_moudle.converted,
            "isFailed": song_name in music_moudle.convertfail
        }

        # 确定当前状态
        if status["finalMixed"]:
            status["currentStatus"] = "完成"
        elif status["isConverting"]:
            status["currentStatus"] = "转换中"
        elif status["isFailed"]:
            status["currentStatus"] = "失败"
        elif status["voiceConverted"]:
            status["currentStatus"] = "已转换（未合并）"
        elif status["converted"]:
            status["currentStatus"] = "已分离"
        elif status["downloaded"]:
            status["currentStatus"] = "已下载"
        else:
            status["currentStatus"] = "未开始"

        return jsonify(status)
    except Exception as e:
        return jsonify({
            "error": str(e),
            "songName": song_name
        })

@app.route('/get_audio/<song_name>', methods=['GET'])
def get_audio(song_name):
    search_pattern = os.path.join(f"output/{song_name}/{song_name}*.wav")
    list = glob.glob(search_pattern)
    if len(list)>0:
        matching_files = list[0]
        try:
            return send_file(matching_files, as_attachment=False)
        except:
            abort(404, description="Audio file not found")

if __name__ == '__main__':
    # 启动时自动执行转换任务
    import threading
    import time

    def delayed_auto_convert():
        """延迟执行自动转换，等待服务完全启动"""
        time.sleep(3)  # 等待3秒让服务完全启动
        auto_convert_songs()

    # 在后台线程中执行自动转换
    auto_thread = threading.Thread(target=delayed_auto_convert, daemon=True)
    auto_thread.start()

    print("🚀 启动Web_through服务...")
    print("📡 服务地址: http://0.0.0.0:1717")
    print("🔗 状态接口: http://0.0.0.0:1717/status")

    app.run(host="0.0.0.0", port=1717)
