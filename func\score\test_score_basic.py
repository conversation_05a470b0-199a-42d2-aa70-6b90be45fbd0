"""
积分系统基础测试
测试核心逻辑而不依赖数据库连接
"""

import sys
import os

# 添加项目根目录到path
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

def test_score_config():
    """测试积分配置"""
    print("=== 测试积分配置 ===")
    
    try:
        from func.score.score_config import ScoreSystemConfig, ScoreOperationType, ScoreRankConfig
        
        # 测试基础配置
        print(f"聊天积分: {ScoreSystemConfig.CHAT_SCORE}")
        print(f"唱歌消耗: {ScoreSystemConfig.SING_COST}")
        print(f"跳舞消耗: {ScoreSystemConfig.DANCE_COST}")
        
        # 测试配置验证
        assert ScoreSystemConfig.CHAT_SCORE == 1
        assert ScoreSystemConfig.SING_COST == 2
        assert ScoreSystemConfig.DANCE_COST == 3
        
        # 测试操作类型验证
        assert ScoreSystemConfig.validate_operation_type("聊天")
        assert ScoreSystemConfig.validate_operation_type("唱歌")
        assert not ScoreSystemConfig.validate_operation_type("无效操作")
        
        # 测试积分数量验证
        assert ScoreSystemConfig.validate_score_amount(100)
        assert ScoreSystemConfig.validate_score_amount(-100)
        assert not ScoreSystemConfig.validate_score_amount(20000)
        
        # 测试消耗型操作判断
        assert ScoreSystemConfig.is_consumption_operation("唱歌")
        assert ScoreSystemConfig.is_consumption_operation("跳舞")
        assert not ScoreSystemConfig.is_consumption_operation("聊天")
        
        # 测试获取操作积分
        assert ScoreSystemConfig.get_operation_score("聊天") == 1
        assert ScoreSystemConfig.get_operation_score("唱歌") == -2
        assert ScoreSystemConfig.get_operation_score("跳舞") == -3
        
        print("✅ 积分配置测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 积分配置测试失败: {e}")
        return False

def test_score_enums():
    """测试枚举类型"""
    print("\n=== 测试枚举类型 ===")
    
    try:
        from func.score.score_config import ScoreOperationType
        
        # 测试枚举值
        operations = [op.value for op in ScoreOperationType]
        print(f"支持的操作类型: {operations}")
        
        assert "聊天" in operations
        assert "唱歌" in operations
        assert "跳舞" in operations
        assert "绘画" in operations
        assert "充值" in operations
        
        print("✅ 枚举类型测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 枚举类型测试失败: {e}")
        return False

def test_rank_config():
    """测试排行榜配置"""
    print("\n=== 测试排行榜配置 ===")
    
    try:
        from func.score.score_config import ScoreRankConfig
        
        # 测试排名标题
        assert ScoreRankConfig.get_rank_title(1) == "冠军"
        assert ScoreRankConfig.get_rank_title(2) == "亚军"
        assert ScoreRankConfig.get_rank_title(3) == "季军"
        assert ScoreRankConfig.get_rank_title(5) == "第5名"
        
        print("✅ 排行榜配置测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 排行榜配置测试失败: {e}")
        return False

def test_message_config():
    """测试消息配置"""
    print("\n=== 测试消息配置 ===")
    
    try:
        from func.score.score_config import ScoreMessageConfig
        
        # 测试成功消息
        msg = ScoreMessageConfig.get_success_message("recharge", score=100)
        assert "100" in msg
        print(f"充值成功消息: {msg}")
        
        # 测试错误消息
        error_msg = ScoreMessageConfig.get_error_message("insufficient_score", score=50, required=100)
        assert "50" in error_msg and "100" in error_msg
        print(f"积分不足消息: {error_msg}")
        
        print("✅ 消息配置测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 消息配置测试失败: {e}")
        return False

def test_config_file_operations():
    """测试配置文件操作"""
    print("\n=== 测试配置文件操作 ===")
    
    try:
        from func.score.score_config import ScoreSystemConfig, save_config_to_file, load_config_from_file
        import tempfile
        import os
        
        # 创建临时配置文件
        temp_file = os.path.join(tempfile.gettempdir(), "test_score_config.json")
        
        # 获取配置并保存
        config = ScoreSystemConfig.get_config_dict()
        success = save_config_to_file(config, temp_file)
        assert success, "配置保存失败"
        
        # 加载配置
        loaded_config = load_config_from_file(temp_file)
        assert "score_rules" in loaded_config
        assert "limits" in loaded_config
        
        # 清理临时文件
        if os.path.exists(temp_file):
            os.remove(temp_file)
        
        print("✅ 配置文件操作测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 配置文件操作测试失败: {e}")
        return False

def test_score_validation_logic():
    """测试积分验证逻辑"""
    print("\n=== 测试积分验证逻辑 ===")
    
    try:
        from func.score.score_config import ScoreSystemConfig
        
        # 测试边界值
        valid_scores = [1, 100, 1000, 9999, -1, -100, -1000]
        invalid_scores = [10001, -10001, 50000, "invalid", None]
        
        for score in valid_scores:
            assert ScoreSystemConfig.validate_score_amount(score), f"Valid score {score} failed validation"
        
        for score in invalid_scores:
            if score is not None and isinstance(score, (int, float)):
                assert not ScoreSystemConfig.validate_score_amount(score), f"Invalid score {score} passed validation"
        
        # 测试操作类型验证
        valid_operations = ["聊天", "唱歌", "跳舞", "绘画", "充值", "扣减"]
        invalid_operations = ["invalid", "", None, "未知操作"]
        
        for op in valid_operations:
            assert ScoreSystemConfig.validate_operation_type(op), f"Valid operation {op} failed validation"
        
        for op in invalid_operations:
            if op is not None:
                assert not ScoreSystemConfig.validate_operation_type(op), f"Invalid operation {op} passed validation"
        
        print("✅ 积分验证逻辑测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 积分验证逻辑测试失败: {e}")
        return False

def test_performance_configs():
    """测试性能相关配置"""
    print("\n=== 测试性能配置 ===")
    
    try:
        from func.score.score_config import ScoreSystemConfig
        
        # 测试分页配置
        assert ScoreSystemConfig.DEFAULT_PAGE_SIZE > 0
        assert ScoreSystemConfig.MAX_PAGE_SIZE >= ScoreSystemConfig.DEFAULT_PAGE_SIZE
        assert ScoreSystemConfig.MAX_RANK_LIMIT > 0
        
        # 测试缓存配置
        assert ScoreSystemConfig.SCORE_CACHE_TTL > 0
        assert ScoreSystemConfig.RANK_CACHE_TTL > 0
        
        # 测试批量操作配置
        assert ScoreSystemConfig.BATCH_OPERATION_SIZE > 0
        
        print(f"默认分页大小: {ScoreSystemConfig.DEFAULT_PAGE_SIZE}")
        print(f"最大分页大小: {ScoreSystemConfig.MAX_PAGE_SIZE}")
        print(f"积分缓存时间: {ScoreSystemConfig.SCORE_CACHE_TTL}秒")
        print(f"排行榜缓存时间: {ScoreSystemConfig.RANK_CACHE_TTL}秒")
        
        print("✅ 性能配置测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 性能配置测试失败: {e}")
        return False

def run_all_tests():
    """运行所有测试"""
    print("开始运行积分系统基础测试...\n")
    
    tests = [
        test_score_config,
        test_score_enums,
        test_rank_config,
        test_message_config,
        test_config_file_operations,
        test_score_validation_logic,
        test_performance_configs
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"❌ 测试 {test.__name__} 异常: {e}")
            failed += 1
    
    print(f"\n=== 测试结果总结 ===")
    print(f"通过: {passed}")
    print(f"失败: {failed}")
    print(f"总计: {passed + failed}")
    
    if failed == 0:
        print("🎉 所有测试都通过了！")
        return True
    else:
        print("⚠️  有测试失败，请检查上述错误信息")
        return False

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1) 