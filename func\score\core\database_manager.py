"""
数据库管理器
负责数据库连接、索引管理、基础操作等功能
"""

import time
import pymongo
from contextlib import contextmanager
from typing import Dict, List, Optional, Any
from pymongo.errors import DuplicateKeyError, OperationFailure

from func.database.mongodb import Mongodb
from func.log.default_log import DefaultLog
from func.score.score_cache import ScoreCacheManager
from func.score.score_metrics import ScoreMetrics


class DatabaseManager:
    """数据库管理器"""

    def __init__(self):
        """初始化数据库管理器"""
        self.log = DefaultLog().getLogger()
        self.db = Mongodb().get_db()
        self.client = self.db.client

        # 集合引用
        self.users_collection = self.db['users_list']
        self.records_collection = self.db['score_record']
        self.chatlist_collection = self.db['chatList']

        # 初始化缓存管理器和指标收集器
        self.cache_manager = ScoreCacheManager()
        self.metrics = ScoreMetrics()

        # 确保索引存在以提高查询性能
        self._create_indexes()

        self.log.info("数据库管理器初始化完成")

    def _create_indexes(self):
        """创建数据库索引以提高查询性能"""
        try:
            # 首先清理重复数据
            self._cleanup_duplicate_users()

            # 用户表索引 - 使用更安全的索引创建方式
            self._create_index_safely(self.users_collection, "openId", unique=True)
            self._create_index_safely(self.users_collection, [("score", -1)])  # 积分排行榜查询
            self._create_index_safely(self.users_collection, [("createTime", -1)])  # 注册时间查询
            self._create_index_safely(self.users_collection, [("updateTime", -1)])  # 更新时间查询

            # 积分记录表索引
            self._create_index_safely(self.records_collection, "openId")
            self._create_index_safely(self.records_collection, "userName")
            self._create_index_safely(self.records_collection, [("submitTime", -1)])  # 按时间倒序查询
            self._create_index_safely(self.records_collection, [("openId", 1), ("submitTime", -1)])  # 复合索引
            self._create_index_safely(self.records_collection, "oper")  # 操作类型索引

            self.log.info("数据库索引创建完成")
        except Exception as e:
            self.log.error(f"创建数据库索引过程中发生错误: {str(e)}")

    def _create_index_safely(self, collection, keys, **kwargs):
        """安全地创建索引，如果已存在则跳过"""
        try:
            collection.create_index(keys, **kwargs)
            index_name = keys if isinstance(keys, str) else str(keys)
            self.log.debug(f"索引创建成功: {index_name}")
        except DuplicateKeyError as e:
            # 如果是唯一索引冲突，需要清理数据后重试
            if kwargs.get('unique'):
                self.log.warning(f"唯一索引创建失败，存在重复数据: {str(e)}")
                # 对于 openId 唯一索引，尝试清理重复数据后重试一次
                if keys == "openId":
                    self.log.info("尝试清理重复的 openId 数据...")
                    self._cleanup_duplicate_users()
                    try:
                        collection.create_index(keys, **kwargs)
                        self.log.info("清理重复数据后，openId 唯一索引创建成功")
                    except Exception as retry_error:
                        self.log.error(f"重试创建 openId 唯一索引失败: {str(retry_error)}")
            else:
                raise e
        except OperationFailure as e:
            # 如果索引已存在，跳过
            if "already exists" in str(e) or "IndexOptionsConflict" in str(e):
                self.log.debug(f"索引已存在，跳过创建: {keys}")
            else:
                self.log.warning(f"索引创建失败: {keys} - {str(e)}")
        except Exception as e:
            self.log.error(f"创建索引时发生未知错误: {keys} - {str(e)}")

    def _cleanup_duplicate_users(self):
        """清理重复的用户数据，保留最新的记录"""
        try:
            # 查找重复的 openId
            pipeline = [
                {"$group": {
                    "_id": "$openId",
                    "count": {"$sum": 1},
                    "docs": {"$push": "$$ROOT"}
                }},
                {"$match": {"count": {"$gt": 1}}}
            ]

            duplicates = list(self.users_collection.aggregate(pipeline))

            if not duplicates:
                self.log.debug("未发现重复的用户数据")
                return

            cleaned_count = 0
            for duplicate_group in duplicates:
                openId = duplicate_group["_id"]
                docs = duplicate_group["docs"]

                # 按更新时间排序，保留最新的记录
                docs.sort(key=lambda x: x.get("updateTime", ""), reverse=True)
                keep_doc = docs[0]  # 保留最新的
                remove_docs = docs[1:]  # 删除其他的

                # 删除重复记录
                for doc in remove_docs:
                    try:
                        self.users_collection.delete_one({"_id": doc["_id"]})
                        cleaned_count += 1
                        self.log.info(f"删除重复用户记录: {openId} (保留最新记录)")
                    except Exception as delete_error:
                        self.log.error(f"删除重复记录失败: {openId} - {str(delete_error)}")

            if cleaned_count > 0:
                self.log.info(f"清理重复用户数据完成，共删除 {cleaned_count} 条重复记录")

        except Exception as e:
            self.log.error(f"清理重复用户数据失败: {str(e)}")

    @contextmanager
    def get_distributed_lock(self, lock_key: str, timeout: int = 30):
        """
        获取分布式锁
        :param lock_key: 锁键
        :param timeout: 超时时间
        """
        try:
            with self.cache_manager.distributed_lock(lock_key, timeout):
                yield
        except Exception as e:
            self.log.error(f"分布式锁操作失败: {e}")
            raise

    def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        try:
            # 检查数据库连接
            db_healthy = True
            try:
                self.db.command('ping')
            except Exception as e:
                db_healthy = False
                self.log.error(f"数据库连接检查失败: {e}")

            # 检查缓存连接
            cache_healthy = True
            try:
                if self.cache_manager.redis_client:
                    self.cache_manager.redis_client.ping()
            except Exception as e:
                cache_healthy = False
                self.log.warning(f"缓存连接检查失败: {e}")

            # 获取基础统计
            basic_stats = self.metrics.get_basic_stats()

            return {
                "status": "healthy" if db_healthy else "unhealthy",
                "database_healthy": db_healthy,
                "cache_healthy": cache_healthy,
                "uptime_seconds": basic_stats.get("uptime_seconds", 0),
                "total_operations": basic_stats.get("total_operations", 0),
                "success_rate": basic_stats.get("success_rate", 0),
                "timestamp": time.time()
            }

        except Exception as e:
            self.log.error(f"健康检查失败: {str(e)}")
            return {
                "status": "error",
                "error": str(e),
                "timestamp": time.time()
            }

    # 通用数据库操作方法
    def find_one_common(self, collection_name: str, where: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        通用单条查询
        :param collection_name: 集合名称
        :param where: 查询条件
        :return: 查询结果
        """
        try:
            collection = self.db[collection_name]
            return collection.find_one(where)
        except Exception as e:
            self.log.error(f"单条查询失败: {str(e)}")
            return None

    def find_common(self, collection_name: str, where: Dict[str, Any], limit: int = 1000) -> List[Dict[str, Any]]:
        """
        通用多条查询
        :param collection_name: 集合名称
        :param where: 查询条件
        :param limit: 查询限制数量
        :return: 查询结果
        """
        try:
            limit = max(1, min(1000, int(limit)))
            collection = self.db[collection_name]
            return list(collection.find(where).limit(limit))
        except Exception as e:
            self.log.error(f"多条查询失败: {str(e)}")
            return []

    def aggregate_common(self, collection_name: str, pipeline: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        通用聚合查询
        :param collection_name: 集合名称
        :param pipeline: 聚合管道
        :return: 聚合结果
        """
        try:
            collection = self.db[collection_name]
            return list(collection.aggregate(pipeline))
        except Exception as e:
            self.log.error(f"聚合查询失败: {str(e)}")
            return []

    def update_common(self, collection_name: str, where: Dict[str, Any], info: Dict[str, Any]) -> bool:
        """
        通用更新操作
        :param collection_name: 集合名称
        :param where: 更新条件
        :param info: 更新信息
        :return: 更新是否成功
        """
        try:
            collection = self.db[collection_name]
            result = collection.update_one(where, info)
            return result.matched_count > 0
        except Exception as e:
            self.log.error(f"更新操作失败: {str(e)}")
            return False

    def cleanup_cache(self):
        """清理缓存"""
        try:
            self.cache_manager.cleanup_expired_cache()
            self.log.info("缓存清理完成")
        except Exception as e:
            self.log.error(f"缓存清理失败: {str(e)}")

    def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        return self.cache_manager.get_cache_stats()

    def get_metrics(self) -> Dict[str, Any]:
        """获取数据库层指标"""
        return self.metrics.get_all_metrics()