# Websocket客户端 - 用于与UPBGE游戏通信
import asyncio
import websockets
import json
import threading
from func.log.default_log import DefaultLog
from func.tools.singleton_mode import singleton

@singleton
class WebSocketClient:
    def __init__(self):
        self.log = DefaultLog().getLogger()
        self.websocket = None
        self.is_connected = False
        self.server_url = "ws://localhost:8765"  # 游戏websocket服务器地址
        self.loop = None
        self.thread = None
        
    def set_server_url(self, url):
        """设置服务器URL"""
        self.server_url = url
        
    async def connect(self):
        """连接到websocket服务器"""
        try:
            self.websocket = await websockets.connect(self.server_url)
            self.is_connected = True
            self.log.info(f"WebSocket连接成功: {self.server_url}")
            
            # 保持连接监听
            await self.listen_for_messages()
            
        except Exception as e:
            self.log.error(f"WebSocket连接失败: {e}")
            self.is_connected = False
            
    async def listen_for_messages(self):
        """监听来自服务器的消息"""
        try:
            async for message in self.websocket:
                data = json.loads(message)
                self.log.info(f"收到游戏消息: {data}")
                # 这里可以处理来自游戏的回调消息
                
        except websockets.exceptions.ConnectionClosed:
            self.log.info("WebSocket连接已关闭")
            self.is_connected = False
        except Exception as e:
            self.log.error(f"监听消息时出错: {e}")
            self.is_connected = False
            
    async def send_dance_command(self, dance_name, user_name=""):
        """发送跳舞命令到游戏"""
        if not self.is_connected or not self.websocket:
            self.log.warning("WebSocket未连接，无法发送跳舞命令")
            return False
            
        try:
            message = {
                "type": "dance",
                "action": "start_dance",
                "dance_name": dance_name,
                "user_name": user_name,
                "timestamp": asyncio.get_event_loop().time()
            }
            
            await self.websocket.send(json.dumps(message, ensure_ascii=False))
            self.log.info(f"发送跳舞命令: {message}")
            return True
            
        except Exception as e:
            self.log.error(f"发送跳舞命令失败: {e}")
            return False
            
    async def disconnect(self):
        """断开websocket连接"""
        if self.websocket:
            await self.websocket.close()
            self.is_connected = False
            self.log.info("WebSocket连接已断开")
            
    def start_client(self):
        """在新线程中启动websocket客户端"""
        def run_client():
            self.loop = asyncio.new_event_loop()
            asyncio.set_event_loop(self.loop)
            try:
                self.loop.run_until_complete(self.connect())
            except Exception as e:
                self.log.error(f"WebSocket客户端运行出错: {e}")
            finally:
                if self.loop and not self.loop.is_closed():
                    self.loop.close()
                    
        if not self.thread or not self.thread.is_alive():
            self.thread = threading.Thread(target=run_client, daemon=True)
            self.thread.start()
            self.log.info("WebSocket客户端线程已启动")
            
    def send_dance_sync(self, dance_name, user_name=""):
        """同步发送跳舞命令（从主线程调用）"""
        if not self.loop or self.loop.is_closed():
            self.log.warning("WebSocket事件循环未运行")
            return False
            
        try:
            # 使用call_soon_threadsafe在websocket线程中执行
            future = asyncio.run_coroutine_threadsafe(
                self.send_dance_command(dance_name, user_name), 
                self.loop
            )
            return future.result(timeout=5)  # 5秒超时
        except Exception as e:
            self.log.error(f"同步发送跳舞命令失败: {e}")
            return False
            
    def stop_client(self):
        """停止websocket客户端"""
        if self.loop and not self.loop.is_closed():
            asyncio.run_coroutine_threadsafe(self.disconnect(), self.loop) 