from flask import Flask, request, jsonify
from flask_cors import CORS
from func.gobal.data import CommonData
import asyncio.coroutines

import asyncio
import logging

from .sched import bind_sched
from controller import create_controller

# 设置日志
logger = logging.getLogger(__name__)

app = Flask(__name__)
CORS(app, resources={r"/*": {"origins": "*"}})

commonData = CommonData()

def api_run(host="0.0.0.0", port=5000):
    """
    运行API服务器
    
    Args:
        host: 主机地址，默认为0.0.0.0
        port: 端口号，默认为None
    """
    # if port is None:
    #     port = 5000  # 默认端口
    
    try:
        # 初始化控制器
        create_controller(app)
 
        # 绑定调度器 需要传入app参数
        bind_sched(app)

        print(f"--------API server starting on {host}:{port}---------")
        
        app.run(host=host, port=port)

    except Exception as e:
        print(e)
        logger.error(f"Failed to start API server: {str(e)}")
        raise

if __name__ == "__main__":
    api_run()