from datetime import datetime
from typing import Dict, Any, Tuple
from enum import Enum
from func.tools.singleton_mode import singleton
from func.database.mongodb import Mongodb
from func.gobal.data import MongodbData

class MessageType(Enum):
    USER = "user"
    LLM = "llm"
@singleton
class ChatDB:
    def __init__(self, connection_string: str = "mongodb://localhost:27017/"):
        """
        初始化MongoDB连接
        :param connection_string: MongoDB连接字符串
        """
        # self.client = MongoClient(connection_string)
        # self.db = self.client['chat_history']

        self.db = Mongodb().get_db()

        self.collection = self.db['yinmei']

    def insert_request0(self, message: str, metadata: Dict[str, Any] = None):
        """
        保存用户请求到数据库
        :param message: 用户的请求
        :param metadata: 额外的元数据（可选）
        """
        doc = {
            'timestamp': datetime.utcnow(),
            'content': message,
            'message_type': MessageType.USER.value,
            'metadata': metadata
        }
        self.collection.insert_one(doc)

    # 保存格式应该如下，message格式本身就是：
    # {
    # 	"_id" : ObjectId("675d178bbaf93cbb02c24c81"),
    # 	"traceid" : "272afde4-c9e6-4cde-849a-8168d587249e",
    # 	"channel" : "api",
    # 	"prompt" : "发布",
    # 	"uid" : "uid0",
    # 	"username" : "name0",
    # 	"uface" : "",
    # 	"intent" : "聊天",
    # 	"submitTime" : "2024-12-14 13:28:43",
    # 	"content" : "" #回复内容
    # }
    def insert_request(self, message: str):
        doc = {
            'traceid': message['traceid'],
            'channel': message['channel'],
            'prompt': message['query'],
            'uid': message['uid'],
            'username': message['username'],
            'uface': message['uface'],
            'submitTime': message['submitTime'],
        }
        self.collection.insert_one(doc)

    def insert_chat(self, user_message: str, llm_response: str, metadata: Dict[str, Any] = None) -> Tuple[str, str]:
        """
        保存用户消息和LLM响应到数据库，作为独立的记录
        :param user_message: 用户的消息
        :param llm_response: LLM的响应
        :param metadata: 额外的元数据（可选）
        :return: 包含用户消息ID和LLM响应ID的元组
        """
        # 生成会话ID来关联用户消息和LLM响应
        conversation_id = str(datetime.utcnow().timestamp())
        base_metadata = (metadata or {}).copy()
        base_metadata['conversation_id'] = conversation_id

        # 保存用户消息
        user_message_doc = {
            'timestamp': datetime.utcnow(),
            'content': user_message,
            'message_type': MessageType.USER.value,
            'metadata': base_metadata
        }
        user_result = self.collection.insert_one(user_message_doc)

        # 保存LLM响应
        llm_response_doc = {
            'timestamp': datetime.utcnow(),
            'content': llm_response,
            'message_type': MessageType.LLM.value,
            'metadata': base_metadata
        }
        llm_result = self.collection.insert_one(llm_response_doc)

        return str(user_result.inserted_id), str(llm_result.inserted_id)

    def all_chat(self, limit: int = 10):
        """
        获取最近的对话历史
        :param limit: 返回的消息数量
        :return: 消息历史列表
        """
        return list(self.collection.find().sort('timestamp', -1).limit(limit))

    def get_conversation_by_id(self, conversation_id: str):
        """
        根据会话ID获取完整对话
        :param conversation_id: 会话ID
        :return: 该会话的所有消息
        """
        return list(self.collection.find(
            {"metadata.conversation_id": conversation_id}
        ).sort('timestamp', 1))

    def find_chat_list_page(self, username: str, page_number: int, page_size: int = 10):
        """
        分页查询对话历史
        :param page_number: 页码
        :param page_size: 每页大小
        :return: 指定页码的对话历史
        """

        skip = (page_number - 1) * page_size
        return list(self.collection.find({"metadata.username": username}).skip(skip).limit(page_size).sort('timestamp', -1))

    def close(self):
        """
        关闭数据库连接
        """
        self.client.close()

# 使用示例
if __name__ == "__main__":
    # 创建数据库连接
    db = ChatDB()
    
    # 保存对话
    user_msg_id, llm_msg_id = db.insert_chat(
        user_message="你好！",
        llm_response="你好！我能帮你什么吗？",
        metadata={"user_id": "user_1"}
    )
    
    # 获取对话历史
    history = db.all_chat()
    
    # 关闭连接
    db.close() 