from bson.json_util import dumps
from flask import jsonify
from func.database.mongodb import Mongodb
from func.tools.singleton_mode import singleton
# from func.tools.decorator_mode import class_switch
from func.log.default_log import DefaultLog
import re
import time
import json

log = DefaultLog().getLogger()

@singleton
class ChatDB:
    def __init__(self):
        self.Mongodb = Mongodb()
        self.db = self.Mongodb.get_db()
        self.chatlistDB = self.db['chatlist']
        self.chat_record = self.db['chat_record']
        self.chat_request = self.db['chat_request']
        
    def insert_chat(self, data):
        """
        插入聊天记录
        :param data: 聊天数据
        :return:
        """
        try:
            self.chat_record.insert_one(data)
            return True
        except Exception as e:
            print(e)
            log.error(f"插入聊天记录失败: {str(e)}")
            return False

    def all_chat(self, username):
        """
        获取用户所有聊天记录
        :param username: 用户名
        :return:
        """
        try:
            cursor = self.chat_record.find({"username": username}).sort("submitTime", -1)
            return list(cursor)
        except Exception as e:
            print(e)
            log.error(f"获取聊天记录失败: {str(e)}")
            return []

    def insert_request(self, data):
        """
        插入请求记录
        :param data: 请求数据
        :return:
        """
        try:
            print("---chatdb---插入---insert_request---")
            # 确保数据是字典类型
            if not isinstance(data, dict):
                try:
                    # 尝试将数据转换为字典
                    if hasattr(data, '__dict__'):
                        data = data.__dict__
                    else:
                        data = dict(data)
                except Exception as e:
                    log.error(f"数据转换为字典失败: {str(e)}")
                    return False
                
            self.chat_request.insert_one(data)
            return True
        except Exception as e:
            print(e)
            log.error(f"插入请求记录失败: {str(e)}")
            return False
    # userinfo_controller调用，无openid
    def find_chat_list_page(self, username, page_number=1, page_size=10):
        """
        聊天列表分页查询
        :param username: 用户名
        :param page_number: 页码
        :param page_size: 每页大小
        :return:
        """
        try:
            skip_count = (page_number - 1) * page_size
            cursor = self.chat_request.find(
                {"user_name": {"$regex": username}}#, "$options": "i"
            ).sort("submitTime", -1).skip(skip_count).limit(page_size)

            total_documents = self.chat_request.count_documents(
                {"user_name": {"$regex": username, "$options": "i"}}
            )
            total_pages = (total_documents + page_size - 1) // page_size
            
            return {
                "status": 200,
                "data": list(cursor),
                "total_documents": total_documents,
                "total_pages": total_pages,
                "current_page": page_number,
                "page_size": page_size
            }
        except Exception as e:
            print(e)
            log.error(f"查询聊天列表失败: {str(e)}")
            return None

    def parse_reply_message(self, processed_plain_text):
        """
        解析回复消息格式，提取发送者名字、ID和回复内容

        Args:
            processed_plain_text: 处理后的消息文本，格式如：
                "[回复<AI呦呦-3D舞宠:dc89b178fba24e20a036878e8cae3c68> 的消息：复合点] 复合啥"

        Returns:
            Dict包含解析结果，格式如：
            {
                "sender_name": "AI呦呦-3D舞宠",
                "sender_id": "dc89b178fba24e20a036878e8cae3c68",
                "question": "复合点",
                "reply_content": "复合啥"
            }
            如果解析失败返回None
        """
        if not processed_plain_text:
            return None

        # 正则表达式匹配回复格式：[回复<发送者名字:发送者ID> 的消息：原消息内容] 回复内容
        pattern = r'\[回复<([^:<>]+):([^:<>]+)>\s*的消息：([^\]]*)\]\s*(.*)'
        match = re.match(pattern, processed_plain_text.strip())

        if not match:
            log.debug(f"回复消息格式不匹配: {processed_plain_text}")
            return None

        sender_name = match.group(1).strip()
        sender_id = match.group(2).strip()
        question = match.group(3).strip()
        reply_content = match.group(4).strip()

        result = {
            "sender_name": sender_name,
            "sender_id": sender_id,
            "question": question,
            "reply_content": reply_content
        }

        log.info(f"解析回复消息成功: {result}")
        return result

    def insert_reply(self, reply_data):
        """
        插入回复记录，并更新对应的原始问题

        Args:
            reply_data: 回复数据，包含解析后的回复信息和消息元数据

        Returns:
            bool: 插入是否成功
        """
        try:
            print("---chatdb---插入回复---insert_reply---")

            # 查找对应的原始问题消息（最近的一条匹配消息）
            original_query = self.chat_request.find_one(
                {
                    "uid": reply_data["sender_id"],
                    "query": {"$regex": re.escape(reply_data["question"]), "$options": "i"}
                },
                sort=[("submitTime", -1)]  # 按时间倒序，获取最新的
            )

            if not original_query:
                log.warning(f"未找到对应的原始问题消息: {reply_data['question']}")
                # 即使没找到原始问题，也可以插入回复记录

            # 格式化时间为前端期望的格式 "YYYY-MM-DD HH:MM:SS"
            current_time = time.time()
            formatted_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(current_time))

            # 构建符合前端期望的回复记录格式，插入到chat_request集合
            reply_request = {
                "traceid": reply_data.get("traceid", ""),
                "query": f"[回复] {reply_data['reply_content']}",  # 标记为回复
                "uid": reply_data["sender_id"],
                "user_name": reply_data["sender_name"],
                "uface": "",
                "channel": "webili",
                "submitTime": formatted_time,
                "content": reply_data["reply_content"],  # 前端显示的回复内容
                "intent": "回复",  # 标记意图为回复
                "original_question": reply_data["question"],  # 原始问题
                "original_traceid": original_query.get("traceid") if original_query else None,
                "is_reply": True,  # 标记这是一条回复记录
                "timestamp": current_time  # 保留数字时间戳用于排序
            }

            # 插入回复记录到chat_request集合（前端查询的集合）
            insert_result = self.chat_request.insert_one(reply_request)
            reply_request['_id'] = str(insert_result.inserted_id)  # 转换ObjectId为字符串

            # 同时插入到chat_record集合（保持兼容性）
            reply_json = {
                "voiceType": "chat",
                "traceid": reply_data.get("traceid", ""),
                "chatStatus": "waiting",
                "question": reply_data["question"],
                "text": reply_data["reply_content"],
                "language": "AutoChange",
                "sender_name": reply_data["sender_name"],
                "sender_id": reply_data["sender_id"],
                "original_traceid": original_query.get("traceid") if original_query else None,
                "timestamp": current_time
            }
            self.chat_record.insert_one(reply_json)

            # 如果找到了原始问题，更新它添加回复信息
            if original_query:
                self.chat_request.update_one(
                    {"_id": original_query["_id"]},
                    {"$set": {"content": reply_data["reply_content"], "replied": True}}
                )
                log.info(f"已更新原始问题，添加回复信息: {original_query.get('traceid', 'unknown')}")

            log.info(f"回复记录插入成功: traceid={reply_data.get('traceid')}, 用户={reply_data['sender_name']}, 回复内容={reply_data['reply_content']}")
            return True

        except Exception as e:
            print(e)
            log.error(f"插入回复记录失败: {str(e)}")
            return False