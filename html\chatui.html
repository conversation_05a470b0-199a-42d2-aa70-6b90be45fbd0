<!DOCTYPE html>
<html lang="zh">
	<head>
		<meta charset="utf-8">
		<meta http-equiv="X-UA-Compatible" content="IE=edge">
		<meta name="viewport" content="width=device-width,initial-scale=1">
		<meta name="referrer" content="no-referrer">
		<link rel="icon" href="/favicon.ico">
		<title>彩色回复框</title>
		<style>
            /* Transparent background */
            body {
                background-color: transparent !important;
            }
			#chat {
				width: 800px; height:400px; overflow: hidden; 
            }
			#content {
                width: auto; height:auto; 
            }
			#content .area{
			    background-color: #b91919; color: white; line-height: 43px;
				font-size: 32px; font-weight: bold; word-wrap: break-word;overflow:hidden;
				letter-spacing:5px; font-family:"微软雅黑";
				margin-bottom: 10px; padding: 10px; width: auto; height: auto; 
				-webkit-border-radius: 15px; text-shadow: 1px 1px 3px #000000;
				border: solid 1px #4e4e4e; box-shadow: 0px 1px 1px 0px #4e4e4e;
			}
        </style>
		<script type="text/javascript" src="jquery-3.7.1.min.js"></script>
	</head>
	<body>
		<div id="chat">
			  <div id="content"></div>
		</div>
		<div id="tip"></div>
		<script type="text/javascript">
			function typewriter(text) {
				var i = 0; // 当前要显示的字符索引
				var colors = ["#b91919", "#2c2cff", "#02c01b","#ffa724","#ff2476","#00BFFF","#9932CC","#C71585","#DC143C","#FF7F50"];
				var randomNumber = Math.floor(Math.random() * colors.length)
				var timestamp = new Date().getTime();
				$("#content").last().append("<div class=\"area "+timestamp+"\" style=\"background-color:"+colors[randomNumber]+"\"></div>")
				function showChar() {
					if (i < text.length) {
						$("#content div.area."+timestamp).append(text[i]);
						//滚动到底部
						$("#chat").scrollTop($("#content").height());
						i++; // 更新索引值
						setTimeout(showChar, 80); // 每次间隔xx调用自身，模拟打字效果
					} else {
						console.log('完成'); // 所有字符都已经输出完毕时触发此行代码
						var count = $('#content div').length;
						if(count>50)
						  $('#content div').slice(0, 20).remove();
					}
				}
				
				showChar(); // 开始输出第一个字符
			}
			
			// JavaScript部分
			function refreshPage(){
				$.ajax({
                    url: "http://127.0.0.1:8888/chatreply",
                    type: "get",
                    dataType: "jsonp",
                    //需要和服务端回掉方法中的参数名相对应
                    //注释掉这句话默认传的名称叫callback
                    jsonp: "CallBack",
                    cache: false,
                    data: {},
                    success: function (data) {
						if(data["status"]=="成功")
						{
						    content = data["content"]
							content = content.replaceAll("<br/>","\r\n");
						    typewriter(content)
						}
                    }
                });
			}
            
			// 每隔1000毫秒（1秒）
			setInterval(function(){
				refreshPage();
			}, 1000);
		</script>
	</body>
</html>