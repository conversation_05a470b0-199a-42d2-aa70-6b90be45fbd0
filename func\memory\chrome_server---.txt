J:\ai\�������\ai-yinmei\func\memory\chroma_server.c    chroma_server.py        __init__        chroma_server.ChromaServer.__init__     memory_add_batch        chroma_server.ChromaServer.memory_add_batch     memory_search   chroma_server.ChromaServer.memory_search        memory_reset    chroma_server.ChromaServer.memory_reset chroma_server   Interpreter change detected - this module can only be loaded into one interpreter per process.  name    __loader__  loader      __file__    origin      __package__ parent      __path__        submodule_search_locations      Module 'chroma_server' has already been imported. Re-initialisation is not supported.   builtins        cython_runtime  __builtins__    init chroma_server      %s() got multiple values for keyword argument '%U'      %.200s() keywords must be strings       %s() got an unexpected keyword argument '%U'    exactly s       %.200s() takes %.8s %zd positional argument%.1s (%zd given)     name '%U' is not defined         while calling a Python object  NULL result without error in PyObject_Call      join() result is too long for a Python string   cannot import name %S   _cython_3_0_11  Shared Cython type %.200s is not a type object  Shared Cython type %.200s has the wrong size, try recompiling   keywords must be strings        __name__ must be set to a string object __qualname__ must be set to a string object     function's dictionary may not be deleted        setting function's dictionary to a non-dict     __defaults__ must be set to a tuple object      changes to cyfunction.__defaults__ will not currently affect the values used in function calls  __kwdefaults__ must be set to a dict object     changes to cyfunction.__kwdefaults__ will not currently affect the values used in function calls        __annotations__ must be set to a dict object    func_doc        __doc__ func_name       __name__        __qualname__    func_dict       __dict__        func_globals    __globals__     func_closure    __closure__     func_code       __code__        func_defaults   __defaults__    __kwdefaults__  __annotations__ _is_coroutine   __module__      __reduce__      Bad call flags for CyFunction   <cyfunction %U at %p>   %.200s() takes no arguments (%zd given) %.200s() takes exactly one argument (%zd given) %.200s() takes no keyword arguments     unbound method %.200S() needs an argument       %.200s() needs an argument      _cython_3_0_11.cython_function_or_method        %s (%s:%d)      does not match          compile time Python version %d.%d of module '%.100s' %s runtime version %d.%d   requests    *   exception   ?   cline_in_traceback      func.tools.singleton_mode       @� �   __prepare__ 【 memory_reset    func.gobal.data __main__        ChromaServer.memory_reset       memoryData      __qualname__    __module__  self        memory_reset异常      __dict__        � �   _initializing   /memory_add_batch       MemoryData      J:\ai\吟美打包\ai-yinmei\func\memory\chroma_server.py       _is_coroutine   super   __spec__    post        memory_search   __import__  log __doc__ ChromaServer.memory_search      __set_name__    response    .   func.log.default_log    
        清洗全部记忆
        :param data:
        :return:
              __name__        DefaultLog      __init_subclass__       】memory_add_batch异常       timeout data    chroma_server   __test__        】memory_search异常  json    e       
        查询记忆
        :param data:
        :return:
            __metaclass__   ChromaServer    getLogger       server_url              
        批量插入记忆数据
        :param data:
        :return:
                __init__        asyncio.coroutines      ChromaServer.memory_add_batch   ChromaServer.__init__   singleton       /memory_reset   memory_add_batch        /memory_search