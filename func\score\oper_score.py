"""
积分操作服务 - 优化版本
处理所有积分相关的业务逻辑
"""

import asyncio
import json
import threading
import time
from typing import Optional, Dict, Any, List
from enum import Enum

from func.tools.singleton_mode import singleton
from func.score.score_db import ScoreDB
from func.score.score_config import ScoreSystemConfig, ScoreOperationType, ScoreRankConfig
from func.score.score_limiter import ScoreRateLimiter, ScoreSecurityManager, rate_limit
from func.score.score_metrics import ScoreMetrics
from func.log.default_log import DefaultLog

# 可选导入，避免依赖问题
try:
    from func.tools.common_websocket import CommonWebsocket
    WEBSOCKET_AVAILABLE = True
except ImportError:
    WEBSOCKET_AVAILABLE = False
    print("Warning: CommonWebsocket not available, broadcast功能将被禁用")

try:
    from func.tts.tts_core import TTsCore
    TTS_AVAILABLE = True
except ImportError:
    TTS_AVAILABLE = False
    print("Warning: TTsCore not available, 语音播报功能将被禁用")

try:
    from func.gobal.data import CommonData
    COMMON_DATA_AVAILABLE = True
except ImportError:
    COMMON_DATA_AVAILABLE = False
    print("Warning: CommonData not available, 部分功能可能受限")

@singleton
class OperScore:
    """积分操作服务类"""
    
    def __init__(self):
        """初始化积分操作服务"""
        print("OperScore 初始化开始...")

        # 核心依赖（必需）
        self.scoreDB = ScoreDB()
        self.log = DefaultLog().getLogger()

        # 安全和监控组件
        self.security_manager = ScoreSecurityManager(self.scoreDB.cache_manager)
        self.rate_limiter = self.security_manager.rate_limiter
        # self.alert_manager = ScoreAlertManager(self.scoreDB.metrics)  # 暂时禁用

        # 可选依赖（优雅降级）
        self.commonWebsocket = None
        self.ttsCore = None
        self.commonData = None

        if WEBSOCKET_AVAILABLE:
            try:
                self.commonWebsocket = CommonWebsocket()
                self.log.info("WebSocket组件初始化成功")
            except Exception as e:
                self.log.warning(f"WebSocket组件初始化失败: {e}")

        if TTS_AVAILABLE:
            try:
                self.ttsCore = TTsCore()
                self.log.info("TTS组件初始化成功")
            except Exception as e:
                self.log.warning(f"TTS组件初始化失败: {e}")

        if COMMON_DATA_AVAILABLE:
            try:
                self.commonData = CommonData()
                self.log.info("CommonData组件初始化成功")
            except Exception as e:
                self.log.warning(f"CommonData组件初始化失败: {e}")

        # 用户每日积分统计缓存（现在使用Redis缓存管理器）
        self._daily_score_cache = {}  # 保留作为降级备用

        # 初始化完成
        self.log.info("OperScore 初始化完成")

    def recharge_score(self, openId: str, score: int, oper: str = "充值") -> Optional[int]:
        """
        充值积分
        :param openId: 用户开放平台id
        :param score: 积分数量（可以是负数表示扣减）
        :param oper: 操作类型
        :return: 操作后的总积分，失败返回None
        """
        if not self._validate_score_operation(openId, score):
            return None
            
        try:
            # 查找用户信息
            user_info = self.scoreDB.get_score(openId)
            if not user_info:
                self.log.error(f"用户不存在，无法操作积分: {openId}")
                return None
            
            # 检查扣减积分时是否有足够的积分
            current_score = user_info.get('score', 0)
            if score < 0 and current_score + score < 0:
                self.log.warning(f"用户积分不足，无法扣减: {openId}, 当前:{current_score}, 扣减:{abs(score)}")
                return None
            
            # 执行积分操作
            success = self.scoreDB.oper_score(openId, user_info['userName'], score, oper)
            
            if success:
                new_score = current_score + score
                self.log.info(f"用户{user_info['userName']}积分操作成功，当前积分：{new_score}")
                
                # 广播积分变更消息
                self._broadcast_score_change(openId, user_info['userName'], score, oper, user_info.get('userface', ''))
                
                return new_score
            else:
                self.log.error(f"积分操作失败: {openId}")
                return None
                
        except Exception as e:
            self.log.error(f"充值积分异常：{str(e)}")
            return None

    def oper_score(self, openId: str, user_name: str, score: int, uface: str, oper: str) -> Optional[int]:
        """
        操作积分（主要接口）- 增强版本，包含安全检查和限流
        :param openId: 用户开放平台id
        :param user_name: 用户名称
        :param score: 积分数量
        :param uface: 用户头像
        :param oper: 操作类型
        :return: 操作后的积分，失败返回None
        """
        start_time = time.time()

        # 基础参数验证
        if not self._validate_operation_params(openId, user_name, score, oper):
            return None

        try:
            # 安全检查：用户黑名单
            if not self.security_manager.is_user_allowed(openId):
                self.log.warning(f"用户在黑名单中，拒绝操作: {openId}")
                return None

            # 限流检查
            rate_check = self.rate_limiter.check_rate_limit(openId, oper)
            if not rate_check["allowed"]:
                self.log.warning(f"用户操作频率超限: {openId}, {rate_check['reason']}")
                return None

            # 可疑活动检查
            if self.security_manager.check_suspicious_activity(openId, oper, score):
                self.log.warning(f"检测到可疑活动: {openId}, {oper}, {score}")
                # 可疑活动不直接拒绝，但会记录

            # 先刷新/创建用户信息
            user_info = self.scoreDB.user_refresh(openId, user_name, uface)
            if not user_info:
                self.log.error(f"用户信息刷新失败: {openId}")
                return None

            # 根据操作类型处理积分
            result = None
            if oper == ScoreOperationType.recharge.value:
                result = self.recharge_score(openId, score, oper)
            elif oper == ScoreOperationType.deduct.value:
                result = self.recharge_score(openId, -score, oper)
            elif oper == ScoreOperationType.chat.value:
                # 聊天积分有每日上限控制
                result = self._handle_chat_score(openId, user_name, score, oper)
            else:
                # 其他操作（唱歌、跳舞、绘画等）在后台线程处理以提高响应速度
                self._handle_async_score_operation(openId, user_name, score, oper)
                result = user_info.get('score', 0)  # 返回当前积分

            # 成功操作后增加限流计数
            if result is not None:
                self.rate_limiter.increment_counter(openId, oper)

            # 记录操作指标
            self.scoreDB.metrics.record_operation(
                oper,
                result is not None,
                time.time() - start_time,
                openId,
                score if result is not None else 0
            )

            return result

        except Exception as e:
            self.log.error(f"操作积分异常：{str(e)}")
            # 记录失败指标
            self.scoreDB.metrics.record_operation(
                oper,
                False,
                time.time() - start_time,
                openId,
                0,
                "operation_exception"
            )
            return None

    def _handle_chat_score(self, openId: str, user_name: str, score: int, oper: str) -> Optional[int]:
        """处理聊天积分（有每日上限限制）- 使用Redis缓存优化"""
        try:
            # 使用Redis缓存管理器获取每日积分
            daily_score = self.scoreDB.cache_manager.get_daily_score(openId)

            if daily_score >= ScoreSystemConfig.MAX_DAILY_CHAT_SCORE:
                self.log.info(f"用户{user_name}今日聊天积分已达上限: {daily_score}")
                return None

            # 调整积分，确保不超过每日上限
            actual_score = min(score, ScoreSystemConfig.MAX_DAILY_CHAT_SCORE - daily_score)

            if actual_score > 0:
                success = self.scoreDB.oper_score(openId, user_name, actual_score, oper)
                if success:
                    # 更新每日积分缓存
                    self.scoreDB.cache_manager.increment_daily_score(openId, actual_score)

                    # 获取更新后的积分
                    user_info = self.scoreDB.get_score(openId)
                    new_score = user_info.get('score', 0) if user_info else 0

                    # 广播积分变更
                    if user_info:
                        self._broadcast_score_change(openId, user_name, actual_score, oper, user_info.get('userface', ''))

                    return new_score

            return None

        except Exception as e:
            self.log.error(f"处理聊天积分异常: {str(e)}")
            return None

    def _handle_async_score_operation(self, openId: str, user_name: str, score: int, oper: str):
        """异步处理积分操作"""
        def async_operation():
            try:
                # 根据操作类型计算实际积分变化
                actual_score = ScoreSystemConfig.get_operation_score(oper)
                if actual_score == 0:
                    actual_score = score  # 使用传入的积分值
                
                self.scoreDB.oper_score(openId, user_name, actual_score, oper)
                self.log.info(f"异步积分操作完成: {openId}, {oper}, {actual_score}")
            except Exception as e:
                self.log.error(f"异步积分操作失败: {str(e)}")
        
        score_thread = threading.Thread(target=async_operation, daemon=True)
        score_thread.start()

    def _broadcast_score_change(self, openId: str, user_name: str, score: int, oper: str, userface: str):
        """广播积分变更消息"""
        if not self.commonWebsocket:
            return  # WebSocket不可用时静默返回
            
        try:
            broadcast_data = {
                "type": "积分提示",
                "openId": openId,
                "username": user_name,
                "userface": userface,
                "score": score,
                "oper": oper,
                "timestamp": int(time.time())
            }
            
            # 异步广播，避免阻塞
            def broadcast():
                try:
                    self.commonWebsocket.broadcast(json.dumps(broadcast_data))
                except Exception as e:
                    self.log.error(f"广播消息失败: {e}")
            
            threading.Thread(target=broadcast, daemon=True).start()
            
        except Exception as e:
            self.log.error(f"准备广播消息失败: {str(e)}")

    def find_score_rank(self, limit: int = 10) -> List[Dict[str, Any]]:
        """
        积分排行榜查询
        :param limit: 返回数量，默认10
        :return: 排行榜列表
        """
        try:
            # 限制查询数量
            if limit < 1:
                limit = 10
            elif limit > ScoreSystemConfig.MAX_RANK_LIMIT:
                limit = ScoreSystemConfig.MAX_RANK_LIMIT
            
            # 从数据库获取排行榜数据
            rank_data = self.scoreDB.find_score_rank(limit)
            if not rank_data:
                return []
            
            # 处理排行榜数据
            rank_list = []
            for index, user in enumerate(rank_data, 1):
                rank_item = {
                    "rank": index,
                    "openId": user.get("openId"),
                    "userName": user.get("userName"),
                    "score": user.get("score", 0),
                    "userface": user.get("userface", ""),
                    "rank_title": ScoreRankConfig.get_rank_title(index)
                }
                rank_list.append(rank_item)
            
            return rank_list
            
        except Exception as e:
            self.log.error(f"查询积分排行榜失败：{str(e)}")
            return []

    def find_score_user(self, openId: str) -> Optional[Dict[str, Any]]:
        """
        查询用户积分信息
        :param openId: 用户开放平台id
        :return: 用户积分信息
        """
        if not openId:
            return None
            
        try:
            user_info = self.scoreDB.get_score(openId)
            if user_info:
                return {
                    "openId": openId,
                    "userName": user_info.get("userName", ""),
                    "score": user_info.get("score", 0),
                    "userface": user_info.get("userface", ""),
                    "updateTime": user_info.get("updateTime", ""),
                    "createTime": user_info.get("createTime", "")
                }
            return None
            
        except Exception as e:
            self.log.error(f"查询用户积分失败：{str(e)}")
            return None

    def user_refresh(self, openId: str, userName: str, face: str) -> bool:
        """
        用户信息刷新
        :param openId: 用户开放平台id
        :param userName: 用户名
        :param face: 用户头像
        :return: 是否成功
        """
        try:
            result = self.scoreDB.user_refresh(openId, userName, face)
            return result is not None
            
        except Exception as e:
            self.log.error(f"刷新用户信息失败：{str(e)}")
            return False

    def get_system_stats(self) -> Dict[str, Any]:
        """
        获取系统积分统计信息
        :return: 统计信息
        """
        try:
            user_count = self.scoreDB.get_user_count()
            total_score = self.scoreDB.get_total_score()
            
            # 计算附加统计信息
            stats = {
                "total_users": user_count,
                "total_score": total_score,
                "average_score": round(total_score / user_count, 2) if user_count > 0 else 0,
                "active_users_today": self._get_active_users_today(),
                "score_operations_today": self._get_score_operations_today(),
                "top_user": self._get_top_user()
            }
            
            return stats
            
        except Exception as e:
            self.log.error(f"获取系统统计失败：{str(e)}")
            return {
                "total_users": 0,
                "total_score": 0,
                "average_score": 0,
                "active_users_today": 0,
                "score_operations_today": 0,
                "top_user": None
            }

    def _get_active_users_today(self) -> int:
        """获取今日活跃用户数量"""
        try:
            today = time.strftime("%Y-%m-%d")
            return len([key for key in self._daily_score_cache.keys() if today in key])
        except:
            return 0

    def _get_score_operations_today(self) -> int:
        """获取今日积分操作次数"""
        try:
            today = time.strftime("%Y-%m-%d")
            return sum(self._daily_score_cache.get(key, 0) for key in self._daily_score_cache.keys() if today in key)
        except:
            return 0

    def _get_top_user(self) -> Optional[Dict[str, Any]]:
        """获取积分最高的用户"""
        try:
            rank_list = self.find_score_rank(1)
            return rank_list[0] if rank_list else None
        except:
            return None

    def batch_score_operation(self, operations: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        批量积分操作
        :param operations: 操作列表
        :return: 操作结果统计
        """
        try:
            success_count = 0
            failed_count = 0
            
            # 验证操作数量限制
            if len(operations) > ScoreSystemConfig.BATCH_OPERATION_SIZE:
                return {
                    "success": False,
                    "message": f"批量操作数量超过限制({ScoreSystemConfig.BATCH_OPERATION_SIZE})",
                    "processed": 0,
                    "success_count": 0,
                    "failed_count": 0
                }
            
            # 执行批量操作
            if self.scoreDB.batch_update_scores(operations):
                success_count = len(operations)
            else:
                failed_count = len(operations)
            
            return {
                "success": success_count > 0,
                "message": "批量操作完成",
                "processed": len(operations),
                "success_count": success_count,
                "failed_count": failed_count
            }
            
        except Exception as e:
            self.log.error(f"批量积分操作失败：{str(e)}")
            return {
                "success": False,
                "message": f"批量操作异常: {str(e)}",
                "processed": 0,
                "success_count": 0,
                "failed_count": len(operations) if operations else 0
            }

    def msg_deal_score_rank(self) -> str:
        """处理积分排行榜语音播报"""
        if not self.ttsCore:
            return "语音播报功能不可用"
            
        try:
            saystr = "大家好，现在公布直播间积分排行榜数据。"
            
            # 获取排行榜数据并处理
            data = self.find_score_rank(10)
            if data:
                for item in data:
                    rank_title = item.get('rank_title', f"第{item['rank']}名")
                    saystr += f"{rank_title}{item['userName']}的积分是{item['score']}分，"
            else:
                saystr += "暂时没有积分数据。"
            
            saystr += "以上是积分前10位同学。"
            
            # 异步播报，避免阻塞
            def tts_say():
                try:
                    self.ttsCore.assistant_tts_say(saystr)
                except Exception as e:
                    self.log.error(f"TTS播报失败: {e}")
            
            threading.Thread(target=tts_say, daemon=True).start()
            
            return saystr
            
        except Exception as e:
            self.log.error(f"处理积分排行榜播报失败：{str(e)}")
            return "积分排行榜暂时无法获取"

    def check_user_score_sufficient(self, openId: str, required_score: int) -> bool:
        """
        检查用户积分是否足够
        :param openId: 用户ID
        :param required_score: 需要的积分
        :return: 是否足够
        """
        try:
            user_info = self.scoreDB.get_score(openId)
            if not user_info:
                return False
            
            current_score = user_info.get('score', 0)
            return current_score >= required_score
            
        except Exception as e:
            self.log.error(f"检查用户积分失败：{str(e)}")
            return False

    def get_daily_chat_remaining(self, openId: str) -> int:
        """
        获取用户今日剩余聊天积分 - 使用Redis缓存优化
        :param openId: 用户ID
        :return: 剩余聊天积分
        """
        try:
            daily_score = self.scoreDB.cache_manager.get_daily_score(openId)
            remaining = ScoreSystemConfig.MAX_DAILY_CHAT_SCORE - daily_score
            return max(0, remaining)

        except Exception as e:
            self.log.error(f"获取剩余聊天积分失败：{str(e)}")
            return 0

    def reset_daily_cache(self):
        """重置每日缓存（可由定时任务调用）"""
        try:
            today = time.strftime("%Y-%m-%d")
            # 清理过期的缓存数据
            keys_to_remove = [key for key in self._daily_score_cache.keys() if today not in key]
            for key in keys_to_remove:
                del self._daily_score_cache[key]
            
            self.log.info(f"每日缓存重置完成，清理了{len(keys_to_remove)}个过期缓存")
            
        except Exception as e:
            self.log.error(f"重置每日缓存失败：{str(e)}")

    def _validate_score_operation(self, openId: str, score: int) -> bool:
        """验证积分操作参数"""
        if not openId or not isinstance(openId, str):
            self.log.error(f"无效的openId: {openId}")
            return False
        if not isinstance(score, int):
            self.log.error(f"无效的score类型: {type(score)}")
            return False
        if not ScoreSystemConfig.validate_score_amount(score):
            self.log.error(f"积分数量超出允许范围: {score}")
            return False
        return True

    def _validate_operation_params(self, openId: str, user_name: str, score: int, oper: str) -> bool:
        """验证操作参数"""
        if not openId or not isinstance(openId, str):
            self.log.error(f"无效的openId: {openId}")
            return False
        if not user_name or not isinstance(user_name, str):
            self.log.error(f"无效的user_name: {user_name}")
            return False
        if not isinstance(score, int):
            self.log.error(f"无效的score类型: {type(score)}")
            return False
        if not oper or not isinstance(oper, str):
            self.log.error(f"无效的oper: {oper}")
            return False
        if not ScoreSystemConfig.validate_operation_type(oper):
            self.log.error(f"无效的操作类型: {oper}")
            return False
        return True

    def get_score_config(self) -> Dict[str, Any]:
        """获取积分配置"""
        return ScoreSystemConfig.get_config_dict()

    def get_component_status(self) -> Dict[str, bool]:
        """获取组件状态"""
        return {
            "websocket": self.commonWebsocket is not None,
            "tts": self.ttsCore is not None,
            "common_data": self.commonData is not None,
            "score_db": self.scoreDB is not None,
            "cache_manager": self.scoreDB.cache_manager is not None,
            "security_manager": self.security_manager is not None,
            "rate_limiter": self.rate_limiter is not None,
            "alert_manager": self.alert_manager is not None
        }

    def get_security_stats(self) -> Dict[str, Any]:
        """获取安全统计信息"""
        try:
            return self.security_manager.get_security_stats()
        except Exception as e:
            self.log.error(f"获取安全统计失败: {e}")
            return {}

    def get_rate_limit_stats(self, user_id: str, operation: str = "default") -> Dict[str, Any]:
        """获取用户限流统计"""
        try:
            return self.rate_limiter.get_user_stats(user_id, operation)
        except Exception as e:
            self.log.error(f"获取限流统计失败: {e}")
            return {}

    def get_metrics_summary(self) -> Dict[str, Any]:
        """获取指标摘要"""
        try:
            return self.scoreDB.metrics.get_all_metrics()
        except Exception as e:
            self.log.error(f"获取指标摘要失败: {e}")
            return {}

    def check_alerts(self) -> List[Dict[str, Any]]:
        """检查系统告警"""
        try:
            return self.alert_manager.check_alerts()
        except Exception as e:
            self.log.error(f"检查告警失败: {e}")
            return []

    def reset_user_limits(self, user_id: str, operation: str = "default") -> bool:
        """重置用户限制（管理员功能）"""
        try:
            self.rate_limiter.reset_user_limits(user_id, operation)
            self.log.info(f"重置用户限制成功: {user_id}, {operation}")
            return True
        except Exception as e:
            self.log.error(f"重置用户限制失败: {e}")
            return False

    def add_user_to_blacklist(self, user_id: str, reason: str = "") -> bool:
        """添加用户到黑名单（管理员功能）"""
        try:
            self.security_manager.add_to_blacklist(user_id, reason)
            return True
        except Exception as e:
            self.log.error(f"添加黑名单失败: {e}")
            return False

    def remove_user_from_blacklist(self, user_id: str) -> bool:
        """从黑名单移除用户（管理员功能）"""
        try:
            self.security_manager.remove_from_blacklist(user_id)
            return True
        except Exception as e:
            self.log.error(f"移除黑名单失败: {e}")
            return False

    def get_system_health(self) -> Dict[str, Any]:
        """获取系统健康状态"""
        try:
            # 获取数据库健康状态
            db_health = self.scoreDB.health_check()

            # 获取组件状态
            component_status = self.get_component_status()

            # 获取基础指标
            basic_stats = self.scoreDB.metrics.get_basic_stats()

            # 检查告警
            alerts = self.check_alerts()

            # 综合健康评分
            health_score = 100
            if not db_health.get("database_healthy", False):
                health_score -= 50
            if not db_health.get("cache_healthy", False):
                health_score -= 20
            if basic_stats.get("success_rate", 100) < 95:
                health_score -= 15
            if len(alerts) > 0:
                health_score -= min(len(alerts) * 5, 15)

            return {
                "health_score": max(0, health_score),
                "status": "healthy" if health_score >= 80 else "warning" if health_score >= 60 else "critical",
                "database_health": db_health,
                "component_status": component_status,
                "basic_stats": basic_stats,
                "active_alerts": len(alerts),
                "alerts": alerts[:5],  # 只返回前5个告警
                "timestamp": time.time()
            }

        except Exception as e:
            self.log.error(f"获取系统健康状态失败: {e}")
            return {
                "health_score": 0,
                "status": "error",
                "error": str(e),
                "timestamp": time.time()
            }

    def cleanup_system(self) -> Dict[str, Any]:
        """系统清理（定时任务）"""
        try:
            cleanup_results = {
                "actions": [],
                "timestamp": time.time()
            }

            # 清理缓存
            try:
                self.scoreDB.cleanup_cache()
                cleanup_results["actions"].append("缓存清理完成")
            except Exception as e:
                cleanup_results["actions"].append(f"缓存清理失败: {e}")

            # 清理限流计数器
            try:
                self.rate_limiter.cleanup_expired_counters()
                cleanup_results["actions"].append("限流计数器清理完成")
            except Exception as e:
                cleanup_results["actions"].append(f"限流计数器清理失败: {e}")

            # 重置每日用户统计
            try:
                current_hour = int(time.time()) // 3600
                if current_hour % 24 == 0:  # 每天0点重置
                    self.scoreDB.metrics.reset_daily_users()
                    cleanup_results["actions"].append("每日用户统计重置完成")

                if current_hour % 1 == 0:  # 每小时重置
                    self.scoreDB.metrics.reset_hourly_users()
                    cleanup_results["actions"].append("小时用户统计重置完成")
            except Exception as e:
                cleanup_results["actions"].append(f"用户统计重置失败: {e}")

            self.log.info(f"系统清理完成: {len(cleanup_results['actions'])}个操作")
            return cleanup_results

        except Exception as e:
            self.log.error(f"系统清理失败: {e}")
            return {
                "error": str(e),
                "timestamp": time.time()
            }