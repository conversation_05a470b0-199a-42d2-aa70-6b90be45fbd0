J:\ai\�������\ai-yinmei\controller\sched.c     sched.py        bind_sched      sched.bind_sched    sched               Interpreter change detected - this module can only be loaded into one interpreter per process.  name    __loader__  loader      __file__    origin      __package__ parent      __path__        submodule_search_locations      Module 'sched' has already been imported. Re-initialisation is not supported.   builtins        cython_runtime  __builtins__    init sched      %s() got multiple values for keyword argument '%U'      %.200s() keywords must be strings       %s() got an unexpected keyword argument '%U'    exactly %.200s() takes %.8s %zd positional argument%.1s (%zd given)     name '%U' is not defined         while calling a Python object  NULL result without error in PyObject_Call      cannot import name %S   _cython_3_0_11  Shared Cython type %.200s is not a type object  Shared Cython type %.200s has the wrong size, try recompiling   keywords must be strings        __name__ must be set to a string object __qualname__ must be set to a string object     function's dictionary may not be deleted        setting function's dictionary to a non-dict     __defaults__ must be set to a tuple object              changes to cyfunction.__defaults__ will not currently affect the values used in function calls  __kwdefaults__ must be set to a dict object     changes to cyfunction.__kwdefaults__ will not currently affect the values used in function calls        __annotations__ must be set to a dict object    func_doc        __doc__ func_name       __name__        __qualname__    func_dict       __dict__        func_globals    __globals__     func_closure    __closure__     func_code       __code__        func_defaults   __defaults__    __kwdefaults__  __annotations__ _is_coroutine   __module__      __reduce__      Bad call flags for CyFunction   <cyfunction %U at %p>   %.200s() takes no arguments (%zd given) %.200s() takes exactly one argument (%zd given) %.200s() takes no keyword arguments     unbound method %.200S() needs an argument       %.200s() needs an argument      _cython_3_0_11.cython_function_or_method        %s (%s:%d)      does not match          compile time Python version %d.%d of module '%.100s' %s runtime version %d.%d   func.draw.draw_core ?   check_draw      func.search.search_core ttsCore seconds actionOper      LLmCore drawCore        cline_in_traceback      func.sing.sing_core     interval    sched1      ȴ �   check_text_search       singCore        func.tts.tts_core       init_app        CommonWebsocket flask_apscheduler       check_tts_play  ImageCore       check_scene_time        __main__    draw        func.tools.common_websocket     commonWebsocket �� �   danceCore       6,17,18 func.vtuber.action_oper APScheduler app check_sing  dance       _is_coroutine   searchCore      SearchCore  start       llmCore sched   __import__  answer      add_job check_img_search        DanceCore       ObsInit cron    rnd_idle_task   func.obs.obs_init       bind_sched      check_answer    DrawCore        TTsCore __name__        check_dance     func.image.image_core   check_welcome_room  tts scene_time      check_playSongMenuList  func.task.idle  __test__        SingCore        playSongMenuList    sing    hour        check_tts   func        welcome_room    func.dance.dance_core   text_search     trigger obs args        IdleCore    .   asyncio.coroutines  get_ws  id  max_instances   img_search      idleCore        ActionOper      imageCore       func.llm.llm_core       J:\ai\吟美打包\ai-yinmei\controller\sched.py