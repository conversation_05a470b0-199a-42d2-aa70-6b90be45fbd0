#!/usr/bin/env python3
"""
测试缓存管理器修复
验证所有缺失的方法是否已经添加
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../..'))

def test_cache_manager_methods():
    """测试缓存管理器的所有方法"""
    print("=" * 50)
    print("测试缓存管理器方法")
    print("=" * 50)

    try:
        from func.score.score_cache import ScoreCacheManager

        # 创建缓存管理器实例
        cache_manager = ScoreCacheManager()

        # 测试基本方法
        basic_methods = ['get', 'set', 'delete', 'delete_pattern']
        print("\n基本方法检查:")
        for method in basic_methods:
            exists = hasattr(cache_manager, method)
            print(f"  {method}: {'✅' if exists else '❌'}")

        # 测试每日积分相关方法
        daily_methods = ['get_daily_score', 'increment_daily_score', 'add_daily_score', 'reset_daily_score']
        print("\n每日积分方法检查:")
        for method in daily_methods:
            exists = hasattr(cache_manager, method)
            print(f"  {method}: {'✅' if exists else '❌'}")

        # 测试兼容性方法
        compat_methods = ['get_user_score_cache', 'set_user_score_cache']
        print("\n兼容性方法检查:")
        for method in compat_methods:
            exists = hasattr(cache_manager, method)
            print(f"  {method}: {'✅' if exists else '❌'}")

        # 测试功能性方法
        func_methods = ['get_cache_stats', 'cleanup_expired_cache']
        print("\n功能性方法检查:")
        for method in func_methods:
            exists = hasattr(cache_manager, method)
            print(f"  {method}: {'✅' if exists else '❌'}")

        # 实际测试每日积分功能
        print("\n功能测试:")
        test_user = "test_user_123"

        # 测试获取每日积分（应该返回0）
        daily_score = cache_manager.get_daily_score(test_user)
        print(f"  get_daily_score('{test_user}'): {daily_score} {'✅' if daily_score == 0 else '❌'}")

        # 测试增加每日积分
        success = cache_manager.add_daily_score(test_user, 10)
        print(f"  add_daily_score('{test_user}', 10): {'✅' if success else '❌'}")

        # 测试获取更新后的每日积分
        daily_score_after = cache_manager.get_daily_score(test_user)
        print(f"  get_daily_score('{test_user}') after add: {daily_score_after} {'✅' if daily_score_after == 10 else '❌'}")

        # 测试增量更新
        success = cache_manager.increment_daily_score(test_user, 5)
        print(f"  increment_daily_score('{test_user}', 5): {'✅' if success else '❌'}")

        # 测试最终积分
        final_score = cache_manager.get_daily_score(test_user)
        print(f"  final daily score: {final_score} {'✅' if final_score == 15 else '❌'}")

        # 测试重置
        success = cache_manager.reset_daily_score(test_user)
        print(f"  reset_daily_score('{test_user}'): {'✅' if success else '❌'}")

        # 测试重置后的积分
        reset_score = cache_manager.get_daily_score(test_user)
        print(f"  daily score after reset: {reset_score} {'✅' if reset_score == 0 else '❌'}")

        print("\n" + "=" * 50)
        print("所有测试完成！缓存管理器修复成功！")
        print("=" * 50)

        return True

    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_score_db_integration():
    """测试积分系统集成"""
    print("\n" + "=" * 50)
    print("测试积分系统集成")
    print("=" * 50)

    try:
        from func.score.score_db import ScoreDB

        # 创建积分系统实例
        score_db = ScoreDB()

        # 测试缓存管理器是否正确集成
        cache_manager = score_db.cache_manager
        print(f"缓存管理器集成: {'✅' if cache_manager is not None else '❌'}")

        # 测试每日积分方法是否可用
        test_user = "integration_test_user"
        daily_score = cache_manager.get_daily_score(test_user)
        print(f"每日积分方法可用: {'✅' if daily_score == 0 else '❌'}")

        print("\n积分系统集成测试完成！")
        return True

    except Exception as e:
        print(f"❌ 集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success1 = test_cache_manager_methods()
    success2 = test_score_db_integration()

    if success1 and success2:
        print("\n🎉 所有测试通过！缓存管理器修复完成！")
        sys.exit(0)
    else:
        print("\n❌ 部分测试失败，请检查错误信息")
        sys.exit(1)