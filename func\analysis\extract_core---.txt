J:\ai\�������\ai-yinmei\func\analysis\extract_core.c   extract_core.py extract extract_core.ExtractCore.extract    chat        extract_core.ExtractCore.chat   extract_core            Interpreter change detected - this module can only be loaded into one interpreter per process.  name    __loader__  loader      __file__    origin      __package__ parent      __path__        submodule_search_locations      Module 'extract_core' has already been imported. Re-initialisation is not supported.    builtins        cython_runtime  __builtins__    init extract_core       exactly s       %.200s() takes %.8s %zd positional argument%.1s (%zd given)     %s() got multiple values for keyword argument '%U'      %.200s() keywords must be strings       %s() got an unexpected keyword argument '%U'     while calling a Python object  NULL result without error in PyObject_Call      name '%U' is not defined        join() result is too long for a Python string   cannot import name %S   _cython_3_0_11  Shared Cython type %.200s is not a type object  Shared Cython type %.200s has the wrong size, try recompiling   keywords must be strings        __name__ must be set to a string object __qualname__ must be set to a string object     function's dictionary may not be deleted        setting function's dictionary to a non-dict     __defaults__ must be set to a tuple object      changes to cyfunction.__defaults__ will not currently affect the values used in function calls  __kwdefaults__ must be set to a dict object     changes to cyfunction.__kwdefaults__ will not currently affect the values used in function calls        __annotations__ must be set to a dict object    func_doc        __doc__ func_name       __name__        __qualname__    func_dict       __dict__        func_globals    __globals__     func_closure    __closure__     func_code       __code__        func_defaults   __defaults__    __kwdefaults__  __annotations__ _is_coroutine   __module__      __reduce__      Bad call flags for CyFunction   <cyfunction %U at %p>   %.200s() takes no arguments (%zd given) %.200s() takes exactly one argument (%zd given) %.200s() takes no keyword arguments     unbound method %.200S() needs an argument       %.200s() needs an argument      _cython_3_0_11.cython_function_or_method        %s (%s:%d)      does not match          compile time Python version %d.%d of module '%.100s' %s runtime version %d.%d   requests    搜索      headers info    exception       】信息回复异常   analysis        cline_in_traceback      func.tools.singleton_mode   role        你是一个文本提取助手，你需要从用户文本中提取场景的名称放进去name字段,name字段与这些场景名称[粉色房间、神社、海岸花坊、花房、清晨房间]模糊匹配，只能返回上面这些场景名称   p� �   __prepare__     你是一个文本提取助手，你需要从用户文本中提取搜索内容的标题放进去name字段和描述内容放进去content字段 time    唱歌  CommonData      defaultConfig   func.gobal.data 切歌  verify  __main__    chatId      __qualname__    func.config.default_config      __module__  self        username        __dict__        8� �   _initializing   tip 搜图  str ?       你是一个文本提取助手，你需要从用户文本中提取图画的名称放进去name字段和描述内容放进去content字段       J:\ai\吟美打包\ai-yinmei\func\analysis\extract_core.py  detail      _is_coroutine   super   __spec__    post                你是一个文本提取助手，你需要从用户文本中提取图片名称放进去name字段       application/json        choices intent  user    __import__  config      extract log     __doc__ content __set_name__    response    跳舞  uid func.log.default_log    message         你是一个文本提取助手，你需要从用户文本中提取歌曲的名称放进去name字段    提取文本内容：   DefaultLog      __init_subclass__       Content-Type    get_config  【 chat    ExtractCore     __annotations__ 我听不懂你说什么        ExtractCore.extract     timeout data    __test__        variables       fastgpt_url json        messages        切换场景    __name__    e   commonData      Authorization   extract_core    .               你是一个文本提取助手，你需要从用户文本中提取更换服装的名称放进去name字段,name字段与这些服装名称[便衣、爱的翅膀、青春猫娘、眼镜猫娘]模糊匹配，只能返回上面这些服装名称      ExtractCore.chat    stream      __metaclass__   getLogger   *   assistant_message       fastgpt_authorization   asyncio.coroutines      你是一个文本提取助手，你需要从用户文本中提取跳舞的舞蹈名称放进去name字段  换装      singleton   画画