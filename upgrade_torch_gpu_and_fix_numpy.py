#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
升级PyTorch到GPU版本并修复NumPy版本兼容性问题
"""
import subprocess
import sys
import os

# 目标环境路径
TARGET_ENV = r"H:/baidudown/AI-YinMei-v2.0.0/MB7-YM2-runtime"
PYTHON_EXE = os.path.join(TARGET_ENV, "python.exe")

def run_command(cmd, description="", timeout=600):
    """运行命令并处理错误"""
    print(f"\n🔄 {description}")
    print(f"执行命令: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(cmd, check=False, text=True, capture_output=True, timeout=timeout)
        if result.returncode == 0:
            print(f"✅ {description} 成功")
            if result.stdout.strip():
                print(f"输出: {result.stdout.strip()}")
            return True
        else:
            print(f"❌ {description} 失败")
            if result.stderr.strip():
                print(f"错误: {result.stderr.strip()}")
            return False
    except subprocess.TimeoutExpired:
        print(f"⏰ {description} 超时")
        return False
    except Exception as e:
        print(f"❌ {description} 异常: {e}")
        return False

def check_cuda_availability():
    """检查CUDA是否可用"""
    print("🔍 检查CUDA环境...")
    
    # 检查nvidia-smi
    try:
        result = subprocess.run(["nvidia-smi"], capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print("✅ 检测到NVIDIA GPU")
            # 提取CUDA版本信息
            lines = result.stdout.split('\n')
            for line in lines:
                if 'CUDA Version:' in line:
                    cuda_version = line.split('CUDA Version:')[1].strip().split()[0]
                    print(f"✅ CUDA版本: {cuda_version}")
                    return cuda_version
            return "11.8"  # 默认版本
        else:
            print("⚠️ 未检测到NVIDIA GPU，将安装CPU版本")
            return None
    except:
        print("⚠️ 无法检测GPU状态，将尝试安装GPU版本")
        return "11.8"  # 默认尝试CUDA 11.8

def uninstall_current_torch():
    """卸载当前的PyTorch"""
    print("\n🗑️ 卸载当前PyTorch...")
    
    torch_packages = [
        "torch", "torchvision", "torchaudio", "torchtext", 
        "torchdata", "pytorch-lightning", "torchmetrics"
    ]
    
    for package in torch_packages:
        cmd = [PYTHON_EXE, "-m", "pip", "uninstall", package, "-y"]
        run_command(cmd, f"卸载 {package}", timeout=120)

def install_torch_gpu(cuda_version=None):
    """安装GPU版本的PyTorch"""
    print(f"\n🚀 安装PyTorch GPU版本...")
    
    if cuda_version is None:
        # 安装CPU版本作为备选
        cmd = [PYTHON_EXE, "-m", "pip", "install", "torch", "torchvision", "torchaudio", 
               "--index-url", "https://download.pytorch.org/whl/cpu"]
        return run_command(cmd, "安装PyTorch CPU版本", timeout=900)
    
    # 根据CUDA版本选择合适的PyTorch
    if cuda_version.startswith("12"):
        # CUDA 12.x
        index_url = "https://download.pytorch.org/whl/cu121"
    elif cuda_version.startswith("11"):
        # CUDA 11.x
        index_url = "https://download.pytorch.org/whl/cu118"
    else:
        # 默认使用CUDA 11.8
        index_url = "https://download.pytorch.org/whl/cu118"
    
    cmd = [PYTHON_EXE, "-m", "pip", "install", "torch", "torchvision", "torchaudio", 
           "--index-url", index_url]
    
    success = run_command(cmd, f"安装PyTorch GPU版本 (CUDA {cuda_version})", timeout=1200)
    
    if not success:
        print("⚠️ GPU版本安装失败，尝试安装CPU版本...")
        cmd = [PYTHON_EXE, "-m", "pip", "install", "torch", "torchvision", "torchaudio", 
               "--index-url", "https://download.pytorch.org/whl/cpu"]
        return run_command(cmd, "安装PyTorch CPU版本", timeout=900)
    
    return success

def fix_numpy_version():
    """修复NumPy版本到1.26.3"""
    print("\n🔧 修复NumPy版本...")
    
    # 先卸载当前版本
    cmd = [PYTHON_EXE, "-m", "pip", "uninstall", "numpy", "-y"]
    run_command(cmd, "卸载当前NumPy", timeout=60)
    
    # 安装指定版本
    cmd = [PYTHON_EXE, "-m", "pip", "install", "numpy==1.26.3"]
    success = run_command(cmd, "安装NumPy 1.26.3", timeout=300)
    
    if not success:
        print("⚠️ 安装NumPy 1.26.3失败，尝试安装1.26.4...")
        cmd = [PYTHON_EXE, "-m", "pip", "install", "numpy==1.26.4"]
        success = run_command(cmd, "安装NumPy 1.26.4", timeout=300)
    
    return success

def fix_compatibility_issues():
    """修复其他兼容性问题"""
    print("\n🔧 修复兼容性问题...")
    
    # 可能需要降级的包
    compatibility_packages = [
        ("scipy", "1.11.4"),
        ("scikit-learn", "1.3.2"),
        ("pandas", "2.0.3"),
    ]
    
    for package, version in compatibility_packages:
        print(f"\n检查 {package} 兼容性...")
        cmd = [PYTHON_EXE, "-m", "pip", "install", f"{package}=={version}"]
        run_command(cmd, f"安装 {package} {version}", timeout=300)

def verify_installation():
    """验证安装结果"""
    print("\n🧪 验证安装结果...")
    
    # 验证PyTorch
    test_script = '''
import torch
import numpy as np
print(f"PyTorch版本: {torch.__version__}")
print(f"CUDA可用: {torch.cuda.is_available()}")
if torch.cuda.is_available():
    print(f"CUDA设备数量: {torch.cuda.device_count()}")
    print(f"当前CUDA设备: {torch.cuda.current_device()}")
    print(f"设备名称: {torch.cuda.get_device_name()}")
print(f"NumPy版本: {np.__version__}")

# 测试基本功能
try:
    x = torch.randn(3, 3)
    if torch.cuda.is_available():
        x_gpu = x.cuda()
        print("✅ GPU张量创建成功")
    print("✅ PyTorch基本功能正常")
except Exception as e:
    print(f"❌ PyTorch测试失败: {e}")

try:
    arr = np.array([1, 2, 3])
    print("✅ NumPy基本功能正常")
except Exception as e:
    print(f"❌ NumPy测试失败: {e}")
'''
    
    cmd = [PYTHON_EXE, "-c", test_script]
    return run_command(cmd, "验证安装", timeout=60)

def main():
    """主函数"""
    print("=== PyTorch GPU升级和NumPy版本修复 ===")
    
    # 检查环境
    if not os.path.exists(PYTHON_EXE):
        print(f"❌ Python可执行文件不存在: {PYTHON_EXE}")
        return
    
    # 检查CUDA
    cuda_version = check_cuda_availability()
    
    # 升级pip
    print("\n📦 升级pip...")
    cmd = [PYTHON_EXE, "-m", "pip", "install", "--upgrade", "pip"]
    run_command(cmd, "升级pip", timeout=120)
    
    # 卸载当前PyTorch
    uninstall_current_torch()
    
    # 修复NumPy版本
    fix_numpy_version()
    
    # 安装PyTorch GPU版本
    install_torch_gpu(cuda_version)
    
    # 修复兼容性问题
    fix_compatibility_issues()
    
    # 验证安装
    verify_installation()
    
    print("\n=== 升级完成 ===")
    print("建议重启应用程序以确保所有更改生效")

if __name__ == "__main__":
    main()
