#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
安装音乐转换服务所需的依赖
"""
import subprocess
import sys

def install_package(package):
    """安装Python包"""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        print(f"✓ 成功安装 {package}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ 安装 {package} 失败: {e}")
        return False

def main():
    """主函数"""
    print("=== 安装音乐转换服务依赖 ===")
    
    # 音乐转换服务所需的依赖
    dependencies = [
        "selenium",
        "webdriver-manager",
        "pydub",
        "pedalboard",
        "flask",
        "requests",
        "pathlib",
    ]
    
    success_count = 0
    total_count = len(dependencies)
    
    for package in dependencies:
        print(f"正在安装 {package}...")
        if install_package(package):
            success_count += 1
    
    print(f"\n=== 安装完成 ===")
    print(f"成功安装: {success_count}/{total_count}")
    
    if success_count == total_count:
        print("✓ 所有依赖安装成功！")
        print("现在可以运行音乐转换服务了。")
    else:
        print("✗ 部分依赖安装失败，请手动检查并安装。")
    
    print("\n注意事项：")
    print("1. 如果使用selenium，可能还需要安装Chrome浏览器和ChromeDriver")
    print("2. 某些音频处理库可能需要额外的系统依赖")
    print("3. 建议在虚拟环境中运行")

if __name__ == "__main__":
    main()
