#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试异步学歌功能
"""
import asyncio
import time
import threading
from func.sing.sing_core import SingCore

def test_async_sing_functionality():
    """测试异步学歌功能"""
    print("=== 测试异步学歌功能 ===")
    
    try:
        # 创建SingCore实例
        sing_core = SingCore()
        print("✅ SingCore实例创建成功")
        
        # 测试异步状态检查方法
        async def test_async_check():
            print("🔄 开始测试异步状态检查...")
            
            # 模拟歌曲状态检查
            result = await sing_core.async_check_song_status("测试歌曲", "测试查询")
            print(f"✅ 异步状态检查完成，结果: {result}")
            return result
        
        # 运行异步测试
        def run_async_test():
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                result = loop.run_until_complete(test_async_check())
                return result
            finally:
                loop.close()
        
        # 在线程中运行异步测试
        print("🚀 启动异步测试线程...")
        test_thread = threading.Thread(target=run_async_test)
        test_thread.start()
        
        # 等待测试完成
        test_thread.join(timeout=10)
        
        if test_thread.is_alive():
            print("⚠️ 异步测试超时")
        else:
            print("✅ 异步测试完成")
        
        print("=== 异步学歌功能测试完成 ===")
        
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()

def test_logging_levels():
    """测试日志级别设置"""
    print("\n=== 测试日志级别设置 ===")
    
    import logging
    
    # 检查各个模块的日志级别
    modules_to_check = [
        'apscheduler',
        'apscheduler.scheduler',
        'websockets',
        'urllib3',
        'requests'
    ]
    
    for module_name in modules_to_check:
        logger = logging.getLogger(module_name)
        level_name = logging.getLevelName(logger.level)
        print(f"📊 {module_name}: {level_name} ({logger.level})")
    
    print("✅ 日志级别检查完成")

def simulate_concurrent_operations():
    """模拟并发操作，测试异步性能"""
    print("\n=== 模拟并发操作测试 ===")
    
    def simulate_task(task_id, duration):
        """模拟任务"""
        print(f"🔄 任务 {task_id} 开始 (预计耗时 {duration}s)")
        time.sleep(duration)
        print(f"✅ 任务 {task_id} 完成")
    
    # 创建多个并发任务
    threads = []
    start_time = time.time()
    
    for i in range(3):
        thread = threading.Thread(target=simulate_task, args=(i+1, 2))
        threads.append(thread)
        thread.start()
    
    # 等待所有任务完成
    for thread in threads:
        thread.join()
    
    end_time = time.time()
    total_time = end_time - start_time
    
    print(f"🎯 所有并发任务完成，总耗时: {total_time:.2f}s")
    
    if total_time < 6:  # 如果是串行执行，应该需要6秒
        print("✅ 并发执行正常")
    else:
        print("⚠️ 可能存在阻塞问题")

if __name__ == "__main__":
    print("🎵 异步学歌功能测试开始")
    
    # 测试日志级别
    test_logging_levels()
    
    # 测试并发操作
    simulate_concurrent_operations()
    
    # 测试异步学歌功能
    test_async_sing_functionality()
    
    print("\n🎉 所有测试完成！")
    print("\n💡 改进说明:")
    print("1. ✅ 学歌状态检查已改为异步，不会阻塞主线程")
    print("2. ✅ 调度器日志级别已降低，减少DEBUG输出")
    print("3. ✅ WebSocket日志级别已降低")
    print("4. ✅ 使用ThreadPoolExecutor管理异步任务")
    print("5. ✅ 保持了原有的功能逻辑和错误处理")
