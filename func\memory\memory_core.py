import asyncio
from func.tools.singleton_mode import singleton
# from func.tools.decorator_mode import *
from func.log.default_log import DefaultLog
from func.llm.chat_db import ChatDB
from func.memory.chroma_server import ChromaServer
from func.gobal.data import SecretData, MemoryData

log = DefaultLog().getLogger()

@singleton
class MemoryCore:
    def __init__(self):
        self.chatDB = ChatDB()
        self.chromaServer = ChromaServer() #"http://127.0.0.1:9889/"
        self._initializing = False
        
    async def memory_reset_inputdata(self, username: str, uid: str):
        """重置记忆输入数据"""
        try:
            # 重置用户的记忆数据
            await self.chatDB.memory_reset(username, uid)
            return True
        except Exception as e:
            print(e)
            log.error(f"重置记忆数据失败: {str(e)}")
            return False

    async def memory_search(self, username: str, prompt: str, **kwargs):
        """搜索记忆"""
        try:
            # 从chromaServer中搜索相关记忆
            results = await self.chromaServer.search(
                username=username,
                text=prompt,
                **kwargs
            )
            return results
        except Exception as e:
            print(e)
            log.error(f"搜索记忆失败: {str(e)}")
            return None

    async def memory_add_batch(self, username: str, documents: list, **kwargs):
        """批量添加记忆"""
        try:
            # 向chromaServer中添加记忆
            status = await self.chromaServer.add_documents(
                username=username,
                documents=documents,
                **kwargs
            )
            return status
        except Exception as e:
            print(e)
            log.error(f"添加记忆失败: {str(e)}")
            return False 