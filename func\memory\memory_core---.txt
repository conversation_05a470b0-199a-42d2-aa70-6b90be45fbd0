J:\ai\�������\ai-yinmei\func\memory\memory_core.c      memory_core.py  __init__        memory_core.MemoryCore.__init__ memory_reset_inputdata  memory_core.MemoryCore.memory_reset_inputdata   memory_search   memory_core.MemoryCore.memory_search    memory_add_batch        memory_core.MemoryCore.memory_add_batch memory_core     Interpreter change detected - this module can only be loaded into one interpreter per process.  name    __loader__  loader      __file__    origin      __package__ parent      __path__        submodule_search_locations      Module 'memory_core' has already been imported. Re-initialisation is not supported.     builtins        cython_runtime  __builtins__    init memory_core        %s() got multiple values for keyword argument '%U'      %.200s() keywords must be strings       %s() got an unexpected keyword argument '%U'    exactly s       %.200s() takes %.8s %zd positional argument%.1s (%zd given)      while calling a Python object  NULL result without error in PyObject_Call      cannot import name %S   name '%U' is not defined        _cython_3_0_11  Shared Cython type %.200s is not a type object  Shared Cython type %.200s has the wrong size, try recompiling   keywords must be strings        __name__ must be set to a string object __qualname__ must be set to a string object     function's dictionary may not be deleted        setting function's dictionary to a non-dict     __defaults__ must be set to a tuple object      changes to cyfunction.__defaults__ will not currently affect the values used in function calls  __kwdefaults__ must be set to a dict object     changes to cyfunction.__kwdefaults__ will not currently affect the values used in function calls        __annotations__ must be set to a dict object    func_doc        __doc__ func_name       __name__        __qualname__    func_dict       __dict__        func_globals    __globals__     func_closure    __closure__     func_code       __code__        func_defaults   __defaults__    __kwdefaults__  __annotations__ _is_coroutine   __module__      __reduce__      Bad call flags for CyFunction   <cyfunction %U at %p>   %.200s() takes no arguments (%zd given) %.200s() takes exactly one argument (%zd given) %.200s() takes no keyword arguments     unbound method %.200S() needs an argument       %.200s() needs an argument      _cython_3_0_11.cython_function_or_method        %s (%s:%d)      does not match          compile time Python version %d.%d of module '%.100s' %s runtime version %d.%d   requests        chromaServer    key_verify  ?   MemoryCore.memory_search        SecretData      cline_in_traceback      func.tools.singleton_mode       0� �   __prepare__ .   chatDB  func.gobal.data jsonData        secretData      __main__        memoryData      __qualname__    __module__  self        username        memory_core     __dict__        �� �   _initializing   prompt  memory_add_batch        memory_reset_inputdata  MemoryData  ChatDB  switch      document        _is_coroutine   super   __spec__        MemoryCore.memory_add_batch     MemoryCore.__init__     __import__  log doc     __set_name__    status  uid     func.log.default_log    submitTime      __name__        DefaultLog      __init_subclass__   data    text        __test__        func.tools.decorator_mode       长期记忆    MemoryCore.memory_reset_inputdata   key func.llm.chat_db        func.memory.chroma_server       __metaclass__   ChromaServer    getLogger       all_chat        __init__        traceid ,       asyncio.coroutines  name        MemoryCore      __doc__ singleton       memory_reset    *       memory_search   J:\ai\吟美打包\ai-yinmei\func\memory\memory_core.py