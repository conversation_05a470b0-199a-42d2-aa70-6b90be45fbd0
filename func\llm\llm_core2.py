import threading
import json
import datetime
import uuid
import re
from queue import Queue

from func.memory.memory_core import MemoryCore
from func.analysis.analysis_core import AnalysisCore 
# from func.entrance.entrance_core import EntranceCore
from func.vtuber.action_oper import ActionOper
from func.tts.tts_core import TTsCore
from func.sing.sing_core import SingCore
from func.user.user_db import UserDB
from func.llm.chat_db import ChatDB
from func.tools.string_util import StringUtil
from func.log.default_log import DefaultLog

class LLmCore:
    def __init__(self):
        self.memoryCore = MemoryCore()
        self.analysisCore = AnalysisCore()
        # self.entranceCore = EntranceCore() 
        self.actionOper = ActionOper()
        self.ttsCore = TTsCore()
        self.singCore = SingCore()
        self.userDB = UserDB()
        self.chatDB = ChatDB()
        
        # 问答队列
        self.QuestionList = Queue()
        self.AnswerList = Queue()
        
        # AI状态
        self.is_ai_ready = True
        
        # 欢迎语相关
        self.WelcomeList = []
        self.WelcomeData = {}
        
    def aiResponseTry(self):
        """
        如果AI没有在生成回复且队列中还有问题 则创建一个生成的线程
        """
        try:
            if self.is_ai_ready and not self.QuestionList.empty():
                self.is_ai_ready = False
                thread = threading.Thread(target=self.ai_response)
                thread.start()
        except Exception as e:
            print(e)
            DefaultLog.error(f"【ai_response】发生了异常：{str(e)}")
            self.is_ai_ready = True
            
    def ai_response(self):
        """
        从问题队列中提取一条，生成回复并存入回复队列中
        """
        try:
            question = self.QuestionList.get()
            # 处理回复逻辑
            response = self.generate_response(question)
            self.AnswerList.put(response)
            
        except Exception as e:
            print(e)
            DefaultLog.error(f"记录聊天异常: {str(e)}")
        finally:
            self.is_ai_ready = True
            
    def insert_chat(self, username, content):
        """插入一条聊天记录到问题队列"""
        try:
            self.QuestionList.put({
                "username": username,
                "content": content,
                "time": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "traceid": str(uuid.uuid4())
            })
        except Exception as e:
            print(e)
            DefaultLog.error(f"insert_chat error: {str(e)}")
            
    def analysis_content(self, content):
        """分析用户输入内容"""
        return self.analysisCore.intent_analysis(content)
        
    def check_answer(self, text):
        """检查回答内容"""
        return self.analysisCore.check_answer(text)
        
    def check_welcome_room(self, username):
        """检查欢迎消息"""
        if username in self.WelcomeList:
            return True
        return False
        
    def msg_deal(self, msg):
        """处理接收到的消息"""
        try:
            content = self.analysis_content(msg)
            if content:
                self.insert_chat(msg["username"], content)
        except Exception as e:
            print(e)
            DefaultLog.error(f"msg_deal error: {str(e)}")