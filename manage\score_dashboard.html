<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>积分系统管理仪表板</title>
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script src="https://unpkg.com/axios/dist/axios.min.js"></script>
    <script src="https://unpkg.com/chart.js"></script>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            min-height: 100vh;
        }

        .dashboard-container {
            padding: 20px;
            max-width: 1400px;
            margin: 0 auto;
        }

        .dashboard-header {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
        }

        .dashboard-title {
            font-size: 2.5rem;
            font-weight: bold;
            color: #2c3e50;
            text-align: center;
            margin-bottom: 10px;
        }

        .dashboard-subtitle {
            text-align: center;
            color: #7f8c8d;
            font-size: 1.1rem;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2);
        }

        .stat-icon {
            font-size: 2.5rem;
            margin-bottom: 15px;
            color: #667eea;
        }

        .stat-value {
            font-size: 2.2rem;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 5px;
        }

        .stat-label {
            color: #7f8c8d;
            font-size: 1rem;
            margin-bottom: 10px;
        }

        .stat-change {
            font-size: 0.9rem;
            font-weight: 500;
        }

        .stat-change.positive {
            color: #27ae60;
        }

        .stat-change.negative {
            color: #e74c3c;
        }

        .chart-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }

        .chart-title {
            font-size: 1.4rem;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 20px;
            text-align: center;
        }

        .activity-feed {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            max-height: 500px;
            overflow-y: auto;
        }

        .activity-item {
            display: flex;
            align-items: center;
            padding: 15px 0;
            border-bottom: 1px solid #ecf0f1;
        }

        .activity-item:last-child {
            border-bottom: none;
        }

        .activity-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            font-size: 1.2rem;
            color: white;
        }

        .activity-icon.score-add {
            background: #27ae60;
        }

        .activity-icon.score-subtract {
            background: #e74c3c;
        }

        .activity-icon.user-register {
            background: #3498db;
        }

        .activity-content {
            flex: 1;
        }

        .activity-title {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 5px;
        }

        .activity-time {
            font-size: 0.9rem;
            color: #7f8c8d;
        }

        .refresh-btn {
            position: fixed;
            bottom: 30px;
            right: 30px;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border: none;
            color: white;
            font-size: 1.5rem;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .refresh-btn:hover {
            transform: scale(1.1);
            box-shadow: 0 12px 35px rgba(0, 0, 0, 0.3);
        }

        .loading {
            text-align: center;
            padding: 50px;
            color: #7f8c8d;
        }

        .error {
            background: #e74c3c;
            color: white;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            text-align: center;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .dashboard-container {
                padding: 10px;
            }

            .dashboard-title {
                font-size: 2rem;
            }

            .stats-grid {
                grid-template-columns: 1fr;
            }

            .stat-card {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="dashboard-container">
            <!-- 头部 -->
            <div class="dashboard-header">
                <h1 class="dashboard-title">
                    <i class="fas fa-chart-line"></i>
                    积分系统管理仪表板
                </h1>
                <p class="dashboard-subtitle">实时监控积分系统运行状态和用户活动</p>
            </div>

            <!-- 错误提示 -->
            <div v-if="error" class="error">
                <i class="fas fa-exclamation-triangle"></i>
                {{ error }}
            </div>

            <!-- 加载状态 -->
            <div v-if="loading" class="loading">
                <i class="fas fa-spinner fa-spin fa-2x"></i>
                <p>正在加载数据...</p>
            </div>

            <!-- 统计卡片 -->
            <div v-if="!loading" class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="stat-value">{{ stats.totalUsers || 0 }}</div>
                    <div class="stat-label">总用户数</div>
                    <div class="stat-change positive">
                        <i class="fas fa-arrow-up"></i>
                        +{{ stats.newUsersToday || 0 }} 今日新增
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-coins"></i>
                    </div>
                    <div class="stat-value">{{ formatNumber(stats.totalScore) || 0 }}</div>
                    <div class="stat-label">总积分</div>
                    <div class="stat-change" :class="stats.scoreChangeToday >= 0 ? 'positive' : 'negative'">
                        <i :class="stats.scoreChangeToday >= 0 ? 'fas fa-arrow-up' : 'fas fa-arrow-down'"></i>
                        {{ stats.scoreChangeToday >= 0 ? '+' : '' }}{{ stats.scoreChangeToday || 0 }} 今日变化
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-exchange-alt"></i>
                    </div>
                    <div class="stat-value">{{ stats.totalOperations || 0 }}</div>
                    <div class="stat-label">总操作数</div>
                    <div class="stat-change positive">
                        <i class="fas fa-arrow-up"></i>
                        +{{ stats.operationsToday || 0 }} 今日操作
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-percentage"></i>
                    </div>
                    <div class="stat-value">{{ stats.successRate || 0 }}%</div>
                    <div class="stat-label">成功率</div>
                    <div class="stat-change" :class="stats.successRate >= 95 ? 'positive' : 'negative'">
                        <i :class="stats.successRate >= 95 ? 'fas fa-check' : 'fas fa-exclamation'"></i>
                        {{ stats.successRate >= 95 ? '优秀' : '需要关注' }}
                    </div>
                </div>
            </div>

            <!-- 图表区域 -->
            <div v-if="!loading" class="row">
                <div class="col-md-8">
                    <div class="chart-container">
                        <h3 class="chart-title">积分变化趋势</h3>
                        <canvas id="scoreChart" width="400" height="200"></canvas>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="chart-container">
                        <h3 class="chart-title">操作类型分布</h3>
                        <canvas id="operationChart" width="300" height="300"></canvas>
                    </div>
                </div>
            </div>

            <!-- 最近活动 -->
            <div v-if="!loading" class="activity-feed">
                <h3 class="chart-title">最近活动</h3>
                <div v-for="activity in recentActivities" :key="activity.id" class="activity-item">
                    <div class="activity-icon" :class="getActivityIconClass(activity.type)">
                        <i :class="getActivityIcon(activity.type)"></i>
                    </div>
                    <div class="activity-content">
                        <div class="activity-title">{{ activity.title }}</div>
                        <div class="activity-time">{{ formatTime(activity.timestamp) }}</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 刷新按钮 -->
        <button class="refresh-btn" @click="refreshData" :disabled="loading">
            <i class="fas fa-sync-alt" :class="{ 'fa-spin': loading }"></i>
        </button>
    </div>

    <script>
        const { createApp } = Vue;

        createApp({
            data() {
                return {
                    loading: true,
                    error: null,
                    stats: {
                        totalUsers: 0,
                        newUsersToday: 0,
                        totalScore: 0,
                        scoreChangeToday: 0,
                        totalOperations: 0,
                        operationsToday: 0,
                        successRate: 0
                    },
                    recentActivities: [],
                    scoreChart: null,
                    operationChart: null
                }
            },
            mounted() {
                this.loadData();
                // 每30秒自动刷新数据
                setInterval(() => {
                    this.loadData(false);
                }, 30000);
            },
            methods: {
                async loadData(showLoading = true) {
                    if (showLoading) {
                        this.loading = true;
                    }
                    this.error = null;

                    try {
                        // 并行加载所有数据
                        const [statsResponse, activitiesResponse] = await Promise.all([
                            axios.get('/score/stats'),
                            axios.get('/score/activities')
                        ]);

                        // 处理统计数据
                        if (statsResponse.data.status === 'success') {
                            this.stats = {
                                totalUsers: statsResponse.data.data.total_users || 0,
                                newUsersToday: statsResponse.data.data.new_users_today || 0,
                                totalScore: statsResponse.data.data.total_score || 0,
                                scoreChangeToday: statsResponse.data.data.score_change_today || 0,
                                totalOperations: statsResponse.data.data.total_operations || 0,
                                operationsToday: statsResponse.data.data.operations_today || 0,
                                successRate: Math.round(statsResponse.data.data.success_rate || 0)
                            };
                        }

                        // 处理活动数据
                        if (activitiesResponse.data.status === 'success') {
                            this.recentActivities = (activitiesResponse.data.data || []).map(activity => ({
                                id: activity.id || Date.now() + Math.random(),
                                type: activity.operation || 'unknown',
                                title: this.getActivityTitle(activity),
                                timestamp: activity.timestamp || activity.submitTime
                            }));
                        }

                        // 更新图表
                        this.$nextTick(() => {
                            this.updateCharts();
                        });

                    } catch (error) {
                        console.error('加载数据失败:', error);
                        this.error = '加载数据失败，请检查网络连接或稍后重试';

                        // 使用模拟数据作为备选方案
                        this.loadMockData();
                    } finally {
                        this.loading = false;
                    }
                },

                loadMockData() {
                    // 模拟数据，用于演示
                    this.stats = {
                        totalUsers: 1250,
                        newUsersToday: 23,
                        totalScore: 125000,
                        scoreChangeToday: 1500,
                        totalOperations: 8750,
                        operationsToday: 156,
                        successRate: 98
                    };

                    this.recentActivities = [
                        {
                            id: 1,
                            type: 'score_add',
                            title: '用户 张三 获得 10 积分 - 聊天',
                            timestamp: Date.now() - 300000
                        },
                        {
                            id: 2,
                            type: 'score_subtract',
                            title: '用户 李四 消耗 2 积分 - 点歌',
                            timestamp: Date.now() - 600000
                        },
                        {
                            id: 3,
                            type: 'user_register',
                            title: '新用户 王五 注册',
                            timestamp: Date.now() - 900000
                        },
                        {
                            id: 4,
                            type: 'score_add',
                            title: '用户 赵六 获得 50 积分 - 充值',
                            timestamp: Date.now() - 1200000
                        },
                        {
                            id: 5,
                            type: 'score_subtract',
                            title: '用户 钱七 消耗 3 积分 - 跳舞',
                            timestamp: Date.now() - 1500000
                        }
                    ];

                    this.$nextTick(() => {
                        this.updateCharts();
                    });
                },

                updateCharts() {
                    this.createScoreChart();
                    this.createOperationChart();
                },

                createScoreChart() {
                    const ctx = document.getElementById('scoreChart');
                    if (!ctx) return;

                    if (this.scoreChart) {
                        this.scoreChart.destroy();
                    }

                    // 生成最近7天的模拟数据
                    const labels = [];
                    const data = [];
                    const today = new Date();

                    for (let i = 6; i >= 0; i--) {
                        const date = new Date(today);
                        date.setDate(date.getDate() - i);
                        labels.push(date.toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' }));

                        // 模拟积分变化数据
                        const baseScore = 120000;
                        const variation = Math.random() * 5000 - 2500;
                        data.push(baseScore + variation + (i * 500));
                    }

                    this.scoreChart = new Chart(ctx, {
                        type: 'line',
                        data: {
                            labels: labels,
                            datasets: [{
                                label: '总积分',
                                data: data,
                                borderColor: '#667eea',
                                backgroundColor: 'rgba(102, 126, 234, 0.1)',
                                borderWidth: 3,
                                fill: true,
                                tension: 0.4
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            plugins: {
                                legend: {
                                    display: false
                                }
                            },
                            scales: {
                                y: {
                                    beginAtZero: false,
                                    grid: {
                                        color: 'rgba(0,0,0,0.1)'
                                    }
                                },
                                x: {
                                    grid: {
                                        color: 'rgba(0,0,0,0.1)'
                                    }
                                }
                            }
                        }
                    });
                },

                createOperationChart() {
                    const ctx = document.getElementById('operationChart');
                    if (!ctx) return;

                    if (this.operationChart) {
                        this.operationChart.destroy();
                    }

                    this.operationChart = new Chart(ctx, {
                        type: 'doughnut',
                        data: {
                            labels: ['聊天', '点歌', '跳舞', '绘画', '充值', '其他'],
                            datasets: [{
                                data: [45, 20, 15, 10, 8, 2],
                                backgroundColor: [
                                    '#667eea',
                                    '#764ba2',
                                    '#f093fb',
                                    '#f5576c',
                                    '#4facfe',
                                    '#43e97b'
                                ],
                                borderWidth: 0
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            plugins: {
                                legend: {
                                    position: 'bottom',
                                    labels: {
                                        padding: 20,
                                        usePointStyle: true
                                    }
                                }
                            }
                        }
                    });
                },

                refreshData() {
                    this.loadData(true);
                },

                formatNumber(num) {
                    if (!num) return '0';
                    return num.toLocaleString('zh-CN');
                },

                formatTime(timestamp) {
                    if (!timestamp) return '';
                    const date = new Date(timestamp);
                    const now = new Date();
                    const diff = now - date;

                    if (diff < 60000) {
                        return '刚刚';
                    } else if (diff < 3600000) {
                        return Math.floor(diff / 60000) + '分钟前';
                    } else if (diff < 86400000) {
                        return Math.floor(diff / 3600000) + '小时前';
                    } else {
                        return date.toLocaleDateString('zh-CN') + ' ' + date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
                    }
                },

                getActivityTitle(activity) {
                    const operation = activity.operation || '';
                    const userName = activity.userName || '用户';
                    const score = activity.score || 0;

                    switch (operation) {
                        case '聊天':
                            return `${userName} 通过聊天获得 ${score} 积分`;
                        case '点歌':
                            return `${userName} 点歌消耗 ${Math.abs(score)} 积分`;
                        case '跳舞':
                            return `${userName} 跳舞消耗 ${Math.abs(score)} 积分`;
                        case '绘画':
                            return `${userName} 绘画消耗 ${Math.abs(score)} 积分`;
                        case '充值':
                            return `${userName} 充值获得 ${score} 积分`;
                        default:
                            return `${userName} 进行了 ${operation} 操作`;
                    }
                },

                getActivityIcon(type) {
                    switch (type) {
                        case 'score_add':
                        case '聊天':
                        case '充值':
                            return 'fas fa-plus';
                        case 'score_subtract':
                        case '点歌':
                        case '跳舞':
                        case '绘画':
                            return 'fas fa-minus';
                        case 'user_register':
                            return 'fas fa-user-plus';
                        default:
                            return 'fas fa-circle';
                    }
                },

                getActivityIconClass(type) {
                    switch (type) {
                        case 'score_add':
                        case '聊天':
                        case '充值':
                            return 'score-add';
                        case 'score_subtract':
                        case '点歌':
                        case '跳舞':
                        case '绘画':
                            return 'score-subtract';
                        case 'user_register':
                            return 'user-register';
                        default:
                            return 'score-add';
                    }
                }
            }
        }).mount('#app');
    </script>
</body>
</html>