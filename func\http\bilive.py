import requests
from func.tools.singleton_mode import singleton
from func.log.default_log import DefaultLog

@singleton
class BiliveApi:
    def __init__(self):
        """初始化B站直播API客户端"""
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:131.0) Gecko/20100101 Firefox/131.0'
        }
        self.log = DefaultLog().getLogger()

    async def queryContributionRank(self, uid: str, room_id: str):
        """
        获取b站直播间用户排行榜
        :param uid: 主播id
        :param room_id: 直播房间号
        :return: 排行榜数据
        """
        try:
            url = f"https://api.live.bilibili.com/xlive/general-interface/v1/rank/queryContributionRank?ruid={uid}"
            url += f"&room_id={room_id}&page=1&page_size=100&type=online_rank&switch=contribution_rank&platform=web"
            
            response = requests.get(url, headers=self.headers, timeout=30)
            return response.json()
        except Exception as e:
            print(e)
            self.log.error(f"queryContributionRank异常: {str(e)}")
            return None