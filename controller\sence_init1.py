import asyncio
from func.obs.obs_init import ObsInit
from func.log.default_log import DefaultLog
from func.vtuber.action_oper import ActionOper
from func.vtuber.emote_oper import EmoteOper
from func.gobal.data import CommonData, VtuberData

class VideoControl:
    def __init__(self):
        self.log = DefaultLog().getLogger()
        self.obs = None
        self.emote_ws = None
        self.actionOper = None
        self.emoteOper = None
        self.commonData = CommonData()
        self.vtuberData = VtuberData()
        self.rooms = {}  # 添加rooms字典用于存储场景

    def init_scene(self):
        """初始化场景"""
        try:
            # 初始化OBS连接
            self.obs = ObsInit().get_ws()
            
            # 初始化动作和表情操作器
            self.actionOper = ActionOper()
            self.emoteOper = EmoteOper()

            # 初始化场景元素
            self.rooms = {
                "唱歌视频": "video",
                "波形": "visible_input",
                "状态提示": "show_text",
                "表情": "emote",
                "伴奏": "bgm",
                "便衣": "now_clothes"
            }
            
            # 设置初始场景状态
            for scene_name, scene_id in self.rooms.items():
                await self.obs.set_scene_visible(scene_name, False)
            
            # 检查场景
            await self.check_scene_time()
            
            # 设置初始状态
            self.vtuberData.value = "初始化"
            return True

        except Exception as e:
            print(e)
            self.log.error(f"初始化场景错误: {str(e)}")
            return False

    async def check_scene_time(self):
        """检查场景时间"""
        try:
            current_time = self.commonData.value
            # 根据时间检查并更新场景状态
            if current_time:
                # 更新场景可见性
                await self.obs.set_scene_visible("状态提示", True)
                await self.obs.set_scene_visible("波形", True)
                
                # 根据状态设置其他场景
                if self.vtuberData.value != "STOP":
                    await self.obs.set_scene_visible("表情", True)
                    await self.obs.set_scene_visible("便衣", True)
        except Exception as e:
            print(e)
            self.log.error(f"检查场景时间错误: {str(e)}")

    async def control_video(self, action="STOP"):
        """控制视频播放"""
        try:
            if action == "STOP":
                # 停止所有视频相关场景
                await self.obs.set_scene_visible("唱歌视频", False)
                await self.obs.set_scene_visible("伴奏", False)
                # 更新状态
                self.vtuberData.value = "STOP"
            else:
                # 启动视频相关场景
                await self.obs.set_scene_visible("唱歌视频", True)
                await self.obs.set_scene_visible("伴奏", True)
                # 更新状态
                self.vtuberData.value = "播放"
                
            # 同步表情状态
            await self.emoteOper.update_state(self.vtuberData.value)
            # 同步动作状态
            await self.actionOper.update_state(self.vtuberData.value)
            
        except Exception as e:
            print(e)
            self.log.error(f"视频控制错误: {str(e)}")

    async def play_video(self, video_path):
        """播放视频"""
        try:
            # 设置视频源
            await self.obs.set_media_source("唱歌视频", video_path)
            # 开始播放
            await self.control_video("PLAY")
            # 设置音频源
            audio_path = video_path.replace(".mp4", ".mp3")
            await self.obs.set_media_source("伴奏", audio_path)
            
            # 更新状态
            self.vtuberData.value = "播放"
            await self.emote_ws.send({
                "type": "video_start",
                "path": video_path
            })
            
        except Exception as e:
            print(e)
            self.log.error(f"播放视频错误: {str(e)}")
            # 发生错误时停止播放
            await self.control_video("STOP")