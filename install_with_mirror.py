#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用国内镜像源安装PyTorch GPU版本和修复NumPy
"""
import subprocess
import sys
import os

# 目标环境路径
TARGET_ENV = r"H:/baidudown/AI-YinMei-v2.0.0/MB7-YM2-runtime"
PYTHON_EXE = os.path.join(TARGET_ENV, "python.exe")

# 国内镜像源
MIRRORS = {
    "tsinghua": "https://pypi.tuna.tsinghua.edu.cn/simple/",
    "aliyun": "https://mirrors.aliyun.com/pypi/simple/",
    "douban": "https://pypi.douban.com/simple/",
    "ustc": "https://pypi.mirrors.ustc.edu.cn/simple/"
}

def run_pip_command(packages, description="", mirror="tsinghua", extra_args=None, timeout=600):
    """使用镜像源运行pip命令"""
    print(f"\n🔄 {description}")
    
    cmd = [PYTHON_EXE, "-m", "pip", "install"] + packages
    cmd.extend(["-i", MIRRORS[mirror], "--trusted-host", mirror + ".edu.cn"])
    
    if extra_args:
        cmd.extend(extra_args)
    
    print(f"执行命令: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(cmd, check=False, text=True, timeout=timeout)
        if result.returncode == 0:
            print(f"✅ {description} 成功")
            return True
        else:
            print(f"❌ {description} 失败，返回码: {result.returncode}")
            return False
    except subprocess.TimeoutExpired:
        print(f"⏰ {description} 超时")
        return False
    except Exception as e:
        print(f"❌ {description} 异常: {e}")
        return False

def configure_pip_mirror():
    """配置pip使用国内镜像"""
    print("🔧 配置pip使用清华镜像...")
    
    # 升级pip
    cmd = [PYTHON_EXE, "-m", "pip", "install", "--upgrade", "pip", 
           "-i", MIRRORS["tsinghua"], "--trusted-host", "pypi.tuna.tsinghua.edu.cn"]
    
    try:
        subprocess.run(cmd, check=False, timeout=120)
        print("✅ pip升级完成")
    except:
        print("⚠️ pip升级可能失败，继续执行...")

def install_numpy_compatible():
    """安装兼容的NumPy版本"""
    print("\n📦 安装NumPy 1.26.3...")
    
    # 先卸载
    cmd = [PYTHON_EXE, "-m", "pip", "uninstall", "numpy", "-y"]
    try:
        subprocess.run(cmd, timeout=60)
    except:
        pass
    
    # 安装指定版本
    return run_pip_command(["numpy==1.26.3"], "安装NumPy 1.26.3", timeout=300)

def install_torch_gpu():
    """安装PyTorch GPU版本"""
    print("\n🚀 安装PyTorch GPU版本...")
    
    # 先卸载现有版本
    torch_packages = ["torch", "torchvision", "torchaudio"]
    for pkg in torch_packages:
        cmd = [PYTHON_EXE, "-m", "pip", "uninstall", pkg, "-y"]
        try:
            subprocess.run(cmd, timeout=60)
        except:
            pass
    
    # 尝试安装GPU版本（CUDA 11.8）
    print("尝试安装CUDA 11.8版本...")
    cmd = [PYTHON_EXE, "-m", "pip", "install", "torch", "torchvision", "torchaudio", 
           "--index-url", "https://download.pytorch.org/whl/cu118", "--timeout", "300"]
    
    try:
        result = subprocess.run(cmd, check=False, timeout=900)
        if result.returncode == 0:
            print("✅ PyTorch GPU版本安装成功")
            return True
    except:
        pass
    
    # 如果GPU版本失败，使用镜像安装CPU版本
    print("GPU版本安装失败，使用镜像安装CPU版本...")
    return run_pip_command(["torch", "torchvision", "torchaudio"], "安装PyTorch CPU版本", timeout=600)

def install_essential_packages():
    """安装必要的包"""
    print("\n📦 安装必要的依赖包...")
    
    essential_packages = [
        "scipy==1.11.4",
        "scikit-learn==1.3.2", 
        "librosa",
        "soundfile",
        "pydub",
        "flask",
        "requests",
        "selenium",
        "webdriver-manager"
    ]
    
    success_count = 0
    for package in essential_packages:
        if run_pip_command([package], f"安装 {package}", timeout=300):
            success_count += 1
    
    print(f"✅ 成功安装 {success_count}/{len(essential_packages)} 个包")
    return success_count > len(essential_packages) * 0.8  # 80%成功率

def test_installation():
    """测试安装结果"""
    print("\n🧪 测试安装结果...")
    
    test_script = '''
try:
    import torch
    import numpy as np
    import librosa
    import flask
    print(f"✅ PyTorch: {torch.__version__}")
    print(f"✅ NumPy: {np.__version__}")
    print(f"✅ CUDA可用: {torch.cuda.is_available()}")
    print("✅ 核心依赖导入成功")
except Exception as e:
    print(f"❌ 导入失败: {e}")
'''
    
    cmd = [PYTHON_EXE, "-c", test_script]
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
        print(result.stdout)
        if result.stderr:
            print(f"警告: {result.stderr}")
        return result.returncode == 0
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def main():
    """主函数"""
    print("=== 使用国内镜像安装PyTorch和依赖 ===")
    
    if not os.path.exists(PYTHON_EXE):
        print(f"❌ Python环境不存在: {PYTHON_EXE}")
        return
    
    # 配置镜像
    configure_pip_mirror()
    
    # 安装NumPy
    install_numpy_compatible()
    
    # 安装PyTorch
    install_torch_gpu()
    
    # 安装其他依赖
    install_essential_packages()
    
    # 测试
    if test_installation():
        print("\n🎉 安装完成！所有依赖都可以正常导入")
    else:
        print("\n⚠️ 安装完成，但部分依赖可能有问题")
    
    print("\n下一步：测试Web_through.py")

if __name__ == "__main__":
    main()
