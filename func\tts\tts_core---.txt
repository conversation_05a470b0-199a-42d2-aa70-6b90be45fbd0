J:\ai\�������\ai-yinmei\func\tts\tts_core.c    tts_core.py     __init__        tts_core.TTsCore.__init__       assistant_tts_say       tts_core.TTsCore.assistant_tts_say      traceid tts_say tts_core.TTsCore.tts_say        type_tts_say    tts_core.TTsCore.type_tts_say   tts_chat_say    tts_core.TTsCore.tts_chat_say   flag_tts_creating       tts_core.TTsCore.flag_tts_creating      wrapper tts_core.TTsCore.flag_tts_creating.wrapper  func        tts_say_do      tts_core.TTsCore.tts_say_do     get_vists       tts_core.TTsCore.get_vists      get_assistant_vists     tts_core.TTsCore.get_assistant_vists    check_tts_play  tts_core.TTsCore.check_tts_play tts_play        tts_core.TTsCore.tts_play       check_tts       tts_core.TTsCore.check_tts      http_chatreply  tts_core.TTsCore.http_chatreply tts_core.__pyx_scope_struct__flag_tts_creating  tts_core                Interpreter change detected - this module can only be loaded into one interpreter per process.  name    __loader__  loader      __file__    origin      __package__ parent      __path__        submodule_search_locations      Module 'tts_core' has already been imported. Re-initialisation is not supported.        builtins        cython_runtime  __builtins__    init tts_core   %s() got multiple values for keyword argument '%U'      %.200s() keywords must be strings       %s() got an unexpected keyword argument '%U'    at least        at most exactly s       %.200s() takes %.8s %zd positional argument%.1s (%zd given)     name '%U' is not defined         while calling a Python object  NULL result without error in PyObject_Call      local variable '%s' referenced before assignment                free variable '%s' referenced before assignment in enclosing scope      _cython_3_0_11  Shared Cython type %.200s is not a type object  Shared Cython type %.200s has the wrong size, try recompiling   keywords must be strings        __name__ must be set to a string object __qualname__ must be set to a string object     function's dictionary may not be deleted        setting function's dictionary to a non-dict     __defaults__ must be set to a tuple object      changes to cyfunction.__defaults__ will not currently affect the values used in function calls  __kwdefaults__ must be set to a dict object     changes to cyfunction.__kwdefaults__ will not currently affect the values used in function calls        __annotations__ must be set to a dict object    func_doc        __doc__ func_name       __name__        __qualname__    func_dict       __dict__        func_globals    __globals__     func_closure    __closure__     func_code       __code__        func_defaults   __defaults__    __kwdefaults__  __annotations__ _is_coroutine   __module__      __reduce__      Bad call flags for CyFunction   <cyfunction %U at %p>   %.200s() takes no arguments (%zd given) %.200s() takes exactly one argument (%zd given) %.200s() takes no keyword arguments     unbound method %.200S() needs an argument       %.200s() needs an argument      _cython_3_0_11.cython_function_or_method        join() result is too long for a Python string   '%.200s' object has no attribute '%U'   cannot fit '%.200s' into an index-sized integer '%.200s' object is not subscriptable    %.200s object is not an iterator        base class '%.200s' is not a heap type          extension type '%.200s' has no __dict__ slot, but base type '%.200s' has: either add 'cdef dict __dict__' to the extension type or add '__slots__ = [...]' to the base type     cannot import name %S   %s (%s:%d)      does not match          compile time Python version %d.%d of module '%.100s' %s runtime version %d.%d   TTsCore.flag_tts_creating.<locals>.wrapper  0   ]音频序列：        tts_play_thread func.tts.player J:\ai\吟美打包\ai-yinmei\func\tts\tts_core.py   say info    other   first_value     exception       .*英(文|语).*        actionOper  jp  replyText   mood        emotevideo_play broadcast       default bert-vists      cline_in_traceback      LLmData enable  shell   threading       func.tools.singleton_mode       SingData        type_tts_say    (&�   release AnswerList      CommonWebsocket __prepare__     TTsData TranslateCore   tts_say_do      check_tts_play  kwargs  emoteOper       asyncio tts_chat_say    *   player      
        说话类型
        :param text:
        :param voiceType: chat：聊天语音  force：强制语音  other：其他语音
        :return:
          【type_tts_say】发生了异常：    func.gobal.data jsonData        ]输出表情   emoteContent    http_chatreply  max_workers     __main__        pattern assistant_select_vists  created ]成功生成音频序列：    __qualname__    creating        func.tools.common_websocket     __module__      "完成 ","content": "  select_vists    self    commonWebsocket BertVis2        __dict__        �%�   _initializing   sub "}) mpvPlay         func.vtuber.action_oper func.tts.gtp_vists  ,   Ai_Name traceStr        emote_show      TTsCore.flag_tts_creating       TTsCore.check_tts_play  tts_play    <br/>       tts_core    target      状态提示    del /f .\output\    result  .mp3    switch      auto_swing  zh  _is_coroutine   replyText_json  super   voiceStatus     __spec__    submit      tts_chat_say_pool       wrapper start   "       thread_name_prefix      emote_content   flag_tts_creating   append  uuid4       asyncio.tasks   func.tools.string_util  vists   __import__      ","chatStatus": "       TTsCore.type_tts_say    ThreadPoolExecutor              
        播放声音
        :param json:
        :return:
            filename    log __doc__ __class_getitem__       聊天回复    content '       ObsInit gc      __set_name__    func.obs.obs_init       get_vists       .\output\       ","status": "   is_tts_playing  [   end moodNum values  func.log.default_log    ,SayCount:      acquire replace TTsCore re  type        __name__        is_singing      .*日(文|语).*        func.vtuber.emote_oper  DefaultLog      __init_subclass__   en  mpv.exe ReplyTextList   llmData 【tts_chat_say】发生了异常：    singData    
   .*日(文|语).*说.*   .       assistant_tts_say       语音合成"   func.tts.edge_tts_vits  【assistant_tts_say】发生了异常：       TTsCore.tts_chat_say    get     
        播放声音【定时器】
        :return:
          force   tts播放结束：      .*英(文|语).*说.*   emote_content_json      gpt-sovits  dumps       TTsCore.tts_play        TTsCore.assistant_tts_say       ,is_singing:    data    search  show_text   text        __test__    played  index       tts_index       func.tools.decorator_mode       subprocess      voiceType       TTsCore.tts_say isenabled   json    
   ]当前感情值:   e   func.translate.duckduckgo_translate     EdgeTTs StringUtil      GtpVists        play_index      check_tts       【tts_say】发生了异常： edge-tts        TTsCore.check_tts   put assistant.exe   is_tts_creating ,is_tts_playing:    func        MpvPlay empty   (《|》|（|）)       chatStatus      ttsData Thread  ?       assistant_vists 成功  AutoChange      TTsCore.__init__        trace_tts_list  translate_origin        __metaclass__   uuid    getLogger       TTsCore.http_chatreply  ]失败生成音频序列：    question        SayCount    obs args    lanuage .mp3 1>nul      disable get_assistant_vists     ({"traceid": "  __init__        traceid TTsCore.get_assistant_vists     jsonStr .*(中文|国语).*说.*        asyncio.coroutines      json_str    get_ws      TTsCore.tts_say_do  angry       tts_say inspect assistant_switch        yaotou_thread   ]text:  .*(中文|国语).*     singleton       func.tts.bert_vits2     emote_thread    DuckduckgoTranslate header  失败      concurrent.futures      EmoteOper       emotion status  ActionOper      speech_max_threads  run speech_switch   TTsCore.get_vists       func.translate.translate_core   translateCore   mpv_play        first_insert_lock