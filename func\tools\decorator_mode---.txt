J:\ai\�������\ai-yinmei\func\tools\decorator_mode.c    decorator_mode.py   
switch      decorator_mode.switch   
decorator       decorator_mode.switch.decorator 
wrapper status  func    argnum  default decorator_mode.switch.decorator.wrapper class_switch    decorator_mode.class_switch     
decorator_mode.class_switch.decorator   
new_init        decorator_mode.class_switch.decorator.new_init  original_init   cls     
key_verify      decorator_mode.key_verify       decorator_mode.key_verify.decorator     superTools  data    name        
decorator_mode.key_verify.decorator.wrapper     
decorator_mode.__pyx_scope_struct__switch       
decorator_mode.__pyx_scope_struct_1_decorator   
decorator_mode.__pyx_scope_struct_2_class_switch        
decorator_mode.__pyx_scope_struct_3_decorator   
decorator_mode.__pyx_scope_struct_4_key_verify  
decorator_mode.__pyx_scope_struct_5_decorator   decorator_mode          Interpreter change detected - this module can only be loaded into one interpreter per process.  __loader__  loader      __file__    origin      __package__ parent      __path__        submodule_search_locations              Module 'decorator_mode' has already been imported. Re-initialisation is not supported.  builtins        cython_runtime  __builtins__    init decorator_mode     %s() got multiple values for keyword argument '%U'      %.200s() keywords must be strings       %s() got an unexpected keyword argument '%U'    at least        at most exactly s       %.200s() takes %.8s %zd positional argument%.1s (%zd given)             free variable '%s' referenced before assignment in enclosing scope       while calling a Python object  NULL result without error in PyObject_Call      cannot fit '%.200s' into an index-sized integer '%.200s' object is not subscriptable    _cython_3_0_11  Shared Cython type %.200s is not a type object  Shared Cython type %.200s has the wrong size, try recompiling   keywords must be strings        __name__ must be set to a string object __qualname__ must be set to a string object     function's dictionary may not be deleted        setting function's dictionary to a non-dict     __defaults__ must be set to a tuple object              changes to cyfunction.__defaults__ will not currently affect the values used in function calls  __kwdefaults__ must be set to a dict object     changes to cyfunction.__kwdefaults__ will not currently affect the values used in function calls        __annotations__ must be set to a dict object    func_doc        __doc__ func_name       __name__        __qualname__    func_dict       __dict__        func_globals    __globals__     func_closure    __closure__     func_code       __code__        func_defaults   __defaults__    __kwdefaults__  __annotations__ _is_coroutine   __module__      __reduce__      Bad call flags for CyFunction   <cyfunction %U at %p>   %.200s() takes no arguments (%zd given) %.200s() takes exactly one argument (%zd given) %.200s() takes no keyword arguments     unbound method %.200S() needs an argument       %.200s() needs an argument      _cython_3_0_11.cython_function_or_method        strings are too large to concat name '%U' is not defined        cannot import name %S   base class '%.200s' is not a heap type          extension type '%.200s' has no __dict__ slot, but base type '%.200s' has: either add 'cdef dict __dict__' to the extension type or add '__slots__ = [...]' to the base type     %s (%s:%d)      does not match  compile time Python version %d.%d of module '%.100s' %s runtime version %d.%d   superTools  .   warning key_verify      original_init   default cline_in_traceback  enable      threading       verifyKey       `� �   func.tools.super_tools  kwargs  argnum  __main__    self        (� �   _initializing   new_init        switch.<locals>.decorator   result  switch      _is_coroutine   __spec__        wrapper ?       __import__  log __class_getitem__       key_verify.<locals>.decorator   gc  status      func.log.default_log    SuperTools      __name__    cls DefaultLog       Class initialization is disabled by the switch.                
    开关切面
    :param status: 开关状态值
    :param default: 如果关闭，返回默认值
    :param argnum: 如果关闭，返回默认传入参数
    :return:
        data    __test__        key_verify.<locals>.decorator.<locals>.wrapper  isenabled       decorator       decorator_mode  switch.<locals>.decorator.<locals>.wrapper  func    *   isVerify        getLogger       class_switch    args    class_switch.<locals>.decorator.<locals>.new_init       disable __init__        秘钥失效    asyncio.coroutines  name        class_switch.<locals>.decorator J:\ai\吟美打包\ai-yinmei\func\tools\decorator_mode.py