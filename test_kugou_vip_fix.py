#coding=UTF-8
"""
测试酷狗VIP修复和排序优化
"""
import sys
sys.path.append('.')

from kugou import Kugou_music
import json

def test_search_with_url_check():
    """测试搜索结果的URL可用性检查和排序"""
    print("=" * 60)
    print("测试搜索结果URL可用性检查和排序")
    print("=" * 60)
    
    try:
        kugou = Kugou_music()
        
        test_cases = [
            "稻香",
            "周杰伦 - 稻香",
            "轻舟",
            "张杰 - 轻舟",
        ]
        
        for i, query in enumerate(test_cases, 1):
            print(f"\n[{i}] 测试搜索: {query}")
            print("-" * 40)
            
            try:
                # 获取完整搜索结果
                search_results = kugou.search_music(query, limit=5, return_list=True)
                
                if search_results:
                    print(f"找到 {len(search_results)} 个结果:")
                    for j, result in enumerate(search_results, 1):
                        print(f"  {j}. {result['artist_name']} - {result['song_name']}")
                        print(f"     匹配度: {result['match_score']}, URL分数: {result['url_score']}")
                        print(f"     有BackupUrl: {result['has_backup_url']}, 综合分数: {result['total_score']}")
                        print()
                    
                    # 测试单个结果搜索
                    hash_val, song_name, artist_name = kugou.search_music(query)
                    if hash_val != 0:
                        print(f"最佳匹配: {artist_name} - {song_name}")
                        print(f"Hash: {hash_val}")
                    else:
                        print("❌ 单个结果搜索失败")
                else:
                    print("❌ 未找到搜索结果")
                    
            except Exception as e:
                print(f"❌ 搜索异常: {e}")
                
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()

def test_url_availability_check():
    """测试URL可用性检查功能"""
    print("\n" + "=" * 60)
    print("测试URL可用性检查功能")
    print("=" * 60)
    
    try:
        kugou = Kugou_music()
        
        # 先搜索一些歌曲获取hash
        search_results = kugou.search_music("稻香", limit=3, return_list=True)
        
        if search_results:
            print("测试URL可用性检查:")
            for i, result in enumerate(search_results, 1):
                hash_val = result['file_hash']
                print(f"\n[{i}] 检查: {result['artist_name']} - {result['song_name']}")
                print(f"    Hash: {hash_val}")
                
                # 手动检查URL可用性
                url_score, has_backup = kugou._check_url_availability(hash_val)
                print(f"    URL分数: {url_score}, 有BackupUrl: {has_backup}")
                
                # 实际获取URL信息
                try:
                    import requests
                    response = requests.get(f"http://localhost:3001/song/url?hash={hash_val}", timeout=5)
                    if response.status_code == 200:
                        data = response.json()
                        print(f"    API响应状态: 成功")
                        print(f"    有backupUrl: {'backupUrl' in data and data['backupUrl']}")
                        print(f"    有url: {'url' in data and data['url']}")
                        print(f"    有data.url: {'data' in data and data.get('data') and 'url' in data['data']}")
                    else:
                        print(f"    API响应状态: {response.status_code}")
                except Exception as e:
                    print(f"    API请求异常: {e}")
        else:
            print("❌ 未找到搜索结果用于测试")
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()

def test_search_and_download():
    """测试搜索和下载组合功能"""
    print("\n" + "=" * 60)
    print("测试搜索和下载组合功能")
    print("=" * 60)
    
    try:
        kugou = Kugou_music()
        
        test_songs = [
            "稻香",  # 简单测试
        ]
        
        for i, song in enumerate(test_songs, 1):
            print(f"\n[{i}] 测试歌曲: {song}")
            print("-" * 40)
            
            try:
                # 使用新的搜索和下载方法
                downloaded_name, file_path = kugou.search_and_download_music(song)
                print(f"✅ 下载成功:")
                print(f"   歌曲名: {downloaded_name}")
                print(f"   文件路径: {file_path}")
                
                # 检查文件是否存在
                import os
                if os.path.exists(file_path):
                    file_size = os.path.getsize(file_path)
                    print(f"   文件大小: {file_size / 1024 / 1024:.2f} MB")
                else:
                    print(f"   ❌ 文件不存在: {file_path}")
                    
            except Exception as e:
                print(f"❌ 下载失败: {e}")
                
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主测试函数"""
    print("🎵 酷狗VIP修复和排序优化测试")
    print("测试时间:", __import__('datetime').datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
    
    # 测试搜索结果排序
    test_search_with_url_check()
    
    # 测试URL可用性检查
    test_url_availability_check()
    
    # 测试搜索和下载
    test_search_and_download()
    
    print("\n" + "=" * 60)
    print("测试完成")
    print("=" * 60)
    print("\n💡 修复总结:")
    print("1. ✅ 优先排序有backupUrl的歌曲")
    print("2. ✅ 避免重复搜索，直接使用搜索结果")
    print("3. ✅ 确保下载的歌曲名与实际匹配")
    print("4. ✅ 增强URL可用性检查")

if __name__ == "__main__":
    main()
