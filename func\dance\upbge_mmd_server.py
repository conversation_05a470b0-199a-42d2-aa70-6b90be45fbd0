#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
UPBGE MMD舞蹈服务端
在UPBGE游戏引擎中运行的WebSocket服务端
处理客户端的舞蹈查询和执行请求

修复记录：
- 修复了BGE多线程访问问题："Blender Game Engine data has been freed"
- 所有BGE对象访问现在都通过 _execute_bge_operation 安全包装器执行
- 添加了对BGE对象名称的安全获取机制
- 增强了错误处理和重试机制
"""

import bge
import asyncio
import websockets
import json
import os
import sys
import threading
import time
from pathlib import Path
from typing import Dict, List, Optional

# 导入新的模块
try:
    from server_port_manager import PortManager
    from server_diagnostics import ServerDiagnostics
    from server_utils import run_test_client, check_server_port, restart_server, run_comprehensive_test
except ImportError as e:
    print(f"⚠️ 导入服务器模块失败: {e}")
    PortManager = None
    ServerDiagnostics = None

# 导入MMD管理器（需要确保路径正确）
try:
    from enhanced_mmd_manager_v2 import EnhancedMMDManagerV2
    from media_property_analyzer import MediaPropertyAnalyzer
    from mmd_loader import UPBGEMMDLoader  # 我们稍后创建这个
except ImportError as e:
    print(f"导入错误: {e}")
    print("请确保所有MMD管理器模块在Python路径中")
    EnhancedMMDManagerV2 = None
    MediaPropertyAnalyzer = None
    UPBGEMMDLoader = None

class UPBGEMMDServer:
    """UPBGE中的MMD WebSocket服务端"""
    
    def __init__(self, host=None, port=None, mmd_data_path=None):
        # 使用配置系统
        try:
            from mmd_config import get_config
            self.config = get_config()
            
            # 获取服务器配置
            server_config = self.config.get_server_config()
            self.host = host or server_config["host"]
            self.port = port or server_config["port"]
            
            # 如果用户提供了 mmd_data_path，检查是否需要更新配置
            # 但优先使用配置文件中的路径（如果存在且有效）
            current_config_path = self.config.get_mmd_data_root()
            
            if mmd_data_path and (not current_config_path or not Path(current_config_path).exists()):
                # 只有在配置路径不存在或无效时，才使用传入的路径
                print(f"💡 配置路径无效，使用传入路径: {mmd_data_path}")
                self.config.set_mmd_data_root(mmd_data_path)
            elif current_config_path and Path(current_config_path).exists():
                # 配置路径有效，使用配置路径
                print(f"✅ 使用配置文件中的有效路径: {current_config_path}")
            
            self.mmd_data_path = self.config.get_mmd_data_root()
            
        except ImportError:
            # 如果没有配置系统，使用默认值
            print(f"🔧 没有配置系统，使用默认值---")
            self.host = host or "localhost"
            self.port = port or 8765
            self.mmd_data_path = Path(mmd_data_path) if mmd_data_path else Path(".")
        
        # 初始化端口管理器
        if PortManager:
            self.port_manager = PortManager(self.host)
        else:
            self.port_manager = None
        
        # 初始化诊断器
        if ServerDiagnostics:
            self.diagnostics = ServerDiagnostics(self)
        else:
            self.diagnostics = None
        
        # 初始化MMD管理器
        try:
            if EnhancedMMDManagerV2 is None:
                raise ImportError("EnhancedMMDManagerV2 未能导入")
            # 不传递base_path参数，让管理器使用配置文件中的路径
            self.mmd_manager = EnhancedMMDManagerV2()
            # 在初始化后更新服务器的数据路径为管理器实际使用的路径
            # if hasattr(self.mmd_manager, 'organizer') and hasattr(self.mmd_manager.organizer, 'base_path'):
            #     self.mmd_data_path = self.mmd_manager.organizer.base_path
            print(f"self.mmd_manager.organizer.base_path----: {self.mmd_manager.organizer.base_path}")
            print(f"✅ MMD管理器已初始化，实际数据路径: {self.mmd_data_path}")
        except Exception as e:
            print(f"⚠️ MMD管理器初始化失败: {e}")
            self.mmd_manager = None
        
        try:
            if MediaPropertyAnalyzer is None:
                raise ImportError("MediaPropertyAnalyzer 未能导入")
            self.media_analyzer = MediaPropertyAnalyzer()
        except Exception as e:
            print(f"⚠️ 媒体分析器初始化失败: {e}")
            self.media_analyzer = None
        
        # 不再直接使用UPBGEMMDLoader，改为通过MMD组件控制
        self.mmd_loader = None
        
        # 服务端状态
        self.server = None
        self.clients = set()
        self.is_running = False
        self.loop = None
        self.server_thread = None
        
        # 端口状态文件管理
        self.port_file = Path("mmd_server_port.txt")  # 存储实际使用的端口
        
        # MMD播放状态
        self.current_dance = None
        self.current_model = None
        self.playback_status = "停止"
        self.dance_start_time = 0
        
        # MMD组件管理
        self.active_mmd_components = []  # 当前激活的MMD组件列表
        self.default_component = None    # 默认使用的MMD组件
        
        # BGE生命周期管理
        self.game_session_id = None      # 当前游戏会话ID
        self.is_game_active = True       # 游戏是否激活
        self.last_component_check = 0    # 上次组件检查时间
        self.component_check_interval = 2.0  # 组件检查间隔（秒）
        
        print("🎭 UPBGE MMD服务端初始化完成")
        
        # 调试信息
        if self.diagnostics:
            self.diagnostics.print_debug_info()
        else:
            self.print_debug_info()
    
    def print_debug_info(self):
        """打印调试信息（简化版本，用于模块未加载时）"""
        # print(f"\n🔧 UPBGE MMD服务器调试信息:")
        # print(f"   - 数据路径: {self.mmd_data_path}")
        # print(f"   - 当前工作目录: {Path.cwd()}")
        
        if self.mmd_manager and self.mmd_manager.dance_index:
            dance_count = len(self.mmd_manager.dance_index)
            print(f"   - 舞蹈索引数量: {dance_count}")
        else:
            print(f"   - 舞蹈索引: 未加载")
        print()
    
    def start_server_threaded(self):
        """在独立线程中启动WebSocket服务端"""
        try:
            # 创建新的事件循环
            self.loop = asyncio.new_event_loop()
            asyncio.set_event_loop(self.loop)
            
            # 在事件循环中运行服务器
            self.loop.run_until_complete(self._start_server_async())
            
        except Exception as e:
            print(f"❌ 服务端启动失败: {e}")

    async def _start_server_async(self):
        """异步启动WebSocket服务端"""
        original_port = self.port  # 保存原始端口
        max_retries = 3
        retry_count = 0
        
        while retry_count < max_retries:
            try:
                # 改进的端口检查和处理
                if self.port_manager and not self.port_manager.is_port_available(self.port):
                    print(f"⚠️ 端口 {self.port} 被占用")
                    
                    # 检查是否是旧的MMD服务器进程
                    if retry_count == 0:
                        if self.port_manager:
                            status = self.port_manager.check_port_status(self.port)
                            
                            # 如果是旧的MMD服务器或Python进程，尝试强制清理
                            if status and any(keyword in str(status).lower() for keyword in ['python', 'upbge', 'blender']):
                                print("🔧 检测到可能的旧MMD服务器进程，尝试清理...")
                                if hasattr(self.port_manager, 'force_free_port'):
                                    try:
                                        cleared = self.port_manager.force_free_port(self.port)
                                        if cleared:
                                            print(f"✅ 已清理端口 {self.port}")
                                            # 等待端口释放
                                            await asyncio.sleep(2)
                                            # 重新检查端口
                                            if self.port_manager.is_port_available(self.port):
                                                print(f"✅ 端口 {self.port} 现在可用")
                                                continue  # 重新尝试启动
                                    except Exception as e:
                                        print(f"⚠️ 清理端口失败: {e}")
                        
                        print("⚠️ 将尝试使用其他可用端口...")
                    
                    # 查找可用端口（优先使用接近原始端口的）
                    try:
                        if self.port_manager:
                            # 先尝试原始端口附近的端口
                            for offset in range(1, 10):
                                test_port = original_port + offset
                                if self.port_manager.is_port_available(test_port):
                                    print(f"✅ 找到可用端口: {test_port}")
                                    self.port = test_port
                                    break
                            else:
                                # 如果都不可用，使用端口管理器的安全查找
                                new_port = self.port_manager.find_available_port_safe(self.port)
                                if new_port and new_port != self.port:
                                    print(f"✅ 找到可用端口: {new_port}")
                                    self.port = new_port
                                else:
                                    raise Exception("无法找到可用端口")
                        else:
                            # 没有端口管理器时的简单处理
                            self.port = original_port + retry_count + 1
                            print(f"尝试端口: {self.port}")
                    except Exception as e:
                        print(f"❌ 查找可用端口失败: {e}")
                        retry_count += 1
                        if retry_count < max_retries:
                            self.port = original_port + retry_count  # 简单递增尝试
                            print(f"尝试端口: {self.port}")
                        continue
                
                # 加载MMD数据索引和发现MMD组件
                try:
                    await self.load_mmd_data()
                except Exception as e:
                    print(f"⚠️ 加载MMD数据失败: {e}")
                    # 不阻止服务器启动，继续运行
                
                # 启动WebSocket服务器
                # 创建一个兼容的处理器包装函数
                async def client_handler(websocket):
                    """WebSocket客户端处理器包装函数"""
                    try:
                        await self.handle_client(websocket, getattr(websocket, 'path', '/'))
                    except Exception as e:
                        print(f"❌ 客户端处理错误: {e}")
                
                print(f"🚀 尝试启动服务器在端口 {self.port}...")
                # 🔥 修复：禁用服务端超时限制，增加连接稳定性
                self.server = await websockets.serve(
                    client_handler, 
                    self.host, 
                    self.port,
                    ping_interval=None,    # 禁用服务端自动ping
                    ping_timeout=None,     # 禁用ping超时
                    close_timeout=10,      # 关闭超时10秒
                    max_size=10**7,        # 增加最大消息大小
                    max_queue=100,         # 增加消息队列大小
                    compression=None       # 禁用压缩以提高性能
                )
                self.is_running = True
                
                print(f"✅ MMD服务端已启动: ws://{self.host}:{self.port}")
                if self.port != original_port:
                    print(f"💡 注意: 端口已从 {original_port} 更改为 {self.port}")
                
                # 保存实际使用的端口到文件，供客户端发现
                try:
                    self.port_file.write_text(f"{self.host}:{self.port}")
                    print(f"💾 端口信息已保存到 {self.port_file}")
                except Exception as e:
                    print(f"⚠️ 保存端口信息失败: {e}")
                
                print("等待客户端连接...")
                print(f"🔧 调试信息:")
                print(f"   - MMD管理器: {'正常' if self.mmd_manager else '未初始化'}")
                print(f"   - 媒体分析器: {'正常' if self.media_analyzer else '未初始化'}")
                print(f"   - MMD加载器: {'正常' if self.mmd_loader else '未初始化'}")
                if self.mmd_manager and self.mmd_manager.dance_index:
                    print(f"   - 舞蹈数量: {len(self.mmd_manager.dance_index)}个")
                
                # 保持服务器运行
                await self.server.wait_closed()
                break  # 成功启动，退出重试循环
                
            except Exception as e:
                retry_count += 1
                print(f"❌ 服务端启动失败 (尝试 {retry_count}/{max_retries}): {e}")
                
                # 重置端口以避免累积错误
                self.port = original_port + retry_count
                
                if retry_count < max_retries:
                    wait_time = min(retry_count * 2, 5)  # 最多等待5秒
                    print(f"等待 {wait_time} 秒后重试...")
                    await asyncio.sleep(wait_time)
                else:
                    print("❌ 已达到最大重试次数，服务端启动失败")
                    print(f"💡 建议:")
                    print(f"   1. 检查端口占用")
                    print(f"   2. 手动指定端口: server.port = {original_port + 10}")
                    print(f"   3. 重启UPBGE并重试")
                    self.is_running = False
    
    async def load_mmd_data(self):
        """加载MMD数据索引和发现MMD组件"""
        print("正在加载MMD数据索引...")
        try:
            if self.mmd_manager is None:
                print("⚠️ MMD管理器未初始化，跳过数据加载")
                return
            
            self.mmd_manager.load_index()
            dance_count = len(self.mmd_manager.dance_index) if self.mmd_manager.dance_index else 0
            print(f"✅ 已加载 {dance_count} 个舞蹈")
            
            # 发现场景中的MMD组件
            await self.discover_mmd_components()
            
        except Exception as e:
            print(f"⚠️ 加载MMD数据时出错: {e}")
    
    async def discover_mmd_components(self):
        """发现并注册场景中的MMD组件"""
        try:
            # 使用安全的BGE操作包装器
            found_components = await self._execute_bge_operation(self._discover_components_sync)
            
            if found_components is None:
                found_components = []
            
            self.active_mmd_components = found_components
            
            # 设置默认组件
            if self.active_mmd_components:
                self.default_component = self.active_mmd_components[0]
                # 安全获取组件名称
                component_name = "未知组件"
                try:
                    if hasattr(self.default_component, 'object') and hasattr(self.default_component.object, 'name'):
                        component_name = str(self.default_component.object.name)
                except:
                    pass
                print(f"✅ 设置默认MMD组件: {component_name}")
            else:
                print("⚠️ 未发现任何MMD组件")
                
        except Exception as e:
            print(f"❌ 发现MMD组件失败: {e}")
    
    def _discover_components_sync(self):
        """同步发现组件（在主线程中执行）"""
        try:
            scene = bge.logic.getCurrentScene()
            found_components = []
            
            # 查找所有注册的MMD组件
            if hasattr(scene, 'mmd_components') and scene['mmd_components']:
                found_components = scene['mmd_components']
                print(f"🔍 发现 {len(found_components)} 个MMD组件")
            else:
                # 如果没有预注册的组件，尝试搜索场景中的对象
                print("🔍 搜索场景中的MMD组件...")
                for obj in scene.objects:
                    if hasattr(obj, 'components'):
                        for component in obj.components:
                            if hasattr(component, '__class__') and 'MMDAnimationComponent' in str(component.__class__):
                                found_components.append(component)
                                # 安全获取对象名称
                                obj_name = "未知对象"
                                try:
                                    obj_name = str(obj.name)
                                except:
                                    pass
                                print(f"   发现组件: {obj_name}")
            
            return found_components
            
        except Exception as e:
            print(f"❌ 同步发现组件失败: {e}")
            return []
    
    def get_target_component(self, model_name=None):
        """获取目标MMD组件"""
        if not self.active_mmd_components:
            return None
        
        # 如果指定了模型名称，查找对应的组件
        if model_name:
            for component in self.active_mmd_components:
                if component.object.name == model_name:
                    return component
        
        # 返回默认组件
        return self.default_component
    
    def run_async_task(self, coro):
        """在服务器的事件循环中运行异步任务"""
        if self.loop and self.loop.is_running():
            # 使用线程安全的方式调度协程
            future = asyncio.run_coroutine_threadsafe(coro, self.loop)
            return future
        return None

    def execute_dance_sync(self, dance_name: str, options: Dict = None) -> Dict:
        """同步方式执行舞蹈（从BGE主线程调用）"""
        if options is None:
            options = {}
        
        data = {"dance_name": dance_name, "options": options}
        future = self.run_async_task(self.execute_dance(data))
        
        if future:
            try:
                # 等待异步任务完成（最多等待5秒）
                result = future.result(timeout=5)
                return result
            except Exception as e:
                return {"error": f"执行舞蹈失败: {e}"}
        else:
            return {"error": "服务器未运行"}

    def stop_dance_sync(self) -> Dict:
        """同步方式停止舞蹈（从BGE主线程调用）"""
        future = self.run_async_task(self.stop_dance())
        
        if future:
            try:
                result = future.result(timeout=3)
                return result
            except Exception as e:
                return {"error": f"停止舞蹈失败: {e}"}
        else:
            return {"error": "服务器未运行"}

    def get_status_sync(self) -> Dict:
        """同步方式获取状态（从BGE主线程调用）"""
        future = self.run_async_task(self.get_status())
        
        if future:
            try:
                result = future.result(timeout=1)
                return result
            except Exception as e:
                return {"error": f"获取状态失败: {e}"}
        else:
            return {"status": {"server_status": "未运行"}}
    
    async def handle_client(self, websocket, path):
        """处理客户端连接"""
        client_addr = websocket.remote_address
        print(f"🔗 客户端连接: {client_addr} (路径: {path})")
        
        self.clients.add(websocket)
        
        try:
            async for message in websocket:
                await self.process_message(websocket, message)
        except websockets.exceptions.ConnectionClosed:
            print(f"🔌 客户端断开: {client_addr}")
        except Exception as e:
            print(f"❌ 处理客户端消息时出错: {e}")
        finally:
            self.clients.discard(websocket)
    
    async def process_message(self, websocket, message):
        """处理客户端消息"""
        try:
            data = json.loads(message)
            request_type = data.get("type")
            request_data = data.get("data", {})
            
            response = await self.handle_request(request_type, request_data)
            
            await websocket.send(json.dumps(response, ensure_ascii=False))
            
        except Exception as e:
            error_response = {"error": f"处理请求时出错: {e}"}
            await websocket.send(json.dumps(error_response, ensure_ascii=False))
    
    async def handle_request(self, request_type: str, data: Dict) -> Dict:
        """处理具体请求"""
        try:
            if request_type == "search_dances":
                return await self.search_dances(data)
            elif request_type == "get_dance_details":
                return await self.get_dance_details(data)
            elif request_type == "execute_dance":
                return await self.execute_dance(data)
            elif request_type == "stop_dance":
                return await self.stop_dance()
            elif request_type == "pause_dance":
                return await self.pause_dance()
            elif request_type == "resume_dance":
                return await self.resume_dance()
            elif request_type == "restart_dance":
                return await self.restart_dance()
            elif request_type == "get_status":
                return await self.get_status()
            elif request_type == "list_models":
                return await self.list_models()
            elif request_type == "switch_model":
                return await self.switch_model(data)
            elif request_type == "seek_to_frame":
                return await self.seek_to_frame(data)
            elif request_type == "set_speed":
                return await self.set_speed(data)
            elif request_type == "set_loop":
                return await self.set_loop(data)
            elif request_type == "get_component_status":
                return await self.get_component_status(data)
            elif request_type == "quick_load_and_play":
                return await self.quick_load_and_play(data)
            else:
                return {"error": f"未知请求类型: {request_type}"}
        
        except Exception as e:
            return {"error": f"处理请求失败: {e}"}
    
    async def search_dances(self, data: Dict) -> Dict:
        """搜索舞蹈"""
        query = data.get("query", "")
        search_type = data.get("search_type", "both")
        
        if not query:
            return {"error": "搜索关键词不能为空"}
        
        if self.mmd_manager is None:
            return {"error": "MMD管理器未初始化"}
        
        try:
            # 使用MMD管理器搜索
            results = self.mmd_manager.search_dances(query, search_type)
            
            # 格式化结果
            dances = []
            for result in results:
                dance_info = self.mmd_manager.dance_index.get(result["name"], {})
                dances.append({
                    "name": result["name"],
                    "translation": result.get("translation"),
                    "score": result.get("score", 0),
                    "has_complete_set": dance_info.get("file_statistics", {}).get("has_complete_set", False)
                })
            
            return {"dances": dances}
            
        except Exception as e:
            return {"error": f"搜索失败: {e}"}
    
    async def get_dance_details(self, data: Dict) -> Dict:
        """获取舞蹈详细信息"""
        dance_name = data.get("dance_name", "")
        
        if not dance_name:
            return {"error": "舞蹈名称不能为空"}
        
        if self.mmd_manager is None:
            return {"error": "MMD管理器未初始化"}
        
        try:
            dance_info = self.mmd_manager.dance_index.get(dance_name)
            if not dance_info:
                return {"error": f"未找到舞蹈: {dance_name}"}
            
            # 获取媒体分析信息
            if self.media_analyzer is not None:
                try:
                    media_analysis = self.media_analyzer.analyze_dance_media(dance_info)
                    dance_info["media_analysis"] = media_analysis
                except Exception as e:
                    print(f"媒体分析失败: {e}")
                    pass  # 媒体分析失败不影响基本信息
            
            dance_info["name"] = dance_name
            return {"dance_info": dance_info}
            
        except Exception as e:
            return {"error": f"获取舞蹈详情失败: {e}"}
    
    async def execute_dance(self, data: Dict) -> Dict:
        """执行舞蹈 - 通过MMD组件（修复版 - 避免异步冲突）"""
        dance_name = data.get("dance_name", "")
        options = data.get("options", {})
        model_name = data.get("model_name", None)  # 可选：指定模型
        
        if not dance_name:
            return {"error": "舞蹈名称不能为空"}
        
        try:
            # 🔥 修复1：增强的舞蹈信息验证
            print(f"🎭 开始执行舞蹈: {dance_name}")
            
            if not self.mmd_manager or not self.mmd_manager.dance_index:
                return {"error": "MMD管理器或舞蹈索引未初始化"}
            
            dance_info = self.mmd_manager.dance_index.get(dance_name)
            if not dance_info:
                return {"error": f"未找到舞蹈: {dance_name}"}
            
            # 检查是否有必要文件
            main_files = dance_info.get("main_files", {})
            motion_file = main_files.get("motion")
            
            if not motion_file:
                return {"error": f"舞蹈 {dance_name} 缺少动作文件"}
            
            # 🔥 修复2：改进组件获取和验证
            print(f"🔍 查找目标MMD组件...")
            target_component = self.get_target_component(model_name)
            if not target_component:
                # 尝试重新发现组件
                print("⚠️ 未找到MMD组件，尝试重新发现...")
                try:
                    await self.discover_mmd_components()
                    target_component = self.get_target_component(model_name)
                    if not target_component:
                        return {"error": "未找到可用的MMD组件"}
                except Exception as e:
                    return {"error": f"重新发现组件失败: {e}"}
            
            # 获取组件名称（用于日志）
            component_name = "未知组件"
            try:
                if hasattr(target_component, 'object') and hasattr(target_component.object, 'name'):
                    component_name = str(target_component.object.name)
            except:
                pass
            print(f"✅ 目标组件: {component_name}")
            
            # 🔥 修复3：异步操作加保护 - 先停止当前播放
            print(f"⏹️ 停止当前播放...")
            try:
                # 使用更安全的停止方法，避免异步冲突
                stop_result = await asyncio.wait_for(
                    self.stop_current_dance(),
                    timeout=3.0  # 3秒超时
                )
                print(f"✅ 当前播放已停止")
            except asyncio.TimeoutError:
                print(f"⚠️ 停止播放超时，继续执行")
            except asyncio.CancelledError:
                print(f"⚠️ 停止播放被取消，继续执行")
            except Exception as e:
                print(f"⚠️ 停止播放时出错: {e}")
                # 不要因为停止失败而中断，继续执行
            
            # 等待一小段时间确保停止完成
            await asyncio.sleep(0.1)
            
            # 解析文件路径
            print(f"📁 解析文件路径...")
            motion_path = self._resolve_file_path(motion_file["path"])
            audio_path = None
            
            if options.get("play_audio", True):
                audio_file = main_files.get("audio")
                if audio_file:
                    audio_path = self._resolve_file_path(audio_file["path"])
                    print(f"🔊 音频文件解析:")
                    print(f"   原始路径: {audio_file['path']}")
                    print(f"   解析后路径: {audio_path}")
                    print(f"   文件存在: {audio_path.exists() if audio_path else False}")
                else:
                    print(f"⚠️ 舞蹈 {dance_name} 没有音频文件")
            else:
                print(f"🔇 音频播放已禁用")
            
            # 检查文件是否存在
            if not motion_path.exists():
                return {"error": f"VMD文件不存在: {motion_path}"}
            
            print(f"📂 文件检查:")
            print(f"   VMD文件: {motion_path} ({'存在' if motion_path.exists() else '不存在'})")
            if audio_path:
                print(f"   音频文件: {audio_path} ({'存在' if audio_path.exists() else '不存在'})")
            
            # 🔥 修复4：异步操作加保护 - 配置并播放组件
            print(f"🎛️ 配置并播放组件...")
            try:
                # 使用超时保护，避免长时间阻塞
                success = await asyncio.wait_for(
                    self.configure_and_play_component(
                        target_component, 
                        str(motion_path), 
                        str(audio_path) if audio_path and audio_path.exists() else None,
                        options
                    ),
                    timeout=10.0  # 10秒超时
                )
            except asyncio.TimeoutError:
                return {"error": "配置组件超时，请重试"}
            except asyncio.CancelledError:
                return {"error": "配置组件被取消，请重试"}
            except Exception as e:
                return {"error": f"配置组件时出错: {e}"}
            
            if success:
                # 更新服务器状态
                self.current_dance = dance_name
                self.current_model = component_name
                self.playback_status = "播放中"
                self.dance_start_time = bge.logic.getRealTime()
                
                # 估算时长
                estimated_duration = self.estimate_dance_duration(dance_info)
                
                print(f"🎉 舞蹈播放启动成功")
                
                return {
                    "success": True,
                    "message": f"开始播放舞蹈: {dance_name}",
                    "model": self.current_model,
                    "estimated_duration": estimated_duration,
                    "component": component_name
                }
            else:
                return {"error": "舞蹈加载失败1"}
                
        except Exception as e:
            print(f"❌ 执行舞蹈失败: {e}")
            import traceback
            traceback.print_exc()
            return {"error": f"执行舞蹈失败: {e}"}
    
    async def configure_and_play_component(self, component, vmd_path, audio_path, options):
        """配置并播放MMD组件"""
        try:
            # 安全获取组件名称，避免BGE对象访问错误
            component_name = "未知组件"
            try:
                if hasattr(component, 'object') and hasattr(component.object, 'name'):
                    component_name = str(component.object.name)  # 立即转换为字符串
            except:
                pass  # 忽略BGE对象访问错误
            
            print(f"🎭 配置MMD组件: {component_name}")
            print(f"   VMD文件: {vmd_path}")
            print(f"   音频文件: {audio_path or '无'}")
            
            # 将BGE操作调度到主线程执行
            success = await self._execute_bge_operation(
                self._configure_component_sync, 
                component, vmd_path, audio_path, options
            )
            
            if success:
                print(f"✅ MMD组件播放已启动")
                return True
            else:
                print(f"❌ MMD组件播放启动失败")
                return False
                
        except Exception as e:
            print(f"❌ 配置MMD组件失败: {e}")
            return False
    
    def _configure_component_sync(self, component, vmd_path, audio_path, options):
        """同步配置组件（在主线程中执行）- 修复版"""
        try:
            print(f"🎭 配置MMD组件开始...")
            
            # 🔥 修复1：先完全停止当前播放，包括清理状态
            print("⏹️ 停止当前播放...")
            try:
                # 确保组件完全停止
                component.stop_playback()
                
                # 额外的状态清理
                if hasattr(component, 'core') and hasattr(component.core, 'clear_animation_state'):
                    component.core.clear_animation_state()
                    print("   已清理动画状态")
                    
                # 等待一小段时间确保停止完成
                import time
                time.sleep(0.05)
                
            except Exception as e:
                print(f"⚠️ 停止播放时出错: {e}")
                # 继续执行，不要因为停止失败而中断
            
            # 🔥 修复2：设置文件路径前验证文件存在
            print(f"📁 设置文件路径...")
            print(f"   VMD: {vmd_path}")
            print(f"   音频: {audio_path or '无'}")
            
            # 验证VMD文件
            from pathlib import Path
            vmd_file_path = Path(vmd_path)
            if not vmd_file_path.exists():
                print(f"❌ VMD文件不存在: {vmd_path}")
                return False
            
            # 设置VMD文件
            component.set_vmd_file(vmd_path)
            print("✅ VMD文件路径已设置")
            
            # 🔥 修复3：音频处理优化
            if audio_path:
                audio_file_path = Path(audio_path)
                if not audio_file_path.exists():
                    print(f"⚠️ 音频文件不存在: {audio_path}")
                    audio_path = None
                else:
                    print(f"🔊 设置音频文件: {audio_path}")
                    component.set_audio_file(audio_path)
                    print(f"✅ 音频文件路径已设置")
                    
                    # 🔥 强制重新初始化音频加载
                    print(f"🔧 强制重新初始化音频加载...")
                    try:
                        # 确保组件已初始化
                        if not component.core.is_initialized:
                            print(f"📁 组件未初始化，先初始化动画...")
                            init_result = component.core.initialize_animation()
                            if not init_result:
                                print(f"❌ 动画初始化失败")
                                return False
                        
                        # 强制重新加载音频
                        if hasattr(component.core, 'sync_load_audio') and component.core.loader:
                            # 先清理现有音频状态
                            if hasattr(component.core.loader, 'current_audio_source') and component.core.loader.current_audio_source:
                                component.core.sync_stop_audio()
                                time.sleep(0.05)
                            
                            # 重新加载音频
                            audio_loaded = component.core.sync_load_audio(audio_file_path)
                            if audio_loaded:
                                print(f"✅ 音频重新加载成功")
                            else:
                                print(f"⚠️ 音频重新加载失败，但继续播放动画")
                        else:
                            print(f"⚠️ 无法重新加载音频：缺少必要的方法或加载器")
                            
                    except Exception as e:
                        print(f"⚠️ 强制音频加载失败: {e}")
                        # 不阻止播放，继续执行
            else:
                print("⚠️ 未提供音频文件路径")
            
            # 🔥 修复4：设置播放选项
            speed = options.get("speed", 1.0)
            loop = options.get("loop", False)
            
            print(f"⚙️ 设置播放选项...")
            print(f"   速度: {speed}")
            print(f"   循环: {loop}")
            
            component.set_speed(speed)
            component.set_loop(loop)
            
            # 🔥 修复5：确保组件完全初始化后再开始播放
            print(f"🔧 验证组件初始化状态...")
            if not component.core.is_initialized:
                print(f"📁 组件未初始化，正在初始化...")
                init_result = component.core.initialize_animation()
                if not init_result:
                    print(f"❌ 组件初始化失败")
                    return False
                print(f"✅ 组件初始化成功")
            
            # 🔥 修复6：开始播放（使用修复后的start_playback）
            print(f"▶️ 开始播放...")
            result = component.start_playback()
            
            if not result:
                print(f"❌ 播放启动失败")
                return False
            
            # 🔥 修复7：验证播放状态
            print(f"🔍 验证播放状态...")
            time.sleep(0.1)  # 等待播放稳定
            
            if component.is_component_playing():
                print(f"✅ 播放状态验证成功")
                
                # 验证音频播放状态（如果有音频）
                if audio_path and hasattr(component.core, 'loader') and hasattr(component.core.loader, 'current_audio_source'):
                    audio_source = component.core.loader.current_audio_source
                    if audio_source and "audio_handle" in audio_source:
                        try:
                            handle = audio_source["audio_handle"]
                            if hasattr(handle, 'status'):
                                audio_status = str(handle.status)
                                print(f"🎵 音频播放状态: {audio_status}")
                                if audio_status == 'AUD_STATUS_PLAYING':
                                    print("🎉 音频播放验证成功")
                                else:
                                    print(f"⚠️ 音频播放状态异常: {audio_status}")
                        except Exception as e:
                            print(f"⚠️ 音频状态验证失败: {e}")
                
                return True
            else:
                print(f"❌ 播放状态验证失败")
                return False
            
        except Exception as e:
            print(f"❌ 同步配置组件失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    async def _execute_bge_operation(self, func, *args, **kwargs):
        """在主线程中执行BGE操作的异步包装器"""
        try:
            # 检查游戏是否仍然活跃
            if not await self._check_game_active():
                print("⚠️ 游戏未激活，跳过BGE操作")
                return False
            
            # 如果当前就在主线程，直接执行
            if threading.current_thread() is threading.main_thread():
                return func(*args, **kwargs)
            
            # 否则，使用简单的延迟执行
            # 注意：这是一个简化的实现，真正的生产环境需要更复杂的线程间通信
            result = [None]
            exception = [None]
            
            def execute():
                try:
                    result[0] = func(*args, **kwargs)
                except Exception as e:
                    exception[0] = e
            
            # 简单的重试机制
            for attempt in range(3):
                try:
                    execute()
                    if exception[0]:
                        raise exception[0]
                    return result[0]
                except Exception as e:
                    if "freed" in str(e):
                        # BGE对象被释放，可能是游戏重启了
                        print(f"⚠️ BGE对象已释放，可能游戏已重启 (尝试 {attempt + 1}/3)")
                        if attempt < 2:
                            # 尝试重新发现组件
                            await self._refresh_components()
                            await asyncio.sleep(0.2)
                            continue
                    raise e
            
            return False
            
        except Exception as e:
            print(f"❌ 执行BGE操作失败: {e}")
            return False
    
    async def _check_game_active(self):
        """检查游戏是否仍然活跃"""
        try:
            # 尝试获取当前场景来检查BGE是否可用
            scene = bge.logic.getCurrentScene()
            if scene is None:
                self.is_game_active = False
                return False
            
            # 检查场景ID是否改变（可能是游戏重启了）
            current_session_id = id(scene)
            if self.game_session_id is None:
                self.game_session_id = current_session_id
            elif self.game_session_id != current_session_id:
                print("🔄 检测到游戏重启，重新初始化组件...")
                self.game_session_id = current_session_id
                self.is_game_active = True
                # 清理旧的组件引用
                self.active_mmd_components = []
                self.default_component = None
                # 重新发现组件
                await self._refresh_components()
                return True
            
            self.is_game_active = True
            return True
            
        except Exception as e:
            if "freed" in str(e):
                self.is_game_active = False
                print("⚠️ BGE已释放，游戏可能已关闭")
            return False
    
    async def _refresh_components(self):
        """刷新MMD组件列表"""
        try:
            print("🔄 刷新MMD组件列表...")
            # 清理旧的引用
            self.active_mmd_components = []
            self.default_component = None
            
            # 重新发现组件
            await self.discover_mmd_components()
            
            print(f"✅ 已刷新，发现 {len(self.active_mmd_components)} 个组件")
            
        except Exception as e:
            print(f"❌ 刷新组件失败: {e}")
    
    def _resolve_file_path(self, file_path: str) -> Path:
        """解析文件路径，支持相对路径和绝对路径"""
        path = Path(file_path)
        
        # 如果是绝对路径，直接返回
        if path.is_absolute():
            return path
        
        # 如果是相对路径，优先相对于MMD数据根目录解析
        if hasattr(self, 'mmd_data_path') and self.mmd_data_path:
            data_root = Path(self.mmd_data_path)
            resolved_path = data_root / path
            # print(f"🔍 路径解析调试:")
            # print(f"   原始路径: {file_path}")
            # print(f"   数据根目录: {data_root}")
            # print(f"   组合路径: {resolved_path}")
            # print(f"   文件存在: {resolved_path.exists()}")
            
            # 直接返回相对于数据根目录的路径，不管文件是否存在
            # 这样可以提供正确的错误信息
            return resolved_path
        
        # 如果没有数据根目录配置，尝试相对于当前工作目录解析
        if path.exists():
            return path.resolve()
        
        # 最后返回原始路径
        return path
    
    async def stop_dance(self) -> Dict:
        """停止舞蹈 - 通过MMD组件"""
        try:
            await self.stop_current_dance()
            return {"success": True, "message": "舞蹈已停止"}
        except Exception as e:
            return {"error": f"停止舞蹈失败: {e}"}
    
    async def pause_dance(self) -> Dict:
        """暂停舞蹈 - 通过MMD组件"""
        try:
            # 使用安全的BGE操作包装器暂停所有组件
            result = await self._execute_bge_operation(self._pause_components_sync)
            
            if result:
                self.playback_status = "暂停"
                return {"success": True, "message": "舞蹈已暂停"}
            else:
                return {"error": "没有正在播放的舞蹈可以暂停"}
        except Exception as e:
            return {"error": f"暂停舞蹈失败: {e}"}
    
    async def resume_dance(self) -> Dict:
        """继续播放舞蹈 - 通过MMD组件"""
        try:
            # 使用安全的BGE操作包装器继续播放所有组件
            result = await self._execute_bge_operation(self._resume_components_sync)
            
            if result:
                self.playback_status = "播放中"
                return {"success": True, "message": "舞蹈已继续播放"}
            else:
                return {"error": "没有暂停的舞蹈可以继续"}
        except Exception as e:
            return {"error": f"继续播放失败: {e}"}
    
    async def restart_dance(self) -> Dict:
        """重新开始当前舞蹈"""
        try:
            if not self.current_dance:
                return {"error": "没有当前舞蹈可以重新开始"}
            
            # 重新播放当前舞蹈
            options = {
                "speed": 1.0,  # 使用默认速度
                "loop": False,
                "play_audio": True
            }
            
            return await self.execute_dance({
                "dance_name": self.current_dance,
                "options": options
            })
        except Exception as e:
            return {"error": f"重新开始失败: {e}"}
    
    def _pause_components_sync(self):
        """同步暂停组件（在主线程中执行）"""
        try:
            paused_count = 0
            for component in self.active_mmd_components:
                if component.is_component_playing():
                    # 检查组件是否支持暂停功能
                    if hasattr(component, 'pause_playback'):
                        component.pause_playback()
                        paused_count += 1
                    elif hasattr(component, 'core') and hasattr(component.core, 'pause_playback'):
                        component.core.pause_playback()
                        paused_count += 1
                    else:
                        # 如果组件不支持暂停，记录一下但不阻止操作
                        print(f"⚠️ 组件不支持暂停功能: {component.object.name}")
            
            if paused_count > 0:
                print(f"⏸️ 已暂停 {paused_count} 个组件")
                return True
            else:
                print("⚠️ 没有找到正在播放的组件")
                return False
            
        except Exception as e:
            print(f"❌ 同步暂停组件失败: {e}")
            return False
    
    def _resume_components_sync(self):
        """同步继续播放组件（在主线程中执行）"""
        try:
            resumed_count = 0
            for component in self.active_mmd_components:
                # 检查组件是否支持继续播放功能
                if hasattr(component, 'resume_playback'):
                    try:
                        component.resume_playback()
                        resumed_count += 1
                    except Exception as e:
                        print(f"⚠️ 继续播放组件失败: {e}")
                elif hasattr(component, 'core') and hasattr(component.core, 'resume_playback'):
                    try:
                        component.core.resume_playback()
                        resumed_count += 1
                    except Exception as e:
                        print(f"⚠️ 继续播放组件失败: {e}")
                else:
                    # 如果组件不支持继续播放，尝试重新开始播放
                    try:
                        if hasattr(component, 'start_playback') and component.vmd_file:
                            component.start_playback()
                            resumed_count += 1
                            print(f"⚠️ 组件不支持继续播放，已重新开始: {component.object.name}")
                    except Exception as e:
                        print(f"⚠️ 重新开始播放失败: {e}")
            
            if resumed_count > 0:
                print(f"▶️ 已继续播放 {resumed_count} 个组件")
                return True
            else:
                print("⚠️ 没有找到可以继续播放的组件")
                return False
            
        except Exception as e:
            print(f"❌ 同步继续播放组件失败: {e}")
            return False
    
    async def stop_current_dance(self):
        """停止当前舞蹈"""
        try:
            # 使用安全的BGE操作包装器停止所有组件
            await self._execute_bge_operation(self._stop_components_sync)
            
            self.current_dance = None
            self.current_model = None
            self.playback_status = "停止"
            
        except Exception as e:
            print(f"❌ 停止当前舞蹈失败: {e}")
    
    def _stop_components_sync(self):
        """同步停止组件（在主线程中执行）"""
        try:
            # 停止所有活动的MMD组件
            for component in self.active_mmd_components:
                if component.is_component_playing():
                    component.stop_playback()
                    # 安全获取组件名称
                    component_name = "未知组件"
                    try:
                        if hasattr(component, 'object') and hasattr(component.object, 'name'):
                            component_name = str(component.object.name)
                    except:
                        pass
                    print(f"⏹️ 停止组件: {component_name}")
            return True
            
        except Exception as e:
            print(f"❌ 同步停止组件失败: {e}")
            return False
    
    async def get_status(self) -> Dict:
        """获取服务端状态"""
        dance_count = 0
        if self.mmd_manager and self.mmd_manager.dance_index:
            dance_count = len(self.mmd_manager.dance_index)
        
        # 安全获取组件状态
        component_status = await self._execute_bge_operation(self._get_components_status_sync)
        if component_status is None:
            component_status = []
        
        # 安全检查实际播放状态
        actual_playing = await self._execute_bge_operation(self._check_playing_status_sync)
        if not actual_playing and self.playback_status == "播放中":
            self.playback_status = "停止"
            self.current_dance = None
        
        return {
            "status": {
                "server_status": "运行中" if self.is_running else "停止",
                "current_model": self.current_model or "无",
                "current_dance": self.current_dance or "无",
                "playback_status": self.playback_status,
                "loaded_dances_count": dance_count,
                "connected_clients": len(self.clients),
                "mmd_manager_status": "正常" if self.mmd_manager else "未初始化",
                "media_analyzer_status": "正常" if self.media_analyzer else "未初始化",
                "active_components_count": len(self.active_mmd_components),
                "components": component_status
            }
        }
    
    def _get_components_status_sync(self):
        """同步获取组件状态（在主线程中执行）"""
        try:
            component_status = []
            for component in self.active_mmd_components:
                try:
                    info = component.get_animation_info()
                    component_status.append(info)
                except Exception as e:
                    print(f"获取组件状态失败: {e}")
                    # 添加一个错误状态项
                    component_status.append({
                        "model_name": "错误组件",
                        "is_playing": False,
                        "error": str(e)
                    })
            return component_status
        except Exception as e:
            print(f"❌ 同步获取组件状态失败: {e}")
            return []
    
    def _check_playing_status_sync(self):
        """同步检查播放状态（在主线程中执行）"""
        try:
            return any(comp.is_component_playing() for comp in self.active_mmd_components)
        except Exception as e:
            print(f"❌ 检查播放状态失败: {e}")
            return False
    
    async def list_models(self) -> Dict:
        """列出可用模型（基于MMD组件）"""
        try:
            models = await self._execute_bge_operation(self._list_models_sync)
            if models is None:
                models = []
            return {"models": models}
        except Exception as e:
            return {"error": f"获取模型列表失败: {e}"}
    
    def _list_models_sync(self):
        """同步列出模型（在主线程中执行）"""
        try:
            models = []
            for component in self.active_mmd_components:
                try:
                    # 安全获取组件名称
                    component_name = "未知组件"
                    try:
                        if hasattr(component, 'object') and hasattr(component.object, 'name'):
                            component_name = str(component.object.name)
                    except:
                        pass
                    
                    models.append({
                        "name": component_name,
                        "is_playing": component.is_component_playing(),
                        "current_vmd": component.vmd_file,
                        "playback_mode": component.playback_mode
                    })
                except Exception as e:
                    print(f"获取模型信息失败: {e}")
                    models.append({
                        "name": "错误组件",
                        "is_playing": False,
                        "error": str(e)
                    })
            return models
        except Exception as e:
            print(f"❌ 同步列出模型失败: {e}")
            return []
    
    async def switch_model(self, data: Dict) -> Dict:
        """切换模型（设置默认组件）"""
        model_name = data.get("model_name", "")
        
        if not model_name:
            return {"error": "模型名称不能为空"}
        
        try:
            # 查找对应的组件
            target_component = self.get_target_component(model_name)
            if not target_component:
                return {"error": f"未找到模型: {model_name}"}
            
            # 设置为默认组件
            self.default_component = target_component
            self.current_model = model_name
            
            return {"success": True, "message": f"已切换到模型: {model_name}"}
        except Exception as e:
            return {"error": f"切换模型时出错: {e}"}
    
    async def seek_to_frame(self, data: Dict) -> Dict:
        """跳转到指定帧"""
        frame = data.get("frame", 0)
        model_name = data.get("model_name", None)
        
        try:
            target_component = self.get_target_component(model_name)
            if not target_component:
                return {"error": "未找到目标组件"}
            
            success = target_component.seek_to_frame(frame)
            if success:
                return {"success": True, "message": f"已跳转到帧: {frame}"}
            else:
                return {"error": "跳转失败"}
        except Exception as e:
            return {"error": f"跳转操作失败: {e}"}
    
    async def set_speed(self, data: Dict) -> Dict:
        """设置播放速度"""
        speed = data.get("speed", 1.0)
        model_name = data.get("model_name", None)
        
        try:
            if model_name:
                # 设置指定组件的速度
                target_component = self.get_target_component(model_name)
                if not target_component:
                    return {"error": f"未找到模型: {model_name}"}
                target_component.set_speed(speed)
                return {"success": True, "message": f"已设置 {model_name} 的播放速度为 {speed}"}
            else:
                # 设置所有组件的速度
                for component in self.active_mmd_components:
                    component.set_speed(speed)
                return {"success": True, "message": f"已设置所有模型的播放速度为 {speed}"}
        except Exception as e:
            return {"error": f"设置速度失败: {e}"}
    
    async def set_loop(self, data: Dict) -> Dict:
        """设置循环播放"""
        loop = data.get("loop", False)
        model_name = data.get("model_name", None)
        
        try:
            if model_name:
                # 设置指定组件的循环
                target_component = self.get_target_component(model_name)
                if not target_component:
                    return {"error": f"未找到模型: {model_name}"}
                target_component.set_loop(loop)
                return {"success": True, "message": f"已设置 {model_name} 的循环播放为 {loop}"}
            else:
                # 设置所有组件的循环
                for component in self.active_mmd_components:
                    component.set_loop(loop)
                return {"success": True, "message": f"已设置所有模型的循环播放为 {loop}"}
        except Exception as e:
            return {"error": f"设置循环失败: {e}"}
    
    async def get_component_status(self, data: Dict) -> Dict:
        """获取指定组件的详细状态"""
        model_name = data.get("model_name", None)
        
        try:
            if model_name:
                target_component = self.get_target_component(model_name)
                if not target_component:
                    return {"error": f"未找到模型: {model_name}"}
                return {"component_info": target_component.get_animation_info()}
            else:
                # 返回所有组件状态
                all_status = []
                for component in self.active_mmd_components:
                    all_status.append(component.get_animation_info())
                return {"all_components": all_status}
        except Exception as e:
            return {"error": f"获取组件状态失败: {e}"}
    
    async def quick_load_and_play(self, data: Dict) -> Dict:
        """快速加载并播放（直接使用文件路径）"""
        vmd_path = data.get("vmd_path", "")
        audio_path = data.get("audio_path", None)
        model_name = data.get("model_name", None)
        options = data.get("options", {})
        
        if not vmd_path:
            return {"error": "VMD文件路径不能为空"}
        
        try:
            target_component = self.get_target_component(model_name)
            if not target_component:
                return {"error": "未找到目标组件"}
            
            # 解析文件路径
            resolved_vmd_path = self._resolve_file_path(vmd_path)
            resolved_audio_path = None
            if audio_path:
                resolved_audio_path = self._resolve_file_path(audio_path)
            
            # 使用组件的快速加载方法
            success = target_component.quick_load_and_play(
                str(resolved_vmd_path),
                str(resolved_audio_path) if resolved_audio_path else None,
                options.get("speed", 1.0),
                options.get("loop", False)
            )
            
            if success:
                self.current_dance = Path(vmd_path).stem
                self.current_model = target_component.object.name
                self.playback_status = "播放中"
                return {"success": True, "message": f"快速加载播放成功: {Path(vmd_path).name}"}
            else:
                return {"error": "快速加载播放失败"}
                
        except Exception as e:
            return {"error": f"快速加载播放失败: {e}"}
    
    def estimate_dance_duration(self, dance_info: Dict) -> float:
        """估算舞蹈时长"""
        try:
            media_analysis = dance_info.get("media_analysis", {})
            motion_props = media_analysis.get("motion_properties", {})
            audio_props = media_analysis.get("audio_properties", {})
            
            # 优先使用音频时长，其次使用动作时长
            if audio_props and audio_props.get("duration"):
                return audio_props["duration"]
            elif motion_props and motion_props.get("duration_seconds"):
                return motion_props["duration_seconds"]
            else:
                return 0.0
        except:
            return 0.0
    
    def update_playback_status(self):
        """更新播放状态"""
        try:
            current_time = time.time()
            
            # 定期检查组件有效性和刷新
            if current_time - self.last_component_check > self.component_check_interval:
                self.last_component_check = current_time
                # 异步检查游戏状态（不阻塞主线程）
                if self.loop and self.loop.is_running():
                    asyncio.run_coroutine_threadsafe(
                        self._periodic_component_check(), 
                        self.loop
                    )
            
            # 安全地检查播放状态
            status_info = self._update_playback_status_sync()
            
            if status_info:
                any_playing = status_info.get("any_playing", False)
                current_info = status_info.get("current_info", {})
                
                # 更新播放状态
                if any_playing:
                    self.playback_status = "播放中"
                    # 更新当前播放信息
                    if not self.current_dance and current_info:
                        self.current_model = current_info.get("model_name")
                        vmd_file = current_info.get("vmd_file", "")
                        if vmd_file:
                            self.current_dance = Path(vmd_file).stem
                else:
                    if self.playback_status == "播放中":
                        self.playback_status = "停止"
                        self.current_dance = None
                        self.current_model = None
                    
        except Exception as e:
            if hasattr(self, 'debug') and self.debug:
                print(f"更新播放状态失败: {e}")
    
    async def _periodic_component_check(self):
        """定期检查组件状态"""
        try:
            # 检查游戏是否仍然活跃
            is_active = await self._check_game_active()
            if not is_active:
                return
            
            # 检查组件是否仍然有效
            valid_components = []
            for component in self.active_mmd_components:
                try:
                    # 尝试访问组件属性来检查有效性
                    _ = component.is_component_playing()
                    valid_components.append(component)
                except Exception as e:
                    if "freed" in str(e):
                        print(f"⚠️ 发现无效组件，已移除")
                    continue
            
            # 如果组件数量发生变化，重新发现
            if len(valid_components) != len(self.active_mmd_components):
                print(f"🔄 组件数量变化: {len(self.active_mmd_components)} -> {len(valid_components)}")
                self.active_mmd_components = valid_components
                
                # 如果默认组件无效，重新设置
                if self.default_component not in valid_components:
                    self.default_component = valid_components[0] if valid_components else None
                    
                # 如果没有有效组件，尝试重新发现
                if not valid_components:
                    await self._refresh_components()
                    
        except Exception as e:
            print(f"❌ 定期组件检查失败: {e}")
    
    def _update_playback_status_sync(self):
        """同步更新播放状态（在主线程中安全执行）"""
        try:
            # 检查所有组件的播放状态
            any_playing = False
            current_info = None
            
            for component in self.active_mmd_components:
                try:
                    if component.is_component_playing():
                        any_playing = True
                        # 更新当前播放信息
                        if not self.current_dance:
                            # 从组件获取当前播放的舞蹈信息
                            info = component.get_animation_info()
                            current_info = info
                        break
                except Exception as e:
                    # 忽略单个组件的错误，继续检查其他组件
                    continue
            
            return {
                "any_playing": any_playing,
                "current_info": current_info
            }
            
        except Exception as e:
            print(f"❌ 同步更新播放状态失败: {e}")
            return None
    
    def shutdown(self):
        """关闭服务端"""
        self.is_running = False
        
        # 清理端口状态文件
        try:
            if self.port_file.exists():
                self.port_file.unlink()
                print(f"🧹 已清理端口状态文件: {self.port_file}")
        except Exception as e:
            print(f"⚠️ 清理端口状态文件失败: {e}")
        
        if self.loop and self.loop.is_running():
            # 在事件循环中调度关闭任务
            self.loop.call_soon_threadsafe(lambda: asyncio.create_task(self._shutdown_async()))
        
        # 等待服务器线程结束
        if self.server_thread and self.server_thread.is_alive():
            self.server_thread.join(timeout=5)  # 最多等待5秒
        
        print("🛑 MMD服务端已关闭")

    async def _shutdown_async(self):
        """异步关闭操作"""
        try:
            # 关闭所有客户端连接
            if self.clients:
                print(f"🔌 关闭 {len(self.clients)} 个客户端连接...")
                for client in list(self.clients):
                    try:
                        await client.close()
                    except:
                        pass
                self.clients.clear()
            
            # 关闭服务器
            if self.server:
                print("🛑 关闭WebSocket服务器...")
                self.server.close()
                await self.server.wait_closed()
                print("✅ WebSocket服务器已关闭")
            
            # 停止当前播放
            await self.stop_current_dance()
            
            # 停止事件循环
            self.loop.stop()
            
        except Exception as e:
            print(f"关闭服务端时出错: {e}")

    # 便捷方法，用于兼容旧代码
    def diagnose_dance_path(self, dance_name: str) -> Dict:
        """诊断特定舞蹈的路径问题（兼容方法）"""
        if self.diagnostics:
            return self.diagnostics.diagnose_dance_path(dance_name)
        else:
            return {"error": "诊断模块未初始化"}
    
    def fix_dance_paths(self) -> bool:
        """修复舞蹈索引中的路径问题（兼容方法）"""
        if self.diagnostics:
            return self.diagnostics.fix_dance_paths()
        else:
            print("❌ 诊断模块未初始化")
            return False
    
    def check_port_status(self, port: int = None) -> Dict:
        """检查端口状态（兼容方法）"""
        if self.port_manager:
            target_port = port or self.port
            return self.port_manager.check_port_status(target_port)
        else:
            return {"error": "端口管理器未初始化"}


# BGE主函数
def main():
    """BGE脚本主函数"""
    cont = bge.logic.getCurrentController()
    own = cont.owner
    
    # 检查是否是初始化
    if "mmd_server" not in own:
        try:
            # 创建服务端实例
            server = UPBGEMMDServer()
            own["mmd_server"] = server
            own["server_initialized"] = True
            
            # 在独立线程中启动服务端
            server.server_thread = threading.Thread(
                target=server.start_server_threaded,
                daemon=True  # 守护线程，主程序退出时自动结束
            )
            server.server_thread.start()
            
            print("✅ MMD服务端已初始化并在后台线程中启动")
            print(f"💡 使用说明:")
            print(f"   - 检查端口状态: check_server_port()")
            print(f"   - 运行测试客户端: run_test_client()")
            print(f"   - 获取服务器状态: server.get_status_sync()")
            print(f"   - 运行综合测试: run_comprehensive_test()")
            
        except Exception as e:
            print(f"❌ 初始化MMD服务端失败: {e}")
    else:
        # 检查服务器是否需要重新初始化（游戏重启情况）
        server = own.get("mmd_server")
        if server:
            try:
                # 检查游戏会话是否发生变化
                current_scene = bge.logic.getCurrentScene()
                current_session_id = id(current_scene)
                
                if server.game_session_id != current_session_id:
                    print("🔄 检测到游戏重启，重新初始化服务器组件...")
                    server.game_session_id = current_session_id
                    server.is_game_active = True
                    
                    # 清理旧的组件引用
                    server.active_mmd_components = []
                    server.default_component = None
                    
                    # 异步重新发现组件
                    if server.loop and server.loop.is_running():
                        asyncio.run_coroutine_threadsafe(
                            server._refresh_components(), 
                            server.loop
                        )
                    
            except Exception as e:
                print(f"⚠️ 检查游戏状态失败: {e}")
    
    # 更新服务端状态
    server = own.get("mmd_server")
    if server:
        try:
            server.update_playback_status()
        except Exception as e:
            if "freed" in str(e):
                print("⚠️ 服务器状态更新失败，BGE对象可能已释放")
                # 标记需要重新初始化
                own["server_needs_refresh"] = True


# BGE清理函数
def cleanup():
    """BGE脚本清理函数"""
    cont = bge.logic.getCurrentController()
    own = cont.owner
    
    server = own.get("mmd_server")
    if server:
        server.shutdown()
        own["mmd_server"] = None


# 向后兼容的全局函数
def diagnose_server():
    """诊断服务器状态（全局函数）"""
    try:
        from server_diagnostics import diagnose_server as _diagnose_server
        return _diagnose_server()
    except ImportError:
        print("❌ 诊断模块未加载")
        return None


def diagnose_dance(dance_name: str):
    """诊断特定舞蹈的路径问题（全局函数）"""
    try:
        from server_diagnostics import diagnose_dance as _diagnose_dance
        return _diagnose_dance(dance_name)
    except ImportError:
        print("❌ 诊断模块未加载")
        return None


def list_available_dances(limit: int = 10):
    """列出可用的舞蹈（全局函数）"""
    try:
        from server_diagnostics import list_available_dances as _list_available_dances
        return _list_available_dances(limit)
    except ImportError:
        print("❌ 诊断模块未加载")
        return []


def fix_paths():
    """修复舞蹈路径问题（全局函数）"""
    try:
        from server_diagnostics import fix_paths as _fix_paths
        return _fix_paths()
    except ImportError:
        print("❌ 诊断模块未加载")
        return False


# 便捷的全局函数，用于直接控制MMD组件
def get_mmd_server():
    """获取当前运行的MMD服务器实例"""
    try:
        scene = bge.logic.getCurrentScene()
        # 查找服务器对象
        for obj in scene.objects:
            server = obj.get("mmd_server")
            if server:
                return server
        return None
    except:
        return None

def test_audio_playback(audio_file_path):
    """测试音频播放功能"""
    print(f"\n🔊 === 音频播放测试 ===")
    print(f"测试文件: {audio_file_path}")
    
    try:
        from pathlib import Path
        audio_path = Path(audio_file_path)
        
        if not audio_path.exists():
            print(f"❌ 音频文件不存在: {audio_path}")
            return False
        
        print(f"✅ 文件存在，大小: {audio_path.stat().st_size} 字节")
        
        # 测试aud模块
        try:
            import aud
            print(f"✅ aud模块可用")
            
            # 测试音频加载
            sound = aud.Sound(str(audio_path))
            print(f"✅ 音频文件加载成功")
            print(f"   时长: {sound.length:.2f} 秒")
            print(f"   规格: {sound.specs}")
            
            # 测试设备创建
            try:
                device = aud.Device()
                print(f"✅ 音频设备创建成功")
                
                # 尝试播放
                handle = device.play(sound)
                print(f"✅ 音频播放启动成功")
                print(f"播放状态: {getattr(handle, 'status', '未知')}")
                
                # 播放3秒后停止
                import time
                time.sleep(3)
                handle.stop()
                print(f"✅ 音频播放测试完成")
                return True
                
            except Exception as e:
                print(f"❌ 音频设备/播放测试失败: {e}")
                return False
                
        except ImportError:
            print(f"❌ aud模块不可用")
            return False
        except Exception as e:
            print(f"❌ aud模块测试失败: {e}")
            return False
            
    except Exception as e:
        print(f"❌ 音频测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_mmd_component_audio(component_name=None):
    """测试MMD组件的音频播放功能"""
    print(f"\n🎭 === MMD组件音频测试 ===")
    
    try:
        # 获取MMD组件
        components = get_all_mmd_components()
        if not components:
            print("❌ 未找到任何MMD组件")
            return False
        
        # 选择要测试的组件
        target_component = None
        if component_name:
            for comp in components:
                if hasattr(comp, 'object') and comp.object.name == component_name:
                    target_component = comp
                    break
        
        if not target_component:
            target_component = components[0]  # 使用第一个组件
        
        # 获取组件名称
        comp_name = "未知组件"
        try:
            if hasattr(target_component, 'object') and hasattr(target_component.object, 'name'):
                comp_name = str(target_component.object.name)
        except:
            pass
        
        print(f"🎯 测试组件: {comp_name}")
        
        # 检查组件是否有音频文件
        if not target_component.audio_file:
            print("⚠️ 组件没有设置音频文件")
            
            # 尝试从test模式文件夹找音频
            try:
                from pathlib import Path
                current_dir = Path.cwd()
                test_folder = current_dir / "中文版极乐净土"
                
                if test_folder.exists():
                    audio_extensions = ["*.wav", "*.mp3", "*.ogg", "*.flac"]
                    for ext in audio_extensions:
                        audio_files = list(test_folder.glob(ext))
                        if audio_files:
                            audio_file = audio_files[0]
                            print(f"🔍 找到测试音频文件: {audio_file.name}")
                            target_component.set_audio_file(str(audio_file))
                            break
            except Exception as e:
                print(f"⚠️ 自动查找音频文件失败: {e}")
        
        if not target_component.audio_file:
            print("❌ 无法进行音频测试：没有音频文件")
            return False
        
        print(f"🎵 音频文件: {target_component.audio_file}")
        
        # 测试音频加载
        print("🔧 测试音频加载...")
        try:
            from pathlib import Path
            audio_path = Path(target_component.audio_file)
            
            if hasattr(target_component.core, 'sync_load_audio'):
                audio_loaded = target_component.core.sync_load_audio(audio_path)
                if audio_loaded:
                    print("✅ 音频加载成功")
                else:
                    print("❌ 音频加载失败")
                    return False
            else:
                print("❌ 组件不支持音频加载")
                return False
        except Exception as e:
            print(f"❌ 音频加载测试失败: {e}")
            return False
        
        # 测试音频播放
        print("🔧 测试音频播放...")
        try:
            if hasattr(target_component.core, 'sync_start_audio'):
                audio_started = target_component.core.sync_start_audio()
                if audio_started:
                    print("✅ 音频播放启动成功")
                    
                    # 播放5秒后停止
                    import time
                    time.sleep(5)
                    
                    if hasattr(target_component.core, 'sync_stop_audio'):
                        target_component.core.sync_stop_audio()
                        print("✅ 音频播放停止成功")
                    
                    print("🎉 MMD组件音频测试完成")
                    return True
                else:
                    print("❌ 音频播放启动失败")
                    return False
            else:
                print("❌ 组件不支持音频播放")
                return False
        except Exception as e:
            print(f"❌ 音频播放测试失败: {e}")
            return False
            
    except Exception as e:
        print(f"❌ MMD组件音频测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def get_all_mmd_components():
    """获取场景中所有的MMD组件"""
    try:
        scene = bge.logic.getCurrentScene()
        if hasattr(scene, 'mmd_components') and scene['mmd_components']:
            return scene['mmd_components']
        return []
    except:
        return []

def find_mmd_component(model_name):
    """根据模型名称查找MMD组件"""
    components = get_all_mmd_components()
    for component in components:
        if component.object.name == model_name:
            return component
    return None

def play_dance_on_model(model_name, dance_name=None, vmd_path=None, speed=1.0, loop=False):
    """在指定模型上播放舞蹈的便捷函数"""
    try:
        component = find_mmd_component(model_name)
        if not component:
            print(f"❌ 未找到模型: {model_name}")
            return False
        
        if dance_name:
            # 通过舞蹈名称播放
            server = get_mmd_server()
            if server:
                result = server.execute_dance_sync(dance_name, {
                    "speed": speed,
                    "loop": loop
                })
                return "success" in result
        elif vmd_path:
            # 直接使用VMD文件路径
            return component.quick_load_and_play(vmd_path, speed=speed, loop=loop)
        
        print("❌ 必须提供舞蹈名称或VMD文件路径")
        return False
        
    except Exception as e:
        print(f"❌ 播放舞蹈失败: {e}")
        return False

def stop_all_dances():
    """停止所有舞蹈的便捷函数"""
    try:
        components = get_all_mmd_components()
        for component in components:
            if component.is_component_playing():
                component.stop_playback()
        print("⏹️ 已停止所有舞蹈")
        return True
    except Exception as e:
        print(f"❌ 停止舞蹈失败: {e}")
        return False

def set_all_speed(speed):
    """设置所有组件播放速度的便捷函数"""
    try:
        components = get_all_mmd_components()
        for component in components:
            component.set_speed(speed)
        print(f"⚡ 已设置所有模型的播放速度为: {speed}")
        return True
    except Exception as e:
        print(f"❌ 设置速度失败: {e}")
        return False

def set_all_loop(loop):
    """设置所有组件循环播放的便捷函数"""
    try:
        components = get_all_mmd_components()
        for component in components:
            component.set_loop(loop)
        print(f"🔄 已设置所有模型的循环播放为: {loop}")
        return True
    except Exception as e:
        print(f"❌ 设置循环失败: {e}")
        return False

def get_playing_status():
    """获取播放状态的便捷函数"""
    try:
        components = get_all_mmd_components()
        status = []
        for component in components:
            info = component.get_animation_info()
            status.append({
                "model": info["model_name"],
                "is_playing": info["is_playing"],
                "progress": info["progress"],
                "current_frame": info["current_frame"],
                "total_frames": info["total_frames"]
            })
        return status
    except Exception as e:
        print(f"❌ 获取状态失败: {e}")
        return []

# 调试函数
def debug_mmd_system():
    """调试MMD系统状态"""
    print("\n🔧 MMD系统调试信息:")
    
    # 检查服务器
    server = get_mmd_server()
    if server:
        print(f"✅ 服务器: 运行中 (端口: {server.port})")
        print(f"   游戏会话ID: {server.game_session_id}")
        print(f"   游戏激活状态: {server.is_game_active}")
        print(f"   活跃组件数: {len(server.active_mmd_components)}")
        
        # 安全获取默认组件名称
        default_component_name = "无"
        if server.default_component:
            try:
                if hasattr(server.default_component, 'object') and hasattr(server.default_component.object, 'name'):
                    default_component_name = str(server.default_component.object.name)
            except:
                default_component_name = "已释放的组件"
        print(f"   默认组件: {default_component_name}")
    else:
        print("❌ 服务器: 未运行")
    
    # 检查组件
    components = get_all_mmd_components()
    print(f"📱 MMD组件: {len(components)} 个")
    for i, component in enumerate(components):
        try:
            info = component.get_animation_info()
            status = "▶️ 播放中" if info["is_playing"] else "⏹️ 停止"
            print(f"   {i+1}. {info['model_name']}: {status}")
            if info["vmd_file"]:
                print(f"      VMD: {Path(info['vmd_file']).name}")
                print(f"      进度: {info['progress']:.1%} ({info['current_frame']}/{info['total_frames']}帧)")
        except Exception as e:
            if "freed" in str(e):
                print(f"   {i+1}. 组件已释放: {e}")
            else:
                print(f"   {i+1}. 组件错误: {e}")

def refresh_mmd_server():
    """手动刷新MMD服务器和组件（游戏重启后使用）"""
    print("🔄 手动刷新MMD服务器...")
    
    try:
        server = get_mmd_server()
        if not server:
            print("❌ 未找到MMD服务器")
            return False
        
        # 获取当前场景
        current_scene = bge.logic.getCurrentScene()
        current_session_id = id(current_scene)
        
        print(f"   当前会话ID: {current_session_id}")
        print(f"   服务器会话ID: {server.game_session_id}")
        
        # 强制更新会话ID
        server.game_session_id = current_session_id
        server.is_game_active = True
        
        # 清理旧组件
        old_count = len(server.active_mmd_components)
        server.active_mmd_components = []
        server.default_component = None
        
        print(f"   已清理 {old_count} 个旧组件")
        
        # 异步刷新组件
        if server.loop and server.loop.is_running():
            future = asyncio.run_coroutine_threadsafe(
                server._refresh_components(), 
                server.loop
            )
            # 等待刷新完成
            future.result(timeout=5)
            print(f"✅ 刷新完成，发现 {len(server.active_mmd_components)} 个新组件")
            return True
        else:
            print("❌ 服务器事件循环未运行")
            return False
            
    except Exception as e:
        print(f"❌ 刷新失败: {e}")
        return False

def force_restart_mmd_server():
    """强制重启MMD服务器（解决严重问题时使用）"""
    print("🔄 强制重启MMD服务器...")
    
    try:
        # 1. 清理端口状态文件
        port_file = Path("mmd_server_port.txt")
        if port_file.exists():
            try:
                port_file.unlink()
                print("   已清理端口状态文件")
            except:
                pass
        
        # 2. 查找并关闭旧服务器
        old_server = get_mmd_server()
        if old_server:
            print("   关闭旧服务器...")
            old_server.shutdown()
        
        # 3. 尝试强制清理端口（如果有端口管理器）
        try:
            from server_port_manager import PortManager
            port_manager = PortManager("localhost")
            
            # 检查并清理常用端口
            for port in [8765, 8766, 8767, 8768, 8769]:
                if not port_manager.is_port_available(port):
                    print(f"   尝试清理端口 {port}...")
                    if hasattr(port_manager, 'force_free_port'):
                        try:
                            port_manager.force_free_port(port)
                        except:
                            pass
        except ImportError:
            print("   端口管理器不可用，跳过端口清理")
        
        # 等待一段时间
        time.sleep(2)
        
        # 4. 清理对象引用
        scene = bge.logic.getCurrentScene()
        for obj in scene.objects:
            if "mmd_server" in obj:
                del obj["mmd_server"]
            if "server_initialized" in obj:
                del obj["server_initialized"]
        
        print("   创建新服务器...")
        # 5. 重新创建服务器
        new_server = UPBGEMMDServer()
        
        # 找一个对象来保存服务器引用
        controller_obj = None
        for obj in scene.objects:
            if hasattr(obj, 'components'):
                controller_obj = obj
                break
        
        if controller_obj:
            controller_obj["mmd_server"] = new_server
            
            # 启动新服务器
            new_server.server_thread = threading.Thread(
                target=new_server.start_server_threaded,
                daemon=True
            )
            new_server.server_thread.start()
            
            print("✅ 新服务器已启动")
            print(f"💡 检查端口状态文件: {port_file}")
            return True
        else:
            print("❌ 未找到合适的对象来保存服务器引用")
            return False
            
    except Exception as e:
        print(f"❌ 强制重启失败: {e}")
        return False

def cleanup_mmd_ports():
    """清理MMD服务器相关的端口占用"""
    print("🧹 清理MMD服务器端口...")
    
    try:
        from server_port_manager import PortManager
        port_manager = PortManager("localhost")
        
        cleaned_ports = []
        # 检查并清理常用端口
        for port in range(8765, 8775):  # 检查8765-8774端口
            if not port_manager.is_port_available(port):
                status = port_manager.check_port_status(port)
                
                # 如果是Python/UPBGE/Blender相关进程，尝试清理
                if status and any(keyword in str(status).lower() for keyword in ['python', 'upbge', 'blender']):
                    print(f"   发现端口 {port} 被相关进程占用，尝试清理...")
                    if hasattr(port_manager, 'force_free_port'):
                        try:
                            if port_manager.force_free_port(port):
                                cleaned_ports.append(port)
                                print(f"   ✅ 已清理端口 {port}")
                        except Exception as e:
                            print(f"   ❌ 清理端口 {port} 失败: {e}")
        
        if cleaned_ports:
            print(f"🎉 成功清理端口: {cleaned_ports}")
        else:
            print("✅ 没有需要清理的端口")
        
        return cleaned_ports
        
    except ImportError:
        print("❌ 端口管理器不可用")
        return []
    except Exception as e:
        print(f"❌ 清理端口失败: {e}")
        return []


if __name__ == "__main__":
    main()


"""
使用示例：

1. 在BGE的Always传感器中调用main()函数来初始化服务器

2. 从其他BGE脚本中使用服务器：
   
   # 获取服务器实例
   scene = bge.logic.getCurrentScene()
   controller_obj = scene.objects.get("你的控制器对象名称")
   server = controller_obj.get("mmd_server")
   
   if server:
       # 执行舞蹈
       result = server.execute_dance_sync("舞蹈名称", {"speed": 1.0, "loop": False})
       print(result)
       
       # 停止舞蹈
       result = server.stop_dance_sync()
       print(result)
       
       # 获取状态
       status = server.get_status_sync()
       print(status)

3. 客户端可以通过WebSocket连接到 ws://localhost:8765 来控制舞蹈

4. 在BGE游戏结束时调用cleanup()函数来正确关闭服务器

便捷功能（从导入的模块中获得）：

5. 端口管理：
   check_server_port()  # 检查当前端口状态
   restart_server()     # 重启服务器

6. 诊断功能：
   diagnose_server()           # 诊断服务器状态
   diagnose_dance("舞蹈名")    # 诊断特定舞蹈
   list_available_dances()     # 列出可用舞蹈
   fix_paths()                 # 修复路径问题

7. 测试功能：
   run_test_client()           # 简单测试
   run_comprehensive_test()    # 综合测试

8. 生命周期管理（新增，解决游戏重启问题）：
   debug_mmd_system()          # 调试系统状态
   refresh_mmd_server()        # 手动刷新服务器（游戏重启后）
   force_restart_mmd_server()  # 强制重启服务器
   cleanup_mmd_ports()         # 清理MMD相关端口占用

游戏重启问题解决方案：
- 系统现在会自动检测游戏重启并重新初始化组件
- 服务器会保存端口信息到文件，客户端可自动发现
- 如果端口被占用，会尝试清理旧进程并重用端口
- 如果自动检测失败，使用 refresh_mmd_server() 手动刷新
- 如果问题严重，使用 force_restart_mmd_server() 强制重启
- 如果端口一直被占用，使用 cleanup_mmd_ports() 清理端口

注意：拆分后的模块结构
- upbge_mmd_server.py: 主服务器类和BGE接口
- server_port_manager.py: 端口管理功能
- server_diagnostics.py: 诊断和路径修复功能
- server_utils.py: 测试客户端和便捷工具
- BGE_THREAD_SAFETY_GUIDE.md: 线程安全指导文档
"""