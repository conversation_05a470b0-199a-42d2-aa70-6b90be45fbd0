#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
前端显示效果演示
模拟前端管理界面显示回复消息的效果
"""

import sys
import os
import time
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from func.llm.chat_db import ChatDB

def create_demo_data():
    """创建演示数据"""
    
    print("📝 创建演示数据...")
    chat_db = ChatDB()
    
    # 创建多个用户的问题和回复
    demo_data = [
        {
            "user": {"name": "AI呦呦-3D舞宠", "id": "dc89b178fba24e20a036878e8cae3c68"},
            "question": "复合点",
            "reply": "复合啥"
        },
        {
            "user": {"name": "小明", "id": "user123456"},
            "question": "今天天气怎么样",
            "reply": "今天天气很好，阳光明媚"
        },
        {
            "user": {"name": "张三", "id": "zhangsan789"},
            "question": "你好吗",
            "reply": "我很好，谢谢关心"
        }
    ]
    
    for i, data in enumerate(demo_data):
        current_time = time.time() + i  # 稍微错开时间
        formatted_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(current_time))
        
        # 插入原始问题
        original_question = {
            "traceid": f"demo_question_{int(current_time)}",
            "query": data["question"],
            "uid": data["user"]["id"],
            "user_name": data["user"]["name"],
            "uface": "",
            "channel": "webili",
            "submitTime": formatted_time
        }
        
        chat_db.insert_request(original_question)
        print(f"  ✅ 插入问题: {data['user']['name']} - {data['question']}")
        
        # 解析并插入回复
        reply_message = f"[回复<{data['user']['name']}:{data['user']['id']}> 的消息：{data['question']}] {data['reply']}"
        reply_info = chat_db.parse_reply_message(reply_message)
        
        if reply_info:
            reply_data = {
                "traceid": f"demo_reply_{int(current_time)}",
                "sender_name": reply_info["sender_name"],
                "sender_id": reply_info["sender_id"],
                "question": reply_info["question"],
                "reply_content": reply_info["reply_content"]
            }
            
            chat_db.insert_reply(reply_data)
            print(f"  ✅ 插入回复: {data['user']['name']} - {data['reply']}")
    
    print("📝 演示数据创建完成!\n")

def display_frontend_table():
    """模拟前端表格显示效果"""
    
    print("🖥️  前端管理界面显示效果")
    print("=" * 100)
    
    chat_db = ChatDB()
    
    # 查询最近的聊天记录
    api_result = chat_db.find_chat_list_page(
        username="",  # 查询所有用户
        page_number=1,
        page_size=20
    )
    
    if not api_result.get('data'):
        print("❌ 没有找到聊天记录")
        return
    
    # 模拟前端表格显示
    print(f"{'用户名':<15} {'渠道':<8} {'意图':<8} {'问题':<25} {'回复':<30} {'创建时间':<20}")
    print("-" * 100)
    
    for record in api_result['data']:
        user_name = record.get('user_name', 'N/A')[:14]
        channel = record.get('channel', 'N/A')[:7]
        intent = record.get('intent', 'N/A')[:7]
        query = record.get('query', 'N/A')[:24]
        content = record.get('content', 'N/A')[:29]
        submit_time_raw = record.get('submitTime', 'N/A')
        if isinstance(submit_time_raw, str):
            submit_time = submit_time_raw[:19]
        else:
            submit_time = str(submit_time_raw)[:19]
        
        print(f"{user_name:<15} {channel:<8} {intent:<8} {query:<25} {content:<30} {submit_time:<20}")
    
    print("-" * 100)
    print(f"总记录数: {api_result.get('total_documents', 0)}, 当前页: {api_result.get('current_page', 1)}/{api_result.get('total_pages', 1)}")

def display_json_format():
    """显示JSON格式数据"""
    
    print("\n📋 前端接收的JSON数据格式")
    print("=" * 60)
    
    chat_db = ChatDB()
    api_result = chat_db.find_chat_list_page(username="", page_number=1, page_size=3)
    
    import json
    
    # 模拟前端接收的数据格式
    frontend_data = {
        "status": "success",
        "data": api_result.get('data', []),
        "total_documents": api_result.get('total_documents', 0),
        "total_pages": api_result.get('total_pages', 0),
        "current_page": api_result.get('current_page', 1),
        "page_size": api_result.get('page_size', 10)
    }
    
    # 处理ObjectId序列化问题
    def clean_data(obj):
        if isinstance(obj, dict):
            return {k: clean_data(v) for k, v in obj.items() if k != '_id'}
        elif isinstance(obj, list):
            return [clean_data(item) for item in obj]
        else:
            return obj
    
    clean_frontend_data = clean_data(frontend_data)
    
    print(json.dumps(clean_frontend_data, ensure_ascii=False, indent=2))

def main():
    """主函数"""
    
    print("🚀 前端显示效果完整演示")
    print("=" * 60)
    
    try:
        # 创建演示数据
        create_demo_data()
        
        # 显示前端表格效果
        display_frontend_table()
        
        # 显示JSON数据格式
        display_json_format()
        
        print("\n" + "=" * 60)
        print("🎉 演示完成!")
        print("\n💡 说明:")
        print("- 原始问题和回复都会显示在前端管理界面")
        print("- 回复记录的意图字段标记为'回复'")
        print("- 问题字段显示为'[回复] 回复内容'")
        print("- 回复内容显示在'回复'列中")
        print("- 时间格式为'YYYY-MM-DD HH:MM:SS'")
        
    except Exception as e:
        print(f"❌ 演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
