from func.tools.singleton_mode import singleton

print("---------初始化----EntranceCore----")

@singleton
def EntranceCore():
    print("Obs Init Successful-------------")
    
    class Handler:
        @staticmethod
        def msg_deal(traceid, query, uid, user_name):
            """
            处理弹幕消息
            """
            print("------------------msg_deal-------------------")
    
    return Handler()
