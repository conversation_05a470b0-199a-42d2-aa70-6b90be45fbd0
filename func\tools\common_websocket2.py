import asyncio
import websockets
import threading
from functools import partial
from func.tools.singleton_mode import SingletonMode

@SingletonMode
class CommonWebsocket:
    def __init__(self):
        self.clients = set()
        self._initializing = False
        # 其他初始化代码...

    async def send_data(self, message):
        """向客户端发送数据"""
        if not self.clients:
            return
            
        if hasattr(self, '_is_coroutine'):
            await self._send_impl(message)
        else:
            asyncio.create_task(self._send_impl(message))

    async def _send_impl(self, message):
        """实际发送数据的实现"""
        disconnected_clients = set()
        for client in self.clients:
            try:
                await client.send(message)
            except websockets.exceptions.ConnectionClosed:
                disconnected_clients.add(client)
            except Exception as e:
                print(f"Send error: {e}")
                disconnected_clients.add(client)
        
        # 清理断开的连接
        self.clients -= disconnected_clients

    async def handler(self, websocket, path):
        """处理客户端连接"""
        self.clients.add(websocket)
        print(f"Client connected: {websocket.remote_address}")
        try:
            async for message in websocket:
                print(f"Received message: {message}")
                # await self.broadcast(message)
        except websockets.exceptions.ConnectionClosed:
            print(f"Client disconnected: {websocket.remote_address}")
        finally:
            self.clients.remove(websocket)

    async def broadcast(self, message):
        """广播消息给所有客户端"""
        if not self.clients:
            return
            
        await asyncio.gather(
            *[client.send(message) for client in self.clients],
            return_exceptions=True
        )

    def start_server(self, host='0.0.0.0', port = 18765):
        """启动WebSocket服务"""
        loop = asyncio.get_event_loop()
        server = websockets.serve(self.handler, host, port)
        loop.run_until_complete(server)
        loop.run_forever()

    def run_forever_thread(self):
        """在新线程中运行事件循环"""
        threading.Thread(target=self.start_server, daemon=True).start()