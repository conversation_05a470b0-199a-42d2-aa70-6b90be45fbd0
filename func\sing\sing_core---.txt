J:\ai\�������\tt2\sing\sing_core.c     sing_core.py    
__init__        sing_core.SingCore.__init__ 
isSing      sing_core.SingCore.isSing       
singTry sing_core.SingCore.singTry  
sing        sing_core.SingCore.sing 
check_playSongMenuList  sing_core.SingCore.check_playSongMenuList       
create_song     sing_core.SingCore.create_song  
down_song_file  sing_core.SingCore.down_song_file       
down_lrc_file   sing_core.SingCore.down_lrc_file        
play_song       sing_core.SingCore.play_song    
sing_play       sing_core.SingCore.sing_play    
accompany_play  sing_core.SingCore.accompany_play       
check_sing      sing_core.SingCore.check_sing   
http_sing       sing_core.SingCore.http_sing    
http_songlist   sing_core.SingCore.http_songlist        
inner_sing      sing_core.SingCore.inner_sing   
msg_deal        sing_core.SingCore.msg_deal     
exist_song_queues       sing_core.SingCore.exist_song_queues    
check_down_song sing_core.SingCore.check_down_song      sing_core       Interpreter change detected - this module can only be loaded into one interpreter per process.  name    __loader__  loader      __file__    origin      __package__ parent      __path__        submodule_search_locations      Module 'sing_core' has already been imported. Re-initialisation is not supported.       builtins        cython_runtime  __builtins__    init sing_core  name '%U' is not defined        %s() got multiple values for keyword argument '%U'      %.200s() keywords must be strings       %s() got an unexpected keyword argument '%U'    exactly s       %.200s() takes %.8s %zd positional argument%.1s (%zd given)      while calling a Python object  NULL result without error in PyObject_Call      join() result is too long for a Python string   cannot fit '%.200s' into an index-sized integer '%.200s' object is not subscriptable    '%.200s' object is unsliceable  strings are too large to concat cannot import name %S   _cython_3_0_11  Shared Cython type %.200s is not a type object  Shared Cython type %.200s has the wrong size, try recompiling   keywords must be strings        __name__ must be set to a string object __qualname__ must be set to a string object     function's dictionary may not be deleted        setting function's dictionary to a non-dict     __defaults__ must be set to a tuple object      changes to cyfunction.__defaults__ will not currently affect the values used in function calls  __kwdefaults__ must be set to a dict object     changes to cyfunction.__kwdefaults__ will not currently affect the values used in function calls        __annotations__ must be set to a dict object    func_doc        __doc__ func_name       __name__        __qualname__    func_dict       __dict__        func_globals    __globals__     func_closure    __closure__     func_code       __code__        func_defaults   __defaults__    __kwdefaults__  __annotations__ _is_coroutine   __module__      __reduce__      Bad call flags for CyFunction   <cyfunction %U at %p>   %.200s() takes no arguments (%zd given) %.200s() takes exactly one argument (%zd given) %.200s() takes no keyword arguments     unbound method %.200S() needs an argument       %.200s() needs an argument      _cython_3_0_11.cython_function_or_method        %s (%s:%d)      does not match          compile time Python version %d.%d of module '%.100s' %s runtime version %d.%d   requests        func.score.oper_score   mlist   根据" 0       func.tts.player set     ttsCore 》,播放路径:       ]你的积分小于 info        http唱歌接口处理："      key_verify      exception       》，请勿重新点播        visible_input   SongNowName     lrc_stop_event  /accompany.wav  actionOper      get_audio   exists      jsonstr default SingCore.sing   SecretData      cline_in_traceback      LLmData count   threading       播放歌词:   点歌  control_video   func.tools.singleton_mode       SingData    open        song_json       ���   music_json      outputTxt       release SingCore.create_song    func.tts.tts_core       accompany.wav   save_path       __prepare__     SingCore.down_lrc_file  SingCore.play_song  当前  time        emoteOper       img_search_json PLAY    is_creating_song        ImageCore   波形      CommonData      http_sing       mpv_play_thread type_tts_say    【singTry】发生了异常： down_song_file  searchimg_output        font_text       http_songlist   tts_count:      func.gobal.data 歌库不存在《      __enter__       secretData      __main__    value       已经跳过歌曲《   has_string_reg_list     song.exe        "点播歌曲《        》，请耐心等待   __qualname__    get_lrc 》,举起麦克风     __module__      需要学唱歌曲《   》play_song异常：   convertfail     processed   self        username        SingCore.exist_song_queues      sing_play       __dict__        ���   _initializing   prompt  SingCore.isSing 当前歌曲只下载不转换《       ({"status": "成功","content":     tip mpvPlay ：我准备唱一首歌《     func.vtuber.action_oper "的信息，   play_song       Ai_Name emote_ws    str show_lrc_thread check_sing  target      ]秒,生成状态:  url SingCore.down_song_file musicJson   》 wb  range       accompany_play  》，请稍后再点播        songName    rooms   switch  lower       开始播放歌曲:     socre_thread    auto_swing      auto_swing_thread       /append_song/   singTry _is_coroutine   sing_thread     processing      waiting '点播《  super       》create_song异常： __spec__    ^   SongQueueList   msg_deal        结束唱歌    》,放下麦克风 mkdir       》歌曲第[   start   等待播放    《 点播      0123456789abcdef0123456789ABCDEF        SingCore.singTry    .   save_folder     func.tools.string_util  create_song     ]唱歌提示：        __import__      song_path       /download_origin_song/  match   filename        歌曲《   query   log SingCore.check_down_song    qsize       00010203040506070809101112131415161718192021222324252627282930313233343536373839404142434445464748495051525354555657585960616263646566676869707172737475767778798081828384858687888990919293949596979899        __doc__ create_song_lock        __class_getitem__       生成歌曲失败《   content 点播的歌曲《  Event       ObsInit songname        __set_name__    func.obs.obs_init       exist_song_queues       ./output/   status  ?   create_song_timout      is_tts_playing  /musicInfo/     VideoControl    accompany_thread        VideoStatus     oper_score  uid queue   func.log.default_log    song_not_convert        SingCore.check_sing     acquire SongMenuList    replace vocal_downfile  TTsCore re  name        is_singing  ： func.vtuber.emote_oper  DefaultLog      __init_subclass__       mpv_name        /song.lrc       llmData 启动唱歌:   get_accompany   singData        down_lrc_file   背景音乐    歌单里已经有歌曲《     准备生成歌曲内容：     func.image.image_core   秒     歌曲不存在，需要生成歌曲《 assistant_tts_say       your_score      converted_file  get force   write   *   f       channel 找到存在本地歌曲:       SingCore.inner_sing     is_created  dumps   num SingCore.check_playSongMenuList ,is_singing：  volume  timeout check_playSongMenuList  data    search  sing_core       show_text   text        __test__        status_json     SingCore        lrcCore func.tools.decorator_mode       CallBackForTest sing    SingCore.msg_deal       生成《       singUrl json            
        是否正在唱歌
        :return: False:否 True：是
              ，不能进行唱歌，请通过聊天和点赞赚取积分，查询积分请输入"我的积分"      downfile        is_download     SingCore.http_sing      启动歌曲:   》这首歌曲哦  》：  e   commonData  key i       accompany_downfile      StringUtil      interface_name  is_contain      is_index_contain_string loads   uface   》第  isSing  SingCore.http_songlist  put     is_tts_creating SingCore.__init__       ,is_tts_playing:        LrcCore song.lrc        MpvPlay empty   会唱《       func.obs.obs_websocket  converted_json  ttsData Thread  SingCore.sing_play  })  已经学会歌曲《   /accompany_vocal_status trace_tts_list  唱歌  '       __metaclass__   getLogger   [   找到服务已经转换的歌曲《    operScore       学唱歌曲《 show_lrc        get_vocal       func.sing.lrc_core  obs args    SingCore.accompany_play __exit__        ,is_tts_creating:       __init__        traceid jsonStr strip   回复  asyncio.coroutines      check_down_song accompany.exe   __name__    get_ws      sing_play_flag  sleep   score   queryExtract    user_name       ObsWebSocket    id  /   singleton   PAUSE       inner_sing      searchimg_output_thread 00010203040506071011121314151617202122232425262730313233343536374041424344454647505152535455565760616263646566677071727374757677        准备唱歌《 os      状态提示    file_name       EmoteOper       ActionOper      J:\ai\吟美打包\tt2\sing\sing_core.py        imageCore   queues      get_score       vocal.wav   path        func.llm.llm_core       mpv_play        /vocal.wav      OperScore