#!/usr/bin/env python3
"""
测试脚本：验证 question 字段修复是否正确工作
"""

import sys
import os
import time
import asyncio
from unittest.mock import Mock, patch

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.chat.message_receive.message import MessageSending, MessageRecv, UserInfo, Seg
from src.chat.message_receive.chat_stream import ChatStream
from src.common.message.base import BaseMessageInfo, GroupInfo
from src.chat.message_receive.uni_message_sender import send_message
from func.gobal.data import LLmData

def create_test_message_recv(question_text: str) -> MessageRecv:
    """创建测试用的 MessageRecv 对象"""
    user_info = UserInfo(
        platform="webili",
        user_id="test_user_123",
        user_nickname="测试用户"
    )
    
    group_info = GroupInfo(
        platform="webili",
        group_id=1234567890987654321,
        group_name="测试群组"
    )
    
    message_info = BaseMessageInfo(
        platform="webili",
        message_id="test_question_msg_123",
        time=time.time(),
        group_info=group_info,
        user_info=user_info
    )
    
    # 构建 MessageRecv 需要的字典
    recv_dict = {
        "message_info": message_info.to_dict(),
        "message_segment": {"type": "text", "data": question_text},
        "raw_message": question_text,
        "processed_plain_text": question_text,
    }
    
    return MessageRecv(recv_dict)

def create_test_message_sending(reply_message: MessageRecv, answer_text: str) -> MessageSending:
    """创建测试用的 MessageSending 对象"""
    user_info = UserInfo(
        platform="webili",
        user_id="bot_123",
        user_nickname="AI助手"
    )
    
    group_info = GroupInfo(
        platform="webili",
        group_id=1234567890987654321,
        group_name="测试群组"
    )
    
    # 创建一个简单的 ChatStream mock
    chat_stream = Mock()
    chat_stream.platform = "webili"
    chat_stream.group_info = group_info
    chat_stream.user_info = user_info
    
    message_segment = Seg(type="text", data=answer_text)
    
    return MessageSending(
        message_id="test_answer_msg_456",
        chat_stream=chat_stream,
        bot_user_info=user_info,
        sender_info=reply_message.message_info.user_info,
        message_segment=message_segment,
        reply=reply_message,
        processed_plain_text=answer_text
    )

async def test_question_extraction():
    """测试问题提取功能"""
    print("开始测试问题提取功能...")
    
    # 创建测试数据
    question_text = "你好，今天天气怎么样？"
    answer_text = "今天天气很好，阳光明媚！"
    
    # 创建测试消息对象
    question_message = create_test_message_recv(question_text)
    answer_message = create_test_message_sending(question_message, answer_text)
    
    # Mock LLmData.AnswerList.put 方法来捕获输出
    captured_json = None
    
    def mock_put(json_data):
        nonlocal captured_json
        captured_json = json_data
        print(f"捕获到的 JSON 数据: {json_data}")
    
    # Mock get_global_api().send_message 方法
    async def mock_send_message(message):
        print(f"Mock 发送消息: {message.processed_plain_text}")
    
    # 使用 patch 来模拟依赖
    with patch('src.chat.message_receive.uni_message_sender.get_global_api') as mock_api, \
         patch.object(LLmData().AnswerList, 'put', side_effect=mock_put):
        
        mock_api.return_value.send_message = mock_send_message
        
        # 执行测试
        result = await send_message(answer_message, show_log=False)
        
        # 验证结果
        assert result == True, "消息发送应该成功"
        assert captured_json is not None, "应该捕获到 JSON 数据"
        assert captured_json["question"] == question_text, f"问题字段应该是 '{question_text}', 但实际是 '{captured_json['question']}'"
        assert captured_json["text"] == answer_text, f"回答字段应该是 '{answer_text}', 但实际是 '{captured_json['text']}'"
        assert captured_json["voiceType"] == "chat", "voiceType 应该是 'chat'"
        assert captured_json["chatStatus"] == "waiting", "chatStatus 应该是 'waiting'"
        
        print("✅ 测试通过！问题字段已正确提取")
        print(f"   原始问题: {question_text}")
        print(f"   提取的问题: {captured_json['question']}")
        print(f"   回答内容: {captured_json['text']}")

async def test_no_reply_case():
    """测试没有回复消息的情况"""
    print("\n开始测试没有回复消息的情况...")
    
    answer_text = "这是一个没有回复的消息"
    
    # 创建没有回复的消息
    user_info = UserInfo(
        platform="webili",
        user_id="bot_123",
        user_nickname="AI助手"
    )
    
    chat_stream = Mock()
    chat_stream.platform = "webili"
    
    message_segment = Seg(type="text", data=answer_text)
    
    answer_message = MessageSending(
        message_id="test_no_reply_msg_789",
        chat_stream=chat_stream,
        bot_user_info=user_info,
        sender_info=None,
        message_segment=message_segment,
        reply=None,  # 没有回复
        processed_plain_text=answer_text
    )
    
    # Mock LLmData.AnswerList.put 方法
    captured_json = None
    
    def mock_put(json_data):
        nonlocal captured_json
        captured_json = json_data
        print(f"捕获到的 JSON 数据: {json_data}")
    
    with patch('src.chat.message_receive.uni_message_sender.get_global_api') as mock_api, \
         patch.object(LLmData().AnswerList, 'put', side_effect=mock_put):
        
        mock_api.return_value.send_message = Mock()
        
        # 执行测试
        result = await send_message(answer_message, show_log=False)
        
        # 验证结果
        assert result == True, "消息发送应该成功"
        assert captured_json is not None, "应该捕获到 JSON 数据"
        assert captured_json["question"] == "", "没有回复时问题字段应该是空字符串"
        assert captured_json["text"] == answer_text, f"回答字段应该是 '{answer_text}'"
        
        print("✅ 测试通过！没有回复时问题字段为空")
        print(f"   问题字段: '{captured_json['question']}'")
        print(f"   回答内容: {captured_json['text']}")

async def main():
    """主测试函数"""
    print("=" * 50)
    print("测试 question 字段修复功能")
    print("=" * 50)
    
    try:
        await test_question_extraction()
        await test_no_reply_case()
        print("\n🎉 所有测试都通过了！")
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())
