J:\ai\�������\tt2\dance_core.c dance_core.py   __init__        dance_core.DanceCore.__init__   check_dance     dance_core.DanceCore.check_dance    dance       dance_core.DanceCore.dance      sing_dance      dance_core.DanceCore.sing_dance emote_play_nodance      dance_core.DanceCore.emote_play_nodance emote_play      dance_core.DanceCore.emote_play msg_deal_emotevideo     dance_core.DanceCore.msg_deal_emotevideo        inner_dance     dance_core.DanceCore.inner_dance        msg_deal_dance  dance_core.DanceCore.msg_deal_dance     dance_core      Interpreter change detected - this module can only be loaded into one interpreter per process.  name    __loader__  loader      __file__    origin      __package__ parent      __path__        submodule_search_locations      Module 'dance_core' has already been imported. Re-initialisation is not supported.      builtins        cython_runtime  __builtins__    init dance_core %s() got multiple values for keyword argument '%U'      %.200s() keywords must be strings       %s() got an unexpected keyword argument '%U'    exactly s       %.200s() takes %.8s %zd positional argument%.1s (%zd given)     name '%U' is not defined         while calling a Python object  NULL result without error in PyObject_Call      join() result is too long for a Python string   cannot fit '%.200s' into an index-sized integer '%.200s' object is not subscriptable    '%.200s' object is unsliceable  cannot import name %S   _cython_3_0_11  Shared Cython type %.200s is not a type object  Shared Cython type %.200s has the wrong size, try recompiling   keywords must be strings        __name__ must be set to a string object __qualname__ must be set to a string object     function's dictionary may not be deleted        setting function's dictionary to a non-dict     __defaults__ must be set to a tuple object      changes to cyfunction.__defaults__ will not currently affect the values used in function calls  __kwdefaults__ must be set to a dict object     changes to cyfunction.__kwdefaults__ will not currently affect the values used in function calls        __annotations__ must be set to a dict object    func_doc        __doc__ func_name       __name__        __qualname__    func_dict       __dict__        func_globals    __globals__     func_closure    __closure__     func_code       __code__        func_defaults   __defaults__    __kwdefaults__  __annotations__ _is_coroutine   __module__      __reduce__      Bad call flags for CyFunction   <cyfunction %U at %p>   %.200s() takes no arguments (%zd given) %.200s() takes exactly one argument (%zd given) %.200s() takes no keyword arguments     unbound method %.200S() needs an argument       %.200s() needs an argument      _cython_3_0_11.cython_function_or_method        %s (%s:%d)      does not match          compile time Python version %d.%d of module '%.100s' %s runtime version %d.%d   func.score.oper_score   背景音乐    0       ttsCore info    key_verify  随机      emote_play_thread   video       default SecretData      sing_dance      cline_in_traceback  count       threading       control_video   CmdCore func.tools.singleton_mode   sched1      SingData        ���   release func.tts.tts_core       __prepare__     TTsData time    emoteData   内部  PLAY    resume      DanceCore.sing_dance    msg_deal_dance  tts_count:      func.gobal.data 播放表情:   secretData      __main__    value       has_string_reg_list     RESTART dance_video     跳舞视频不存在：        __qualname__    __module__  跳舞      DanceCore.msg_deal_dance    self        username        __dict__        x��   play_video      _initializing   prompt  matches_list    唱歌视频    target  \dance  ]你的积分小于 random  switch      socre_thread    emote_play      _is_coroutine   super   eomte_path      __spec__        singdance_now_path  start   *   all ?   DanceCore.emote_play_nodance            0123456789abcdef0123456789ABCDEF        func.tools.string_util  DanceCore.__init__      __import__      DanceData   query   log 00010203040506070809101112131415161718192021222324252627282930313233343536373839404142434445464748495051525354555657585960616263646566676869707172737475767778798081828384858687888990919293949596979899        __doc__ __class_getitem__       跳一下       DanceCore       ObsInit songname        __set_name__    func.obs.obs_init   status      is_tts_playing  VideoControl    VideoStatus     DanceCore.dance oper_score  uid func.log.default_log    emote_video_lock        acquire get_video_status        SongMenuList    replace rnd_video       TTsCore dance_json      __name__        check_dance     is_singing  [   DefaultLog      __init_subclass__   tags        danceData       singData    dance       ]跳舞表情：        msg_deal_emotevideo     ，不能进行跳舞，请通过聊天和点赞赚取积分，查询积分请输入"我的积分"      assistant_tts_say       your_score  get rnd     channel ]跳舞提示：        emote_play_nodance  num data    pause   DanceCore.check_dance   text    __test__        func.tools.decorator_mode       jieba_extract_tags      fuzzy_match_list    表情      func.cmd.cmd_core   key StringUtil      is_contain      is_index_contain_string uface   put     is_tts_creating ,is_tts_playing:        DanceCore.emote_play    empty           emote_now_path  END     func.obs.obs_websocket  ttsData Thread  .       DanceCore.inner_dance   trace_tts_list  __metaclass__   cmdCore EmoteData       randrange       inner_dance     getLogger       operScore   obs args    ,is_tts_creating:       __init__        traceid 开始跳舞了，大家嗨起来   strip   ^   asyncio.coroutines      dance_now_path  name    get_ws  video_path      is_dance    tag sleep   score   STOP    queryExtract    user_name       ObsWebSocket    sec #   singleton       DanceCore.msg_deal_emotevideo   PAUSE   cmd     dance_core      00010203040506071011121314151617202122232425262730313233343536374041424344454647505152535455565760616263646566677071727374757677    舞蹈      J:\ai\吟美打包\tt2\dance_core.py    emote_video     DanceQueueList  get_score       OperScore