J:\ai\�������\ai-yinmei\func\llm\tgw.c tgw.py  chat    tgw.Tgw.chat    tgw             Interpreter change detected - this module can only be loaded into one interpreter per process.  name    __loader__  loader      __file__    origin      __package__ parent      __path__        submodule_search_locations      Module 'tgw' has already been imported. Re-initialisation is not supported.     builtins        cython_runtime  __builtins__    init tgw        exactly s       %.200s() takes %.8s %zd positional argument%.1s (%zd given)     %s() got multiple values for keyword argument '%U'      %.200s() keywords must be strings       %s() got an unexpected keyword argument '%U'     while calling a Python object  NULL result without error in PyObject_Call      '%.200s' object has no attribute '%U'   name '%U' is not defined        join() result is too long for a Python string   cannot import name %S   _cython_3_0_11  Shared Cython type %.200s is not a type object  Shared Cython type %.200s has the wrong size, try recompiling   keywords must be strings        __name__ must be set to a string object __qualname__ must be set to a string object     function's dictionary may not be deleted        setting function's dictionary to a non-dict     __defaults__ must be set to a tuple object              changes to cyfunction.__defaults__ will not currently affect the values used in function calls  __kwdefaults__ must be set to a dict object     changes to cyfunction.__kwdefaults__ will not currently affect the values used in function calls        __annotations__ must be set to a dict object    func_doc        __doc__ func_name       __name__        __qualname__    func_dict       __dict__        func_globals    __globals__     func_closure    __closure__     func_code       __code__        func_defaults   __defaults__    __kwdefaults__  __annotations__ _is_coroutine   __module__      __reduce__      Bad call flags for CyFunction   <cyfunction %U at %p>   %.200s() takes no arguments (%zd given) %.200s() takes exactly one argument (%zd given) %.200s() takes no keyword arguments     unbound method %.200S() needs an argument       %.200s() needs an argument      _cython_3_0_11.cython_function_or_method        %s (%s:%d)      does not match          compile time Python version %d.%d of module '%.100s' %s runtime version %d.%d   requests    .   headers exception       cline_in_traceback      func.tools.singleton_mode   role        �� �   character       Tgw.chat        __prepare__ Tgw 【     defaultConfig   verify  __main__        add_bos_token   __qualname__    func.config.default_config      __module__      relation    self        username        __dict__        X� �   _initializing   tgw     max_new_tokens  *   str your_name   mode        instruction_template    _is_coroutine   super   __spec__    post        application/json        choices intent  append  user    __import__  config  log __doc__ content __set_name__    response        我听不懂你说什么    uid func.log.default_log    message __name__        DefaultLog      __init_subclass__       Content-Type    get_config  Alpaca  chat        __annotations__ seed    history timeout data    __test__    json        messages    e   J:\ai\吟美打包\ai-yinmei\func\llm\tgw.py    skip_special_tokens llm __metaclass__   text-generation-webui   getLogger       ban_eos_token   assistant_message       asyncio.coroutines      do_sample       tgw_url preset  ?       singleton       】信息回复异常