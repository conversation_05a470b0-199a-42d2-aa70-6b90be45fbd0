from flask_apscheduler import APScheduler
import asyncio.coroutines

class sched(object):

    scheduler = APScheduler()

    def __init__(self):

        print("sched--------init----------")

        self.scheduler = APScheduler()

        # 初始化各个核心组件
        
    def bind_sched(self, app):

        print("bind_sched--------------")
        
        """绑定调度任务到Flask应用"""
        self.scheduler.init_app(app) # init_app(app)

        # 定时任务
        self._add_check_jobs()
        
        self.scheduler.start()
        
    def _add_check_jobs(self):
        """添加各种检查任务"""
        print("_add_check_jobs------------")