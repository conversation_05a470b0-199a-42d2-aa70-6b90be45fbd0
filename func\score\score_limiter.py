"""
积分系统限流管理器
提供API限流、防刷机制等安全功能
"""

import time
import threading
from typing import Dict, Any, Optional
from functools import wraps
from func.log.default_log import DefaultLog
from func.score.score_config import ScoreSecurityConfig, ScoreCacheConfig

# 可选导入Redis
try:
    import redis
    REDIS_AVAILABLE = True
except ImportError:
    REDIS_AVAILABLE = False


class ScoreRateLimiter:
    """积分系统限流器"""
    
    def __init__(self, cache_manager=None):
        self.log = DefaultLog().getLogger()
        self.cache_manager = cache_manager
        self.memory_counters = {}  # 内存计数器（降级使用）
        self.memory_locks = {}     # 内存锁
        
    def check_rate_limit(self, user_id: str, operation: str = "default") -> Dict[str, Any]:
        """
        检查用户操作频率限制
        :param user_id: 用户ID
        :param operation: 操作类型
        :return: 检查结果
        """
        current_time = int(time.time())
        
        # 检查每分钟限制
        minute_key = f"{ScoreCacheConfig.RATE_LIMIT_PREFIX}:minute:{user_id}:{operation}:{current_time // 60}"
        minute_count = self._get_counter(minute_key)
        
        if minute_count >= ScoreSecurityConfig.MAX_REQUESTS_PER_MINUTE:
            return {
                "allowed": False,
                "reason": "每分钟请求次数超限",
                "retry_after": 60 - (current_time % 60),
                "current_count": minute_count,
                "limit": ScoreSecurityConfig.MAX_REQUESTS_PER_MINUTE
            }
        
        # 检查每小时限制
        hour_key = f"{ScoreCacheConfig.RATE_LIMIT_PREFIX}:hour:{user_id}:{operation}:{current_time // 3600}"
        hour_count = self._get_counter(hour_key)
        
        if hour_count >= ScoreSecurityConfig.MAX_REQUESTS_PER_HOUR:
            return {
                "allowed": False,
                "reason": "每小时请求次数超限",
                "retry_after": 3600 - (current_time % 3600),
                "current_count": hour_count,
                "limit": ScoreSecurityConfig.MAX_REQUESTS_PER_HOUR
            }
        
        # 检查每日限制
        day_key = f"{ScoreCacheConfig.RATE_LIMIT_PREFIX}:day:{user_id}:{operation}:{current_time // 86400}"
        day_count = self._get_counter(day_key)
        
        if day_count >= ScoreSecurityConfig.MAX_REQUESTS_PER_DAY:
            return {
                "allowed": False,
                "reason": "每日请求次数超限",
                "retry_after": 86400 - (current_time % 86400),
                "current_count": day_count,
                "limit": ScoreSecurityConfig.MAX_REQUESTS_PER_DAY
            }
        
        return {
            "allowed": True,
            "minute_count": minute_count,
            "hour_count": hour_count,
            "day_count": day_count
        }
    
    def increment_counter(self, user_id: str, operation: str = "default") -> Dict[str, int]:
        """
        增加用户操作计数
        :param user_id: 用户ID
        :param operation: 操作类型
        :return: 更新后的计数
        """
        current_time = int(time.time())
        
        minute_key = f"{ScoreCacheConfig.RATE_LIMIT_PREFIX}:minute:{user_id}:{operation}:{current_time // 60}"
        hour_key = f"{ScoreCacheConfig.RATE_LIMIT_PREFIX}:hour:{user_id}:{operation}:{current_time // 3600}"
        day_key = f"{ScoreCacheConfig.RATE_LIMIT_PREFIX}:day:{user_id}:{operation}:{current_time // 86400}"
        
        minute_count = self._increment_counter(minute_key, 60)
        hour_count = self._increment_counter(hour_key, 3600)
        day_count = self._increment_counter(day_key, 86400)
        
        return {
            "minute_count": minute_count,
            "hour_count": hour_count,
            "day_count": day_count
        }
    
    def _get_counter(self, key: str) -> int:
        """获取计数器值"""
        try:
            if self.cache_manager and self.cache_manager.redis_client:
                result = self.cache_manager.redis_client.get(key)
                return int(result) if result else 0
            else:
                return self.memory_counters.get(key, 0)
        except Exception as e:
            self.log.error(f"获取计数器失败: {e}")
            return 0
    
    def _increment_counter(self, key: str, ttl: int) -> int:
        """增加计数器值"""
        try:
            if self.cache_manager and self.cache_manager.redis_client:
                # 使用Redis管道提高性能
                pipe = self.cache_manager.redis_client.pipeline()
                pipe.incr(key)
                pipe.expire(key, ttl)
                results = pipe.execute()
                return results[0]
            else:
                # 内存计数器需要加锁
                lock = self._get_memory_lock(key)
                with lock:
                    current = self.memory_counters.get(key, 0)
                    new_count = current + 1
                    self.memory_counters[key] = new_count
                    return new_count
        except Exception as e:
            self.log.error(f"增加计数器失败: {e}")
            return 1
    
    def _get_memory_lock(self, key: str) -> threading.Lock:
        """获取内存锁"""
        if key not in self.memory_locks:
            self.memory_locks[key] = threading.Lock()
        return self.memory_locks[key]
    
    def reset_user_limits(self, user_id: str, operation: str = "default"):
        """重置用户限制（管理员功能）"""
        try:
            current_time = int(time.time())
            
            keys_to_delete = [
                f"{ScoreCacheConfig.RATE_LIMIT_PREFIX}:minute:{user_id}:{operation}:{current_time // 60}",
                f"{ScoreCacheConfig.RATE_LIMIT_PREFIX}:hour:{user_id}:{operation}:{current_time // 3600}",
                f"{ScoreCacheConfig.RATE_LIMIT_PREFIX}:day:{user_id}:{operation}:{current_time // 86400}"
            ]
            
            if self.cache_manager and self.cache_manager.redis_client:
                self.cache_manager.redis_client.delete(*keys_to_delete)
            else:
                for key in keys_to_delete:
                    self.memory_counters.pop(key, None)
            
            self.log.info(f"重置用户限制成功: {user_id}, {operation}")
            
        except Exception as e:
            self.log.error(f"重置用户限制失败: {e}")
    
    def get_user_stats(self, user_id: str, operation: str = "default") -> Dict[str, Any]:
        """获取用户统计信息"""
        current_time = int(time.time())
        
        minute_key = f"{ScoreCacheConfig.RATE_LIMIT_PREFIX}:minute:{user_id}:{operation}:{current_time // 60}"
        hour_key = f"{ScoreCacheConfig.RATE_LIMIT_PREFIX}:hour:{user_id}:{operation}:{current_time // 3600}"
        day_key = f"{ScoreCacheConfig.RATE_LIMIT_PREFIX}:day:{user_id}:{operation}:{current_time // 86400}"
        
        return {
            "user_id": user_id,
            "operation": operation,
            "minute_count": self._get_counter(minute_key),
            "hour_count": self._get_counter(hour_key),
            "day_count": self._get_counter(day_key),
            "limits": {
                "minute_limit": ScoreSecurityConfig.MAX_REQUESTS_PER_MINUTE,
                "hour_limit": ScoreSecurityConfig.MAX_REQUESTS_PER_HOUR,
                "day_limit": ScoreSecurityConfig.MAX_REQUESTS_PER_DAY
            }
        }
    
    def cleanup_expired_counters(self):
        """清理过期的内存计数器"""
        if self.cache_manager and self.cache_manager.redis_client:
            return  # Redis自动处理过期
        
        try:
            current_time = int(time.time())
            keys_to_remove = []
            
            for key in self.memory_counters.keys():
                if key.startswith(ScoreCacheConfig.RATE_LIMIT_PREFIX):
                    # 解析时间戳
                    parts = key.split(":")
                    if len(parts) >= 5:
                        timestamp = int(parts[-1])
                        time_unit = parts[1]  # minute, hour, day
                        
                        # 检查是否过期
                        if time_unit == "minute" and current_time // 60 > timestamp:
                            keys_to_remove.append(key)
                        elif time_unit == "hour" and current_time // 3600 > timestamp:
                            keys_to_remove.append(key)
                        elif time_unit == "day" and current_time // 86400 > timestamp:
                            keys_to_remove.append(key)
            
            for key in keys_to_remove:
                del self.memory_counters[key]
            
            if keys_to_remove:
                self.log.info(f"清理了{len(keys_to_remove)}个过期的限流计数器")
                
        except Exception as e:
            self.log.error(f"清理过期计数器失败: {e}")


def rate_limit(operation: str = "default", enabled: bool = True):
    """
    限流装饰器
    :param operation: 操作类型
    :param enabled: 是否启用限流
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            if not enabled:
                return func(*args, **kwargs)
            
            # 尝试从参数中获取用户ID
            user_id = None
            if 'openId' in kwargs:
                user_id = kwargs['openId']
            elif len(args) > 1 and isinstance(args[1], str):
                user_id = args[1]
            
            if not user_id:
                # 如果无法获取用户ID，跳过限流检查
                return func(*args, **kwargs)
            
            # 获取限流器实例（假设在类中使用）
            limiter = getattr(args[0], 'rate_limiter', None) if args else None
            if not limiter:
                return func(*args, **kwargs)
            
            # 检查限流
            check_result = limiter.check_rate_limit(user_id, operation)
            if not check_result["allowed"]:
                raise Exception(f"请求频率超限: {check_result['reason']}, 请{check_result['retry_after']}秒后重试")
            
            # 执行函数
            try:
                result = func(*args, **kwargs)
                # 成功执行后增加计数
                limiter.increment_counter(user_id, operation)
                return result
            except Exception as e:
                # 失败时也增加计数（防止恶意重试）
                limiter.increment_counter(user_id, operation)
                raise e
        
        return wrapper
    return decorator


class ScoreSecurityManager:
    """积分安全管理器"""
    
    def __init__(self, cache_manager=None):
        self.log = DefaultLog().getLogger()
        self.cache_manager = cache_manager
        self.rate_limiter = ScoreRateLimiter(cache_manager)
        self.blacklist = set()  # 用户黑名单
        self.suspicious_users = {}  # 可疑用户记录
    
    def is_user_allowed(self, user_id: str) -> bool:
        """检查用户是否被允许操作"""
        return user_id not in self.blacklist
    
    def add_to_blacklist(self, user_id: str, reason: str = ""):
        """添加用户到黑名单"""
        self.blacklist.add(user_id)
        self.log.warning(f"用户已加入黑名单: {user_id}, 原因: {reason}")
    
    def remove_from_blacklist(self, user_id: str):
        """从黑名单移除用户"""
        self.blacklist.discard(user_id)
        self.log.info(f"用户已从黑名单移除: {user_id}")
    
    def check_suspicious_activity(self, user_id: str, operation: str, score: int) -> bool:
        """检查可疑活动"""
        try:
            # 检查单次操作积分是否异常
            if abs(score) > ScoreSecurityConfig.SUSPICIOUS_OPERATION_THRESHOLD:
                self._record_suspicious_activity(user_id, f"单次操作积分异常: {score}")
                return True
            
            # 检查操作频率
            stats = self.rate_limiter.get_user_stats(user_id, operation)
            if stats["minute_count"] > ScoreSecurityConfig.MAX_REQUESTS_PER_MINUTE * 0.8:
                self._record_suspicious_activity(user_id, f"操作频率异常: {stats['minute_count']}/分钟")
                return True
            
            return False
            
        except Exception as e:
            self.log.error(f"检查可疑活动失败: {e}")
            return False
    
    def _record_suspicious_activity(self, user_id: str, reason: str):
        """记录可疑活动"""
        current_time = time.time()
        if user_id not in self.suspicious_users:
            self.suspicious_users[user_id] = []
        
        self.suspicious_users[user_id].append({
            "reason": reason,
            "timestamp": current_time
        })
        
        # 如果可疑活动过多，自动加入黑名单
        recent_activities = [
            activity for activity in self.suspicious_users[user_id]
            if current_time - activity["timestamp"] < 3600  # 1小时内
        ]
        
        if len(recent_activities) >= 5:  # 1小时内5次可疑活动
            self.add_to_blacklist(user_id, f"可疑活动过多: {len(recent_activities)}次")
    
    def get_security_stats(self) -> Dict[str, Any]:
        """获取安全统计信息"""
        return {
            "blacklist_count": len(self.blacklist),
            "suspicious_users_count": len(self.suspicious_users),
            "rate_limiter_stats": {
                "memory_counters": len(self.rate_limiter.memory_counters),
                "memory_locks": len(self.rate_limiter.memory_locks)
            }
        }
