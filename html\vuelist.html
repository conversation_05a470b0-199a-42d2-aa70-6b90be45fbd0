<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Title</title>
    <script src="vue2.6.14.min.js"></script>
</head>
<body>
<div id="rank">
    <div v-for="item in randlist" class="tip">
        <div class="num">1</div>
        <div class="avatar" style="background-image: url({{item.userface}})"></div>
        <div class="rightArea">
            <div class="score good">{{item.score}}积分</div>
            <div class="username">{{item.username}}</div>
        </div>
    </div>
</div>
</body>
<script>
    var vm = new Vue({
            el: '#rank',
            data: {
                randlist: [{"userName": "\u7a0b\u5e8f\u733f\u7684\u9000\u4f11\u751f\u6d3b", "userface": "https://i1.hdslb.com/bfs/face/d5addd247a6c2b2c3ed7b25d439c13f02505fac1.jpg", "score": 5555}, {"userName": "keke", "userface": "http://gips0.baidu.com/it/u=3602773692,1512483864&fm=3028&app=3028&f=JPEG&fmt=auto?w=960&h=1280", "score": 243}, {"userName": "\u5c0f\u5929\u4f7f", "userface": "http://gips3.baidu.com/it/u=3886271102,3123389489&fm=3028&app=3028&f=JPEG&fmt=auto?w=1280&h=960", "score": 300}]
            },
            methods: {
                loadData() {
                    this.randlist = [{"userName": "\u7a0b\u5e8f\u733f\u7684\u9000\u4f11\u751f\u6d3b", "userface": "https://i1.hdslb.com/bfs/face/d5addd247a6c2b2c3ed7b25d439c13f02505fac1.jpg", "score": 5555}, {"userName": "keke", "userface": "http://gips0.baidu.com/it/u=3602773692,1512483864&fm=3028&app=3028&f=JPEG&fmt=auto?w=960&h=1280", "score": 243}, {"userName": "\u5c0f\u5929\u4f7f", "userface": "http://gips3.baidu.com/it/u=3886271102,3123389489&fm=3028&app=3028&f=JPEG&fmt=auto?w=1280&h=960", "score": 300}]
                }
            }
        })
</script>
</html>
