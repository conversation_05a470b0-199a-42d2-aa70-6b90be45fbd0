J:\ai\�������\ai-yinmei\func\llm\llm_core.c    llm_core.py     
__init__        llm_core.LLmCore.__init__       
aiResponseTry   llm_core.LLmCore.aiResponseTry  
ai_response     llm_core.LLmCore.ai_response    response        
insert_chat     llm_core.LLmCore.insert_chat    
analysis_content        llm_core.LLmCore.analysis_content       
check_answer    llm_core.LLmCore.check_answer   
check_welcome_room      llm_core.LLmCore.check_welcome_room     
msg_deal        llm_core.LLmCore.msg_deal       llm_core                
Interpreter change detected - this module can only be loaded into one interpreter per process.  name    __loader__  loader      __file__    origin      __package__ parent      __path__        submodule_search_locations      Module 'llm_core' has already been imported. Re-initialisation is not supported.        builtins        cython_runtime  __builtins__    init llm_core   %s() got multiple values for keyword argument '%U'      %.200s() keywords must be strings       %s() got an unexpected keyword argument '%U'    exactly s       %.200s() takes %.8s %zd positional argument%.1s (%zd given)     name '%U' is not defined         while calling a Python object  NULL result without error in PyObject_Call      join() result is too long for a Python string   local variable '%s' referenced before assignment        '%.200s' object is unsliceable  cannot fit '%.200s' into an index-sized integer '%.200s' object is not subscriptable    cannot import name %S   strings are too large to concat _cython_3_0_11  Shared Cython type %.200s is not a type object  Shared Cython type %.200s has the wrong size, try recompiling   keywords must be strings        __name__ must be set to a string object __qualname__ must be set to a string object     function's dictionary may not be deleted        setting function's dictionary to a non-dict     __defaults__ must be set to a tuple object              changes to cyfunction.__defaults__ will not currently affect the values used in function calls  __kwdefaults__ must be set to a dict object     changes to cyfunction.__kwdefaults__ will not currently affect the values used in function calls        __annotations__ must be set to a dict object    func_doc        __doc__ func_name       __name__        __qualname__    func_dict       __dict__        func_globals    __globals__     func_closure    __closure__     func_code       __code__        func_defaults   __defaults__    __kwdefaults__  __annotations__ _is_coroutine   __module__      __reduce__      Bad call flags for CyFunction   <cyfunction %U at %p>   %.200s() takes no arguments (%zd given) %.200s() takes exactly one argument (%zd given) %.200s() takes no keyword arguments     unbound method %.200S() needs an argument       %.200s() needs an argument      _cython_3_0_11.cython_function_or_method        %s (%s:%d)      does not match          compile time Python version %d.%d of module '%.100s' %s runtime version %d.%d   
find_user       func.score.oper_score   local_llm_type  粉丝  ttsCore decode  info    other   exception       
"完成 analysis_thread insert_chat     actionOper      all_content:    LLmCore cline_in_traceback      LLmData func.user.user_db       str_data        threading       func.sing.sing_core text1       random_number   func.tools.singleton_mode       new_userlist    ��   aiResponseTry   answers_thread  singCore        llm_core        func.tts.tts_core       character       AnswerList      __prepare__     的直播间,你们终于回来看我了,想死你们了。     relation_query  ai_response     memoryCore  Tgw 思考问题"   You     func.memory.memory_core CommonData      同学来到    chatDB  player  text2   fastGpt line    func.gobal.data jsonData        __main__        filter_html_tags        has_string_reg_list     LLmCore.ai_response     extractCore 跳舞      __qualname__    func.analysis.analysis_core     iter_lines      fastgpt draw    __module__  画画      relation    temp2   self        username        WelcomeData     func.entrance.entrance_core     split_content   extract_text_between_chevrons   __dict__        ��   _initializing   AnalysisCore    J:\ai\吟美打包\ai-yinmei\func\llm\llm_core.py   prompt  tgw 欢迎" :       func.vtuber.action_oper 闲置  memory_add_batch    top_n       Ai_Name llm_json        analysisCore    str     strftime    target  ChatDB  dance   切换      is_llm_welcome  ]的回复已存入队列，当前剩余问题数:      ][AI回复]     memData FastGpt clear   random  rooms   switch  count2  ['      socre_thread    func.analysis.extract_core      document        _is_coroutine   super   is_ai_ready     __spec__        msg_deal    位 切歌  now     numstr2 start   LLmCore.msg_deal    \(  choices %Y-%m-%d %H:%M:%S   换装  intent              
        如果AI没有在生成回复且队列中还有问题 则创建一个生成的线程
        :return:
                 0123456789abcdef0123456789ABCDEF    uuid4       func.tools.string_util  __import__      ]System>>[  query       extract log     welcomeData qsize               00010203040506070809101112131415161718192021222324252627282930313233343536373839404142434445464748495051525354555657585960616263646566676869707172737475767778798081828384858687888990919293949596979899        __doc__ __class_getitem__   《 content numstr1 \)      ObsInit __set_name__    func.obs.obs_init       response        EntranceCore    status  搜索  end     oper_score  delta   uid func.log.default_log    check_answer    submitTime      replace TTsCore re  name        女仆版       DefaultLog      __init_subclass__       insert_chat_thread  temp1       mpv.exe llmData split_limit     Aileen Voracious        public_sentiment_key            的直播间,跪求关注一下,加QQ群【27831318】大家讨论一下吧。  ']      老粉来到    finish_reason   ]用户对话：    唱歌      assistant_tts_say       check_welcome_room  get chat    datetime        ExtractCore     __annotations__ channel num     keke history split_str   data        entranceCore    search  show_text       LLmCore.check_welcome_room  text        __test__    stop        Ai意图：     SingCore        【ai_response】发生了异常：     func.tools.decorator_mode   data:   B站        voiceType   sing        LLmCore.aiResponseTry   搜图  json    操作指令--  all_content [   |   e   commonData      extractData     newUserlistStr  *       StringUtil  memRs       is_contain      is_index_contain_string loads   oldUserlistStr  uface   t       func.llm.chat_db    utf-8   ^   isSing  put     rfind_index_contain_string      LLmCore.__init__        llm_prompt  llm empty   记录聊天异常: UserDB      chatStatus      
        从问题队列中提取一条，生成回复并存入回复队列中
        :return:
            Thread      LLmCore.analysis_content    .   clothing        QuestionList    AutoChange      __metaclass__   uuid    randrange       intent_analysis text-generation-webui   getLogger   ?   扩展性：画画、唱歌、跳舞:   operScore       question        WelcomeList 聊天  obs args    lanuage current_question_count  [DONE]  LLmCore.insert_chat     __init__        traceid 状态提示    func.llm.tgw    jsonStr strip   怒怼版       asyncio.coroutines      LLmCore.check_answer    describe        func.llm.fastgpt        old_userlist    __name__    get_ws      MemoryCore      queryExtract    user_name       relations       response_json   ]   temp        singleton   cmd "       analysis_content                00010203040506071011121314151617202122232425262730313233343536374041424344454647505152535455565760616263646566677071727374757677    count1  》 stream_content  userDB  ActionOper      memory_search   OperScore