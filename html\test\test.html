<!DOCTYPE html>
<html lang="zh">
	<head>
		<meta charset="utf-8">
		<meta http-equiv="X-UA-Compatible" content="IE=edge">
		<meta name="viewport" content="width=device-width,initial-scale=1">
		<meta name="referrer" content="no-referrer">
		<link rel="icon" href="/favicon.ico">
		<title>chat</title>
		<style>
            /* Transparent background */
            body {
                background-color: transparent !important;
            }
			#chat {
				width: 1200px; height:200px; overflow: hidden; 
				background-color: rgba(0, 0, 0, 0.7);
				padding: 10px; -webkit-border-radius: 15px; border: solid 1px #ffffff; 
            }
			#content {
                width: auto; height:auto;
            }
			#content .area{
			    color: white; line-height: 43px; 
				font-size: 32px; font-weight: bold; word-wrap: break-word; overflow:hidden;
				letter-spacing:5px; font-family:"微软雅黑";
			}
			#content .area .end{
			    animation: blink 1s linear infinite;
			}
			@keyframes blink {
				0% {
					opacity: 0;
				}
				50% {
					opacity: 1;
				}
				100% {
					opacity: 0;
				}
			}
        </style>
		<script type="text/javascript" src="jquery-3.7.1.min.js"></script>
	</head>
	<body>
		<div id="chat">
			  <div id="content">
				<div class="area">
					<span class="font"></span><span class="end"></span>
				</div>
			  </div>
		</div>
		<div id="tip"></div>
		<script type="text/javascript">
            var line = "你好啊<br>第三方都是<br>".match("<br>").length + 1;
            console.log(line)

		</script>
	</body>
</html>