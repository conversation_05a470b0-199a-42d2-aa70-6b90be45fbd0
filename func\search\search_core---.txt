J:\ai\�������\tt2\search_core.c        search_core.py  __init__        search_core.SearchCore.__init__ check_text_search       search_core.SearchCore.check_text_search        baidu_web_search        search_core.SearchCore.baidu_web_search msg_deal        search_core.SearchCore.msg_deal search_core             Interpreter change detected - this module can only be loaded into one interpreter per process.  name    __loader__  loader      __file__    origin      __package__ parent      __path__        submodule_search_locations      Module 'search_core' has already been imported. Re-initialisation is not supported.     builtins        cython_runtime  __builtins__    init search_core        %s() got multiple values for keyword argument '%U'      %.200s() keywords must be strings       %s() got an unexpected keyword argument '%U'    exactly s       %.200s() takes %.8s %zd positional argument%.1s (%zd given)      while calling a Python object  NULL result without error in PyObject_Call      join() result is too long for a Python string   name '%U' is not defined        strings are too large to concat '%.200s' object is unsliceable  cannot import name %S   _cython_3_0_11  Shared Cython type %.200s is not a type object  Shared Cython type %.200s has the wrong size, try recompiling   keywords must be strings        __name__ must be set to a string object __qualname__ must be set to a string object     function's dictionary may not be deleted        setting function's dictionary to a non-dict     __defaults__ must be set to a tuple object      changes to cyfunction.__defaults__ will not currently affect the values used in function calls  __kwdefaults__ must be set to a dict object     changes to cyfunction.__kwdefaults__ will not currently affect the values used in function calls        __annotations__ must be set to a dict object    func_doc        __doc__ func_name       __name__        __qualname__    func_dict       __dict__        func_globals    __globals__     func_closure    __closure__     func_code       __code__        func_defaults   __defaults__    __kwdefaults__  __annotations__ _is_coroutine   __module__      __reduce__      Bad call flags for CyFunction   <cyfunction %U at %p>   %.200s() takes no arguments (%zd given) %.200s() takes exactly one argument (%zd given) %.200s() takes no keyword arguments     unbound method %.200S() needs an argument       %.200s() needs an argument      _cython_3_0_11.cython_function_or_method        %s (%s:%d)      does not match          compile time Python version %d.%d of module '%.100s' %s runtime version %d.%d   ]重置提问:  searchStr   info    查询      default cline_in_traceback      LLmData SearchCore.__init__     func.tools.singleton_mode       �� �   check_text_search       abstract        __prepare__     "中提取"     defaultConfig   func.gobal.data SearchData      __main__        has_string_reg_list     SearchTextList  search_first_string .   __qualname__    func.config.default_config      __module__      ]搜索词：   search results：(total[{}]items.)  self        username        __dict__        X� �   prompt  搜索  llm_json        SearchCore.baidu_web_search     func.search.baidu_websearch ^(  J:\ai\吟美打包\tt2\search_core.py   switch  text_search_json    ?   _is_coroutine   super   msg_deal        SearchCore  intent      func.tools.string_util  __import__  config  query   log __doc__ content SearchCore.msg_deal     __set_name__    status  [   uid func.log.default_log    SearchCore.check_text_search    replace __name__        DefaultLog      __init_subclass__       BaiduWebsearch  llmData get_config      num_results ;   )       baiduWebsearch  get pa  channel 查一下       baidu_web_search    num debug   search  text    __test__        func.tools.decorator_mode   
   StringUtil      searchNum       is_contain  uface   put is_SearchText   llm_prompt  empty   ^   results QuestionList    __metaclass__   getLogger   |   search_core     __init__        traceid 帮我在答案"    strip   
   asyncio.coroutines      searchData  s   queryExtract    user_name   format      singleton       "的信息  res