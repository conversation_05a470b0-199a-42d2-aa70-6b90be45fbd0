J:\ai\�������\ai-yinmei\func\llm\chat_db.c     chat_db.py      __init__        chat_db.ChatDB.__init__ insert_chat     chat_db.ChatDB.insert_chat      all_chat        chat_db.ChatDB.all_chat insert_request  chat_db.ChatDB.insert_request   find_chat_list_page     chat_db.ChatDB.find_chat_list_page      chat_db Interpreter change detected - this module can only be loaded into one interpreter per process.  name    __loader__  loader      __file__    origin      __package__ parent      __path__        submodule_search_locations      Module 'chat_db' has already been imported. Re-initialisation is not supported. builtins        cython_runtime  __builtins__    init chat_db    %s() got multiple values for keyword argument '%U'      %.200s() keywords must be strings       %s() got an unexpected keyword argument '%U'    exactly s       %.200s() takes %.8s %zd positional argument%.1s (%zd given)     name '%U' is not defined         while calling a Python object  NULL result without error in PyObject_Call      cannot import name %S   _cython_3_0_11  Shared Cython type %.200s is not a type object  Shared Cython type %.200s has the wrong size, try recompiling   keywords must be strings        __name__ must be set to a string object __qualname__ must be set to a string object     function's dictionary may not be deleted        setting function's dictionary to a non-dict     __defaults__ must be set to a tuple object      changes to cyfunction.__defaults__ will not currently affect the values used in function calls  __kwdefaults__ must be set to a dict object     changes to cyfunction.__kwdefaults__ will not currently affect the values used in function calls        __annotations__ must be set to a dict object    func_doc        __doc__ func_name       __name__        __qualname__    func_dict       __dict__        func_globals    __globals__     func_closure    __closure__     func_code       __code__        func_defaults   __defaults__    __kwdefaults__  __annotations__ _is_coroutine   __module__      __reduce__      Bad call flags for CyFunction   <cyfunction %U at %p>   %.200s() takes no arguments (%zd given) %.200s() takes exactly one argument (%zd given) %.200s() takes no keyword arguments     unbound method %.200S() needs an argument       %.200s() needs an argument      _cython_3_0_11.cython_function_or_method        %s (%s:%d)      does not match          compile time Python version %d.%d of module '%.100s' %s runtime version %d.%d   chatlistDB  *   insert_chat ?   cline_in_traceback      ChatDB.all_chat $ne     func.tools.singleton_mode       H� �   db      J:\ai\吟美打包\ai-yinmei\func\llm\chat_db.py        __prepare__ regex   get_db  闲置      func.gobal.data jsonData        __main__        __qualname__    __module__  self        username    conn        __dict__        � �   _initializing   prompt  page_size       chat_db ChatDB  count_documents switch  total_pages     chat_record     _is_coroutine   ChatDB.insert_chat      IGNORECASE  super       __spec__    find    intent      Mongodb $regex  compile __import__  query   log __doc__ current_page    __set_name__    ChatDB.insert_request   status  chatlist        func.log.default_log    submitTime  re  chat_request    __name__        DefaultLog      __init_subclass__       skip_count      ChatDB.find_chat_list_page  limit       ChatDB.__init__ MongodbData     datetime    dumps   data    skip        func.database.mongodb   __test__        func.tools.decorator_mode       insert_request  regex2  openId  __metaclass__   getLogger       all_chat        class_switch    bson.json_util  mongodbData cursor      __init__        total_documents page_number     asyncio.coroutines      find_chat_list_page     singleton       insert_one  .   sort            
        聊天列表查询
        :param username:
        :param page_number:
        :param page_size:
        :return: