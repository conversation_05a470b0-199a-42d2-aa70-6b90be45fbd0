import random
import uuid
from func.score.oper_score import OperScore
from func.user.user_db import UserDB
from func.tools.singleton_mode import singleton
from func.http.bilive import BiliveApi
from func.log.default_log import DefaultLog
from func.config.default_config import defaultConfig
from func.gobal.data import (
    LLmData, IdleData, CommonData, SearchData, 
    DanceData, ImageData, DrawData, BiliDanmakuData
)

@singleton
class IdleCore:
    def __init__(self):
        self.log = DefaultLog().getLogger()
        self.config = defaultConfig().get_config()
        self.biliveApi = BiliveApi()
        self.llmData = LLmData()
        self.idleData = IdleData()
        self.commonData = CommonData()
        self.searchData = SearchData()
        self.danceData = DanceData()
        self.imageData = ImageData()
        self.drawData = DrawData()
        self.biliDanmakuData = BiliDanmakuData()
        self.userDB = UserDB()
        self.operScore = OperScore()
        
        # 闲置任务列表
        self.task_list = [
            {"name": "聊天", "func": self.chat_task},
            {"name": "播报", "func": self.broadcast_task},
            {"name": "积分", "func": self.score_task},
            {"name": "排行榜", "func": self.rank_task}
        ]

    async def rnd_idle_task(self):
        """随机执行闲置任务"""
        self.log.info("执行闲置任务")
        
        if not self.is_ai_ready():
            return
            
        task = random.choice(self.task_list)
        task_name = task["name"]
        self.log.info(f"触发闲置任务: {task_name}")
        
        traceid = str(uuid.uuid4())
        await task["func"](traceid)

    def is_ai_ready(self):
        """检查AI是否准备就绪"""
        # 检查各种状态，确保AI可以执行新任务
        if (self.llmData.is_drawing or 
            self.idleData.is_singing or 
            self.idleData.is_dance or
            self.searchData.is_SearchText or 
            self.searchData.is_SearchImg):
            return False
            
        # 检查闲置时间
        idle_time = getattr(self.idleData, "idle_time", 0)
        limit_time = self.config.get("limit_time", 300)  # 默认5分钟
        
        return idle_time >= limit_time

    async def chat_task(self, traceid):
        """闲聊任务"""
        prompts = [
            "给大家说一个新的笑话。提示：要换一个新的笑话",
            "给大家说一个新的段子。提示：要换一个新的段子"
        ]
        
        prompt = random.choice(prompts)
        prompt = prompt.replace("{Ai_Name}", self.config.get("Ai_Name", ""))
        
        # 生成回复
        response = await self.llmData.query(
            prompt=prompt,
            traceid=traceid
        )
        
        if response:
            await self.biliveApi.put(text=response, traceid=traceid)

    async def broadcast_task(self, traceid):
        """播报任务"""
        if not hasattr(self.commonData, "broadcast_list") or not self.commonData.broadcast_list:
            return
            
        broadcast = random.choice(self.commonData.broadcast_list)
        if isinstance(broadcast, dict) and "text" in broadcast:
            text = broadcast["text"]
            text = text.replace("{Ai_Name}", self.config.get("Ai_Name", ""))
            await self.biliveApi.put(text=text, traceid=traceid)

    async def score_task(self, traceid):
        """积分任务"""
        # 获取随机用户
        user = await self.userDB.find_user(
            room_id=self.config.get("room_id"),
            room_uid=self.config.get("room_uid")
        )
        
        if not user or "username" not in user:
            return
            
        # 获取用户积分
        score_info = await self.operScore.msg_deal_score_rank(
            uid=user.get("uid", ""),
            username=user["username"],
            traceid=traceid
        )
        
        if score_info:
            await self.biliveApi.put(text=score_info, traceid=traceid)

    async def rank_task(self, traceid):
        """排行榜任务"""
        # 获取贡献榜数据
        rank_data = await self.biliveApi.queryContributionRank(
            limit=5,
            traceid=traceid
        )
        
        if not rank_data:
            return
            
        # 构建排行榜消息
        rank_msg = "当前贡献榜前5名：\n"
        for idx, item in enumerate(rank_data, 1):
            username = item.get("username", "未知用户")
            score = item.get("score", 0)
            rank_msg += f"第{idx}名: {username} - {score}分\n"
        
        await self.biliveApi.put(text=rank_msg, traceid=traceid)