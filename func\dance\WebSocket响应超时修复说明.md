# WebSocket响应超时问题修复说明

## 问题描述

用户报告：发送WebSocket消息后，服务端没有响应，客户端卡住等待。

从日志看到：
```
🔄 check_connection...
🔄 check_connection success...
🔄 send_request send...
```

分析：消息发送成功，但在等待服务端响应时阻塞。

## 问题原因

1. **服务端无响应**：UPBGE MMD服务端可能处理请求时出错或阻塞
2. **无超时机制**：客户端的`await websocket.recv()`没有超时限制
3. **BGE线程问题**：UPBGE的BGE操作可能在异步环境中出现问题

## 修复方案

### 1. 添加接收超时机制

**文件**: `func/dance/websocket_client.py`

**修复前**:
```python
response = await self.websocket.recv()
```

**修复后**:
```python
try:
    response = await asyncio.wait_for(
        self.websocket.recv(), 
        timeout=30.0  # 30秒超时
    )
    print("🔄 send_request success...")
    return json.loads(response)
except asyncio.TimeoutError:
    print("❌ send_request timeout - 服务端响应超时")
    self.is_connected = False  # 标记连接断开
    return {"error": "服务端响应超时"}
```

### 2. 重连后的重试机制优化

同样添加超时保护：
```python
try:
    response = await asyncio.wait_for(
        self.websocket.recv(), 
        timeout=30.0
    )
    print("🔄 send_request retry success...")
    return json.loads(response)
except asyncio.TimeoutError:
    print("❌ send_request retry timeout")
    return {"error": "重连后响应超时"}
```

### 3. 增强请求日志

添加请求类型日志，便于调试：
```python
print(f"🔄 发送请求: {request_type}")
```

## 诊断工具

创建了 `func/dance/debug_mmd_connection.py` 调试工具：

### 使用方法

```bash
cd /path/to/project
python func/dance/debug_mmd_connection.py
```

### 功能

1. **端口文件检查**：检查`mmd_server_port.txt`是否存在
2. **端口扫描**：扫描8765-8774端口范围
3. **TCP连接测试**：验证端口是否可达
4. **WebSocket连接测试**：测试WebSocket握手和通信
5. **完整客户端测试**：测试MMD客户端完整功能

### 示例输出

```
🔧 MMD连接诊断工具
==================================================

📄 检查端口文件: mmd_server_port.txt
✅ 端口文件存在: localhost:8765

🔍 TCP连接测试...
✅ TCP端口 localhost:8765 可达

🔍 WebSocket连接测试...
🔍 测试WebSocket连接: ws://localhost:8765
✅ WebSocket连接成功
📤 发送测试消息成功
📥 收到响应: 234 字符
✅ 响应解析成功: ['status']
✅ WebSocket连接正常

🎉 诊断完成！
```

## 服务端问题排查

如果诊断工具显示连接正常但仍有响应问题，可能的原因：

### 1. UPBGE服务端阻塞

**检查方法**：在UPBGE中运行
```python
debug_mmd_system()
```

**可能的问题**：
- BGE对象被释放
- MMD组件未正确初始化
- 异步任务死锁

### 2. BGE线程安全问题

**解决方案**：
- 确保BGE操作在主线程执行
- 使用`_execute_bge_operation`包装器
- 检查游戏会话是否重启

### 3. 服务端日志检查

在UPBGE控制台查看错误信息：
```python
server = get_mmd_server()
if server:
    print(f"服务器状态: {server.is_running}")
    print(f"活跃组件: {len(server.active_mmd_components)}")
```

## 应急措施

### 1. 重启服务端

在UPBGE中：
```python
force_restart_mmd_server()
```

### 2. 清理端口占用

```python
cleanup_mmd_ports()
```

### 3. 手动重连客户端

```python
client = MMDWebSocketClient()
success = await client.manual_reconnect(8765)
```

## 预防措施

### 1. 定期心跳检查

- 心跳间隔：45秒（避免与其他心跳冲突）
- 心跳超时：15秒
- 最大失败次数：3次

### 2. 连接质量监控

```python
connection_info = client.get_connection_info()
print(f"连接质量: {connection_info['connection_quality']}")
```

### 3. 自动重连机制

- 指数退避重连
- 智能端口发现
- 最大重连次数限制

## 总结

通过添加超时机制和诊断工具，解决了WebSocket响应卡住的问题：

1. ✅ **超时保护**：30秒响应超时，避免无限等待
2. ✅ **诊断工具**：快速检查连接和服务端状态
3. ✅ **错误恢复**：自动重连和状态恢复
4. ✅ **监控机制**：连接质量和心跳监控

如果问题仍然存在，使用诊断工具确定具体问题位置，然后针对性解决。 