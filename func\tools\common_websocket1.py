import asyncio
import json
import time
from threading import Thread
import websockets
from func.tools.singleton_mode import singleton
from func.log.default_log import DefaultLog

@singleton
class CommonWebsocket:
    def __init__(self):
        self.clients = set()
        self._initializing = False
        self._server_started = False
        self._server = None
        self._loop = None
        self._thread = None
        self.log = DefaultLog().getLogger()
        self.host = "0.0.0.0"
        self.port = 18765

    async def handler(self, websocket, path=None):
        """处理WebSocket连接"""
        client_address = websocket.remote_address
        self.log.info(f"WebSocket客户端连接: {client_address}")
        self.clients.add(websocket)

        try:
            async for message in websocket:
                self.log.debug(f"收到消息: {message}")
                # 可以在这里处理客户端发送的消息
                # await self.broadcast(message)
        except websockets.exceptions.ConnectionClosed:
            self.log.info(f"WebSocket客户端断开连接: {client_address}")
        except Exception as e:
            self.log.error(f"WebSocket处理错误: {e}")
        finally:
            if websocket in self.clients:
                self.clients.remove(websocket)
                self.log.info(f"清理客户端连接: {client_address}")

    async def send_data(self, message):
        """向所有客户端发送数据"""
        if not self.clients:
            return

        if isinstance(message, dict):
            message = json.dumps(message, ensure_ascii=False)
        elif not isinstance(message, str):
            message = str(message)

        await self.broadcast(message)

    async def broadcast(self, message):
        """广播消息给所有连接的客户端"""
        if not self.clients:
            return

        disconnected_clients = set()
        for client in self.clients.copy():
            try:
                await client.send(message)
            except websockets.exceptions.ConnectionClosed:
                disconnected_clients.add(client)
            except Exception as e:
                self.log.error(f"发送消息失败: {e}")
                disconnected_clients.add(client)

        # 清理断开的连接
        self.clients -= disconnected_clients
        if disconnected_clients:
            self.log.info(f"清理了 {len(disconnected_clients)} 个断开的连接")

    def send_data_sync(self, message):
        """同步方式发送数据（从非异步环境调用）"""
        if not self._loop or self._loop.is_closed():
            self.log.warning("WebSocket事件循环未运行，无法发送消息")
            return

        try:
            # 在WebSocket的事件循环中执行发送操作
            asyncio.run_coroutine_threadsafe(self.send_data(message), self._loop)
        except Exception as e:
            self.log.error(f"同步发送消息失败: {e}")

    def run_forever_thread(self):
        """在新线程中运行WebSocket服务器"""
        try:
            self._loop = asyncio.new_event_loop()
            asyncio.set_event_loop(self._loop)

            self.log.info(f"启动WebSocket服务器在 {self.host}:{self.port}")
            start_server = websockets.serve(
                self.handler,
                self.host,
                self.port,
                ping_interval=20,  # 每20秒发送ping
                ping_timeout=10,   # ping超时10秒
                close_timeout=10   # 关闭超时10秒
            )

            self._server = self._loop.run_until_complete(start_server)
            self._server_started = True
            self.log.info(f"WebSocket服务器启动成功，监听端口 {self.port}")

            # 运行事件循环
            self._loop.run_forever()

        except Exception as e:
            self.log.error(f"WebSocket服务器启动失败: {e}")
            self._server_started = False
        finally:
            if self._loop and not self._loop.is_closed():
                self._loop.close()
            self.log.info("WebSocket服务器已停止")

    def start(self):
        """启动WebSocket服务器"""
        if self._server_started:
            self.log.warning("WebSocket服务器已经启动")
            return

        self._thread = Thread(target=self.run_forever_thread, daemon=True)
        self._thread.start()

        # 等待服务器启动
        max_wait = 10  # 最多等待10秒
        wait_time = 0
        while not self._server_started and wait_time < max_wait:
            time.sleep(0.1)
            wait_time += 0.1

        if self._server_started:
            self.log.info("WebSocket服务器启动完成")
        else:
            self.log.error("WebSocket服务器启动超时")

    def stop(self):
        """停止WebSocket服务器"""
        if not self._server_started:
            return

        try:
            if self._loop and not self._loop.is_closed():
                self._loop.call_soon_threadsafe(self._loop.stop)
            self._server_started = False
            self.log.info("WebSocket服务器停止命令已发送")
        except Exception as e:
            self.log.error(f"停止WebSocket服务器失败: {e}")

    def is_running(self):
        """检查WebSocket服务器是否正在运行"""
        return self._server_started and len(self.clients) >= 0

    def get_client_count(self):
        """获取当前连接的客户端数量"""
        return len(self.clients)