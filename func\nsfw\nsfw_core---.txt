J:\ai\�������\ai-yinmei\func\nsfw\nsfw_core.c  nsfw_core.py    __init__        nsfw_core.NsfwCore.__init__     nsfw_deal       nsfw_core.NsfwCore.nsfw_deal    nsfw_fun        nsfw_core.NsfwCore.nsfw_fun     str_filter      nsfw_core.NsfwCore.str_filter   nsfw_core               Interpreter change detected - this module can only be loaded into one interpreter per process.  name    __loader__  loader      __file__    origin      __package__ parent      __path__        submodule_search_locations      Module 'nsfw_core' has already been imported. Re-initialisation is not supported.       builtins        cython_runtime  __builtins__    init nsfw_core  %s() got multiple values for keyword argument '%U'      %.200s() keywords must be strings       %s() got an unexpected keyword argument '%U'    exactly s       %.200s() takes %.8s %zd positional argument%.1s (%zd given)     name '%U' is not defined         while calling a Python object  NULL result without error in PyObject_Call      strings are too large to concat join() result is too long for a Python string   cannot import name %S   _cython_3_0_11  Shared Cython type %.200s is not a type object  Shared Cython type %.200s has the wrong size, try recompiling   keywords must be strings        __name__ must be set to a string object __qualname__ must be set to a string object     function's dictionary may not be deleted        setting function's dictionary to a non-dict     __defaults__ must be set to a tuple object              changes to cyfunction.__defaults__ will not currently affect the values used in function calls  __kwdefaults__ must be set to a dict object     changes to cyfunction.__kwdefaults__ will not currently affect the values used in function calls        __annotations__ must be set to a dict object    func_doc        __doc__ func_name       __name__        __qualname__    func_dict       __dict__        func_globals    __globals__     func_closure    __closure__     func_code       __code__        func_defaults   __defaults__    __kwdefaults__  __annotations__ _is_coroutine   __module__      __reduce__      Bad call flags for CyFunction   <cyfunction %U at %p>   %.200s() takes no arguments (%zd given) %.200s() takes exactly one argument (%zd given) %.200s() takes no keyword arguments     unbound method %.200S() needs an argument       %.200s() needs an argument      _cython_3_0_11.cython_function_or_method        %s (%s:%d)      does not match          compile time Python version %d.%d of module '%.100s' %s runtime version %d.%d   requests        nsfw_lock       鉴黄结果:   headers info    NsfwCore.__init__       nsfwJson        show_image      exception       str_filter      nsfw_fun    filter      default imgb64  img     cline_in_traceback  /input      func.tools.singleton_mode   open        x� �   release nsfw_deal       __prepare__     image_loader    time    b64decode   《 NsfwCore.nsfw_fun       func.gobal.data 次】  verify  __main__        model_weights   J:\ai\吟美打包\ai-yinmei\func\nsfw\nsfw_core.py     __qualname__    Image   __module__  save        J:\ai\ai-yinmei\images\黄图950.jpg    BytesIO self    username        __dict__        H� �   _initializing   io      input_type  prompt  tip 失败  》【nsfw】【重试剩余    NsfwCore    url retryCount      ./porn/ 》【nsfw】鉴黄 nsfw    switch      nsfw_server     _is_coroutine   super   __spec__    post        application/json        data/open_nsfw-weights.npy      func.tools.string_util  __import__  PIL query   log     __doc__ ?       ObsInit __set_name__    func.obs.obs_init   _porn_  status      func.log.default_log    timestamp       acquire nsfwData        nsfw_limit      __name__    yahoo       ,马上退出   NsfwCore.str_filter     DefaultLog      __init_subclass__       Content-Type    绘画图片    NsfwData    _   》【nsfw】   timeout data    __test__        func.tools.decorator_mode   json        鉴黄失败，图片不明确跳出    e   base64      StringUtil      NsfwCore.nsfw_deal      nsfw_core   .jpg    .   __metaclass__   getLogger   *   obs     input_image     filterCh        __init__        完成，发现黄图:  asyncio.coroutines  get_ws  成功      singleton       BASE64_JPEG     发生了异常：