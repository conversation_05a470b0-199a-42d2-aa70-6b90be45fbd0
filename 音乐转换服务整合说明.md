# 音乐转换服务整合说明

## 概述

已成功将 `Web_through.py` 项目整合到主项目中，作为独立的音乐转换服务模块运行。

## 整合内容

### 1. 创建的新文件

#### `func/sing/music_convert_service.py`
- 基于 `Web_through.py` 创建的音乐转换服务模块
- 包装了 `AutoConvertMusic` 的功能
- 提供 Flask Web 服务接口
- 支持单例模式，确保服务唯一性

#### `test_music_integration.py`
- 音乐转换服务的测试脚本
- 验证服务启动和接口功能

#### `install_music_dependencies.py`
- 安装音乐转换服务所需依赖的脚本

### 2. 修改的文件

#### `bot_ym.py`
- 添加了音乐转换服务的导入
- 在 `run_ym()` 函数中添加了服务启动逻辑
- 服务作为独立线程运行在 `127.0.0.1:17171`

#### `func/sing/sing_core.py`
- 导入了本地音乐转换服务
- 修改了 `create_song()` 方法，优先调用本地服务
- 如果本地服务失败，会回退到原有的HTTP请求方式
- 修改了 `check_down_song()` 方法，支持新的文件命名格式
- 修改了 `play_song()` 方法，支持多种文件格式的播放

## 服务架构

```
bot_ym.py (主程序)
├── 启动音乐转换服务线程
│   └── func/sing/music_convert_service.py
│       ├── Flask Web服务 (端口17171)
│       └── AutoConvertMusic包装
└── 唱歌功能
    └── func/sing/sing_core.py
        ├── 优先调用本地服务
        └── 回退到HTTP请求
```

## API接口

音乐转换服务提供以下HTTP接口：

- `GET /status` - 获取转换状态
- `GET /append_song/<song_name>` - 添加转换任务
- `GET /get_audio/<song_name>` - 获取转换后的音频文件

## 配置说明

### SVC配置
```python
svc_config = {
    "model_path": r"sovits4.1\logs\草神\caoshen1_57000.pth",
    "config_path": r"sovits4.1\logs\草神\config.json",
    "cluster_model_path": r"sovits4.1\logs\yinmei\kmeans_10000.pt",
    "cluster_infer_ratio": 0,
    "diffusion_model_path": r"sovits4.1\logs\yinmei\diffusion\model_44000.pt",
    "diffusion_config_path": r"sovits4.1\logs\yinmei\diffusion\config.yaml"
}
```

### 音乐平台
- 支持：kugou, netease, bilibili, youtube
- 默认使用：kugou

### 默认任务配置
```python
default_task_dict = {'ms':'bs-roformer-1296','vr1':'6-HP','vr2': 'De-Echo-Normal'}
```

## 依赖要求

### 必需依赖
- selenium (网页自动化)
- webdriver-manager (浏览器驱动管理)
- pydub (音频处理)
- pedalboard (音频效果)
- flask (Web服务)
- requests (HTTP请求)

### 安装方法
```bash
python install_music_dependencies.py
```

## 使用方法

### 1. 启动服务
运行主程序时会自动启动音乐转换服务：
```bash
python bot_ym.py
```

### 2. 学歌功能
用户可以通过以下方式触发学歌：
- "唱一下 歌曲名"
- "唱一首 歌曲名"
- "唱歌 歌曲名"
- "点歌 歌曲名"
- "点播 歌曲名"

### 3. 工作流程
1. 用户发送学歌请求
2. `sing_core.py` 接收请求
3. 优先调用本地音乐转换服务
4. 如果本地服务不可用，回退到HTTP请求
5. 等待歌曲转换完成
6. 播放转换后的音频

## 错误处理

### 1. 依赖缺失
- 如果 `AutoConvertMusic` 导入失败，服务会记录警告但不会崩溃
- 提供友好的错误信息指导用户安装依赖

### 2. 服务启动失败
- 记录详细的错误日志
- 不影响主程序的其他功能

### 3. 转换失败
- 支持回退到原有的HTTP请求方式
- 提供详细的状态反馈

## 文件结构

```
output/
└── {歌曲名}/
    ├── {歌曲名}_{说话人}.wav  # 最终输出文件
    ├── Vocals_{说话人}.wav   # 人声转换文件
    ├── Vocals.wav            # 原始人声
    ├── Instrumental.wav      # 伴奏
    ├── Chord.wav            # 和声
    └── Echo.wav             # 混响
```

## 注意事项

1. **端口冲突**：确保端口17171未被占用
2. **模型文件**：确保SVC模型文件路径正确
3. **权限问题**：确保有写入output目录的权限
4. **浏览器驱动**：使用selenium时需要安装对应的浏览器驱动
5. **网络连接**：某些音乐平台可能需要网络代理

## 测试

运行测试脚本验证整合是否成功：
```bash
python test_music_integration.py
```

## 故障排除

### 1. 导入错误
- 检查是否安装了所有必需依赖
- 运行 `install_music_dependencies.py`

### 2. 服务启动失败
- 检查端口是否被占用
- 查看日志文件中的详细错误信息

### 3. 转换失败
- 检查模型文件是否存在
- 确认网络连接正常
- 查看AutoConvertMusic的日志

## 更新日志

- 2025-07-23: 完成Web_through.py项目整合
- 支持本地服务优先，HTTP请求回退的双重保障机制
- 添加完整的错误处理和日志记录
