#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
安装音乐转换项目依赖到指定环境
"""
import subprocess
import sys
import os
from pathlib import Path

# 目标环境路径
TARGET_ENV = r"H:/baidudown/AI-YinMei-v2.0.0/MB7-YM2-runtime"
PYTHON_EXE = os.path.join(TARGET_ENV, "python.exe")

def check_environment():
    """检查目标环境是否存在"""
    if not os.path.exists(TARGET_ENV):
        print(f"❌ 目标环境不存在: {TARGET_ENV}")
        return False
    
    if not os.path.exists(PYTHON_EXE):
        print(f"❌ Python可执行文件不存在: {PYTHON_EXE}")
        return False
    
    print(f"✅ 目标环境检查通过: {TARGET_ENV}")
    return True

def install_package(package, retry_count=2, timeout=600):
    """安装单个包，支持重试和错误处理"""
    for attempt in range(retry_count + 1):
        try:
            print(f"📦 正在安装 {package} (尝试 {attempt + 1}/{retry_count + 1})")

            # 基本安装命令
            cmd = [PYTHON_EXE, "-m", "pip", "install", package, "--upgrade"]

            # 对于某些特殊包的处理
            if package.lower() in ["onnxruntime_gpu"]:
                # GPU版本可能需要特殊处理
                cmd.extend(["--extra-index-url", "https://download.pytorch.org/whl/cu118"])
                timeout = 900  # GPU版本需要更长时间
            elif package.lower() in ["torch", "torchvision", "torchaudio"]:
                # PyTorch相关包，使用CPU版本更快
                cmd = [PYTHON_EXE, "-m", "pip", "install", package, "--upgrade", "--index-url", "https://download.pytorch.org/whl/cpu"]
                timeout = 900  # PyTorch需要更长时间
            elif package.lower() in ["fairseq"]:
                # fairseq可能需要从源码安装
                cmd = [PYTHON_EXE, "-m", "pip", "install", "fairseq", "--no-deps"]
                timeout = 1200  # 源码编译需要更长时间
            elif package.lower() in ["pyaudio"]:
                # PyAudio在Windows上比较难安装
                cmd = [PYTHON_EXE, "-m", "pip", "install", "PyAudio", "--only-binary=all"]

            result = subprocess.run(cmd, capture_output=True, text=True, timeout=timeout)
            
            if result.returncode == 0:
                print(f"✅ 成功安装 {package}")
                return True
            else:
                print(f"⚠️ 安装 {package} 失败 (尝试 {attempt + 1}): {result.stderr}")
                
                # 尝试不同的安装策略
                if attempt < retry_count:
                    if "Microsoft Visual C++" in result.stderr:
                        print(f"🔧 尝试使用预编译版本安装 {package}")
                        cmd = [PYTHON_EXE, "-m", "pip", "install", package, "--only-binary=all"]
                    elif "No matching distribution" in result.stderr:
                        print(f"🔧 尝试使用兼容版本安装 {package}")
                        cmd = [PYTHON_EXE, "-m", "pip", "install", package, "--force-reinstall", "--no-deps"]
                    elif package.lower() == "pyaudio":
                        print(f"🔧 尝试安装PyAudio的预编译版本")
                        cmd = [PYTHON_EXE, "-m", "pip", "install", "pipwin"]
                        subprocess.run(cmd, capture_output=True)
                        cmd = [PYTHON_EXE, "-m", "pipwin", "install", "pyaudio"]
                
        except subprocess.TimeoutExpired:
            print(f"⏰ 安装 {package} 超时 (尝试 {attempt + 1})，超时时间: {timeout}秒")
            if attempt < retry_count:
                print(f"🔧 增加超时时间重试...")
                timeout = timeout * 1.5  # 增加超时时间
        except Exception as e:
            print(f"❌ 安装 {package} 时发生异常 (尝试 {attempt + 1}): {e}")
    
    print(f"❌ 最终安装失败: {package}")
    return False

def read_requirements():
    """读取requirements-music.txt文件"""
    try:
        with open("requirements-music.txt", "r", encoding="utf-8") as f:
            packages = [line.strip() for line in f if line.strip() and not line.startswith("#")]
        return packages
    except FileNotFoundError:
        print("❌ 找不到requirements-music.txt文件")
        return []

def install_critical_packages_first():
    """优先安装关键包"""
    critical_packages = [
        "pip",
        "setuptools",
        "wheel",
        "numpy",
        "scipy",
        "torch",
        "torchaudio", 
        "torchvision"
    ]
    
    print("🚀 优先安装关键包...")
    for package in critical_packages:
        install_package(package)

def main():
    """主函数"""
    print("=== 音乐转换项目依赖安装 ===")
    print(f"目标环境: {TARGET_ENV}")
    
    # 检查环境
    if not check_environment():
        return
    
    # 读取依赖列表
    packages = read_requirements()
    if not packages:
        return
    
    print(f"📋 共需要安装 {len(packages)} 个包")
    
    # 优先安装关键包
    install_critical_packages_first()
    
    # 安装统计
    success_count = 0
    failed_packages = []
    
    # 按类别分组安装，避免依赖冲突
    audio_packages = ["audioread", "librosa", "soundfile", "PySoundFile", "sounddevice", "PyAudio", "pydub", "pedalboard"]
    ml_packages = ["torch", "torchvision", "torchaudio", "transformers", "fairseq", "pytorch_lightning"]
    onnx_packages = ["onnx", "onnx2pytorch", "onnxruntime", "onnxruntime_gpu", "onnxsim", "onnxoptimizer"]
    
    package_groups = [
        ("音频处理包", [p for p in packages if p in audio_packages]),
        ("机器学习包", [p for p in packages if p in ml_packages]),
        ("ONNX相关包", [p for p in packages if p in onnx_packages]),
        ("其他包", [p for p in packages if p not in audio_packages + ml_packages + onnx_packages])
    ]
    
    for group_name, group_packages in package_groups:
        if not group_packages:
            continue
            
        print(f"\n📦 安装 {group_name} ({len(group_packages)} 个包)")
        for package in group_packages:
            if install_package(package):
                success_count += 1
            else:
                failed_packages.append(package)
    
    # 安装结果统计
    print(f"\n=== 安装完成 ===")
    print(f"✅ 成功安装: {success_count}/{len(packages)}")
    print(f"❌ 安装失败: {len(failed_packages)}")
    
    if failed_packages:
        print(f"\n失败的包列表:")
        for package in failed_packages:
            print(f"  - {package}")
        
        print(f"\n💡 建议:")
        print(f"1. 手动安装失败的包")
        print(f"2. 检查是否需要安装Visual C++ Build Tools")
        print(f"3. 某些包可能需要特定版本的Python")
        print(f"4. 考虑使用conda安装某些难以安装的包")
    
    print(f"\n🎯 可以运行以下命令测试安装结果:")
    print(f"{PYTHON_EXE} -c \"import torch; import librosa; import flask; print('核心依赖导入成功')\"")

if __name__ == "__main__":
    main()
