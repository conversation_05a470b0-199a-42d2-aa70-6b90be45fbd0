from flask import jsonify, request
from controller.baseview import BaseView
from func.config.yml_oper import YmlOper
from func.config.default_config import defaultConfig

config = defaultConfig()

class ReadYml(BaseView):
    """读取yml配置控制器"""
    
    def post(self):
        """
        读取yml配置
        请求数据格式: ['analysis.extract.fastgpt_url', 'translate.HttpProxies', 'welcome.welcome_not_allow']
        """
        try:
            data = request.get_json()
            result = YmlOper.read_yml(config, data)
            print(result)
            return jsonify({
                'status': '成功',
                'data': result
            })
        except Exception as e:
            print(e)
            print(data)
            return jsonify({
                'status': '失败',
                'message': str(e)
            })

class WriteYml(BaseView):
    """写入yml配置控制器"""
    
    def post(self):
        """
        写入yml配置
        请求数据格式: {'analysis.extract.fastgpt_url':'http://127.0.0.1','translate.HttpProxies':'tttt'}
        """
        try:
            data = request.get_json()
            print(data)
            YmlOper.write_yml(config, data, "config/config.yaml")
            return jsonify({
                'status': '成功'
            })
        except Exception as e:
            print(e)
            return jsonify({
                'status': '失败',
                'message': str(e)
            })

class GetConfig(BaseView):
    """获取默认配置控制器"""
    
    def get(self):
        """获取默认配置"""
        try:
            return jsonify({
                'status': '成功',
                'data': defaultConfig
            })
        except Exception as e:
            print(e)
            return jsonify({
                'status': '失败',
                'message': str(e)
            })