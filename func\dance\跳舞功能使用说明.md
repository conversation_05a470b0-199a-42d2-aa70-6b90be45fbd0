# MMD跳舞功能使用说明

## 概述

本系统集成了MMD (MikuMikuDance) 舞蹈功能，支持通过直播弹幕控制虚拟角色跳舞。系统采用WebSocket架构，可以连接到UPBGE (Blender Game Engine) 服务端执行高质量的3D舞蹈动画。

## 功能特点

- 🎭 **智能舞蹈搜索**: 支持中文舞蹈名称模糊搜索
- 🎵 **音频同步**: 自动播放舞蹈配套音频
- 🔄 **自动回退**: 当MMD服务器不可用时自动回退到传统模式
- 🧘 **日常动作**: 停止跳舞后自动切换到日常动作（站立、呼吸、眨眼等）
- 📊 **状态监控**: 实时监控连接状态和播放状态
- 💰 **积分系统**: 跳舞消耗用户积分

## 弹幕命令

### 跳舞命令
```
跳舞 [舞蹈名称]
跳一下 [舞蹈名称]
舞蹈 [舞蹈名称]
```

**示例：**
- `跳舞 极乐净土` - 跳指定舞蹈
- `跳舞` - 随机跳舞
- `跳一下 千本樱` - 跳千本樱舞蹈

### 停止命令
```
停止跳舞
停舞
stop
停止
```

**效果：**
- 立即停止当前舞蹈
- 自动切换到日常动作
- 播放停止提示音

## 系统架构

### 组件说明

1. **dance_core.py** - 核心控制模块
   - 处理弹幕命令
   - 管理舞蹈队列
   - 控制MMD服务器连接

2. **websocket_client.py** - WebSocket客户端
   - 与UPBGE服务端通信
   - 智能端口发现
   - 自动重连机制

3. **upbge_mmd_server.py** - UPBGE服务端
   - 处理舞蹈执行请求
   - 管理MMD组件
   - 音频同步播放

### 连接流程

```
弹幕输入 → dance_core.py → websocket_client.py → UPBGE服务端 → MMD组件播放
```

## 配置说明

### 主要配置项

在系统配置文件中添加以下配置：

```toml
[dance.websocket]
# MMD服务器配置
mmd_server_host = "localhost"
mmd_server_port = 8765
mmd_server_enabled = true

# 传统WebSocket配置（保持兼容）
switch = true
game_server_url = "ws://localhost:9999"
vmd_path = "./vmd/"

[dance]
# 日常动作配置
idle_actions_enabled = true
idle_actions_path = "data/idle_actions/"
default_idle_action = "站立呼吸"
```

### 积分设置

跳舞消耗积分：`-3分/次`

## 运行流程

### 1. 启动UPBGE服务端

在UPBGE中运行服务端脚本：
```python
# 在UPBGE中运行
exec(open("func/dance/upbge_mmd_server.py").read())
```

### 2. 启动直播系统

启动主程序，系统会自动：
- 初始化MMD WebSocket客户端
- 尝试连接UPBGE服务端
- 如果连接失败，回退到传统模式

### 3. 弹幕控制

观众在直播间发送弹幕：
```
跳舞 极乐净土
```

系统处理流程：
1. 解析弹幕命令
2. 扣除用户积分
3. 搜索匹配舞蹈
4. 发送执行请求到UPBGE
5. 播放舞蹈和音频
6. 播放完成后恢复日常动作

## 故障排除

### 常见问题

#### 1. MMD服务器连接失败

**症状：** 系统提示"无法连接到MMD服务器，将使用传统模式"

**解决方案：**
- 检查UPBGE是否正在运行
- 确认端口8765未被占用
- 运行`debug_mmd_system()`检查服务器状态

#### 2. 舞蹈搜索失败

**症状：** 提示"抱歉，没有找到舞蹈"

**解决方案：**
- 检查舞蹈索引是否已加载
- 确认舞蹈文件路径正确
- 尝试使用完整舞蹈名称

#### 3. 音频播放问题

**症状：** 舞蹈播放但没有音频

**解决方案：**
- 检查音频文件是否存在
- 确认音频格式支持（WAV/MP3/OGG）
- 检查UPBGE音频设备配置

### 调试命令

在UPBGE控制台中可以使用以下调试命令：

```python
# 调试系统状态
debug_mmd_system()

# 手动刷新服务器
refresh_mmd_server()

# 强制重启服务器
force_restart_mmd_server()

# 清理端口占用
cleanup_mmd_ports()

# 测试音频播放
test_audio_playback("path/to/audio.wav")

# 测试组件音频
test_mmd_component_audio()
```

## 日常动作系统

### 概念

当没有舞蹈播放时，系统会自动播放日常动作，让虚拟角色保持自然的状态。

### 支持的日常动作

- **站立呼吸** - 基础站立姿态，配合呼吸动作
- **眨眼** - 随机眨眼动画
- **轻微摇摆** - 轻微的身体摇摆

### 切换逻辑

- 开始跳舞时 → 停止日常动作
- 停止跳舞时 → 启动默认日常动作
- 系统启动时 → 启动默认日常动作

### 文件结构

```
data/idle_actions/
├── 站立呼吸/
│   ├── motion.vmd
│   └── config.json
├── 眨眼/
│   ├── motion.vmd
│   └── config.json
└── 轻微摇摆/
    ├── motion.vmd
    └── config.json
```

**注意：** 日常动作文件目前为预留功能，具体文件需要单独准备。

## 性能优化

### 连接管理

- 自动重连：每30秒检查连接状态
- 智能端口发现：自动扫描可用端口
- 连接池：复用WebSocket连接

### 资源管理

- 舞蹈索引缓存：启动时预加载
- 音频预加载：提高播放响应速度
- 内存清理：定期清理无效引用

## 扩展功能

### 未来计划

1. **高级控制命令**
   - 调整播放速度：`跳舞 极乐净土 速度1.5`
   - 循环播放：`跳舞 极乐净土 循环`
   - 指定模型：`模型1 跳舞 极乐净土`

2. **表情同步**
   - 根据舞蹈内容自动切换表情
   - 支持自定义表情配置

3. **多角色支持**
   - 同时控制多个虚拟角色
   - 编队舞蹈功能

4. **舞蹈预览**
   - 在执行前展示舞蹈预览图
   - 显示舞蹈时长和难度

## 技术支持

如有问题，请检查：

1. **日志输出** - 查看控制台错误信息
2. **配置文件** - 确认所有配置项正确
3. **文件权限** - 确保程序有访问权限
4. **端口占用** - 检查端口是否被其他程序占用

---

**注意：** 本功能需要UPBGE环境支持，请确保已正确安装和配置UPBGE及相关MMD组件。 