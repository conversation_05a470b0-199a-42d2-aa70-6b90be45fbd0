import asyncio
from asyncio import coroutines
from functools import wraps
from flask import Flask, request, jsonify
from flask_cors import CORS
import logging

class APIServer:
    def __init__(self):
        self.app = Flask(__name__)
        self.cors = CORS(self.app, resources={r"/*": {"origins": "*"}})
        self.logger = self._setup_logger()
        self.sched = None
        self.controller = None
        self.common_data = {}

    def _setup_logger(self):
        logging.basicConfig(level=logging.INFO)
        return logging.getLogger(__name__)

    @coroutines.coroutine
    def bind_sched(self):
        """使用asyncio.coroutines装饰器的调度器绑定"""
        self.sched = asyncio.get_event_loop()
        # 可以在这里添加其他异步初始化代码
        yield from asyncio.sleep(0)  # 确保这是一个协程

    def create_controller(self):
        """创建控制器实例"""
        self.controller = Controller()

    def setup_routes(self):
        """设置API路由，使用asyncio.coroutines处理异步请求"""

        def async_route(f):
            @wraps(f)
            def wrapper(*args, ​**kwargs):
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                try:
                    return loop.run_until_complete(f(*args, ​**kwargs))
                finally:
                    loop.close()
            return wrapper

        @self.app.route('/api/run', methods=['POST'])
        @async_route
        @coroutines.coroutine
        def api_run():
            try:
                data = request.get_json()
                self.logger.info(f"Received request: {data}")
                
                # 使用yield from调用协程
                result = yield from self._process_request(data)
                
                return jsonify({
                    "status": "success",
                    "data": result
                })
            except Exception as e:
                self.logger.error(f"Error processing request: {e}")
                return jsonify({
                    "status": "error",
                    "message": str(e)
                }), 500

    @coroutines.coroutine
    def _process_request(self, data):
        """使用asyncio.coroutines装饰器的异步处理逻辑"""
        if hasattr(self.controller, 'process'):
            result = yield from self.controller.process(data)
            return result
        return {"message": "Request processed"}

    def run(self, host='0.0.0.0', port=5000):
        """启动服务器"""
        loop = asyncio.get_event_loop()
        loop.run_until_complete(self._async_run(host, port))

    @coroutines.coroutine
    def _async_run(self, host, port):
        """异步启动逻辑"""
        yield from self.bind_sched()
        self.create_controller()
        self.setup_routes()
        
        self.app.config['DISABLED'] = False
        
        self.logger.info(f"Starting API server on {host}:{port}")
        # 注意：Flask的run()是同步的，这里只是演示coroutines用法
        # 实际生产环境应该使用异步服务器如Quart或uvicorn+FastAPI

if __name__ == '__main__':
    server = APIServer()
    server.run()