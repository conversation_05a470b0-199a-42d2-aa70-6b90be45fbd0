J:\ai\�������\ai-yinmei\func\http\bilive.c     bilive.py       __init__        bilive.BiliveApi.__init__       queryContributionRank   bilive.BiliveApi.queryContributionRank  bilive  Interpreter change detected - this module can only be loaded into one interpreter per process.  name    __loader__  loader      __file__    origin      __package__ parent      __path__        submodule_search_locations      Module 'bilive' has already been imported. Re-initialisation is not supported.  builtins        cython_runtime  __builtins__    init bilive     %s() got multiple values for keyword argument '%U'      %.200s() keywords must be strings       %s() got an unexpected keyword argument '%U'    exactly s       %.200s() takes %.8s %zd positional argument%.1s (%zd given)     name '%U' is not defined        join() result is too long for a Python string    while calling a Python object  NULL result without error in PyObject_Call      cannot import name %S   _cython_3_0_11  Shared Cython type %.200s is not a type object  Shared Cython type %.200s has the wrong size, try recompiling   keywords must be strings        __name__ must be set to a string object __qualname__ must be set to a string object     function's dictionary may not be deleted        setting function's dictionary to a non-dict     __defaults__ must be set to a tuple object      changes to cyfunction.__defaults__ will not currently affect the values used in function calls  __kwdefaults__ must be set to a dict object     changes to cyfunction.__kwdefaults__ will not currently affect the values used in function calls        __annotations__ must be set to a dict object    func_doc        __doc__ func_name       __name__        __qualname__    func_dict       __dict__        func_globals    __globals__     func_closure    __closure__     func_code       __code__        func_defaults   __defaults__    __kwdefaults__  __annotations__ _is_coroutine   __module__      __reduce__      Bad call flags for CyFunction   <cyfunction %U at %p>   %.200s() takes no arguments (%zd given) %.200s() takes exactly one argument (%zd given) %.200s() takes no keyword arguments     unbound method %.200S() needs an argument       %.200s() needs an argument      _cython_3_0_11.cython_function_or_method        %s (%s:%d)      does not match          compile time Python version %d.%d of module '%.100s' %s runtime version %d.%d   requests        headers exception       BiliveApi.__init__      cline_in_traceback      func.tools.singleton_mode       (� �   __prepare__ *   bilive  BiliveApi       __main__        __qualname__    __module__  self        __dict__        �� �   _initializing           https://api.live.bilibili.com/xlive/general-interface/v1/rank/queryContributionRank?ruid=       Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:131.0) Gecko/20100101 Firefox/131.0        _is_coroutine   BiliveApi.queryContributionRank super   __spec__        J:\ai\吟美打包\ai-yinmei\func\http\bilive.py        __import__  log __doc__ &page=1&page_size=100&type=online_rank&switch=contribution_rank&platform=web    User-Agent      __set_name__    response    uid func.log.default_log    __name__        DefaultLog      __init_subclass__   get timeout __test__    json    ?   e       __metaclass__   queryContributionRank异常     getLogger       __init__        asyncio.coroutines      
        获取b站直播间用户排行榜
        :param uid: 主播id
        :param room_id: 直播房间号
        :return:
              queryContributionRank   singleton   .   room_id &room_id=