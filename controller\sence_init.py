# 场景初始化模块
import time
from threading import Thread
from func.log.default_log import DefaultLog
from func.tools.common_websocket1 import CommonWebsocket
from func.obs.obs_init import ObsInit
from func.vtuber.action_oper import ActionOper
from func.vtuber.emote_oper import EmoteOper
from func.gobal.data import CommonData, VtuberData

# 设置控制台日志
log = DefaultLog().getLogger()

def senceInit():
    """场景初始化函数"""
    try:
        log.info("开始初始化场景...")
        
        # 1. 启动WebSocket服务
        init_websocket_service()
        
        # 2. 初始化OBS连接
        init_obs_connection()
        
        # 3. 初始化VTuber相关组件
        init_vtuber_components()
        
        # 4. 设置初始场景状态
        init_scene_status()
        
        log.info("场景初始化完成")
        return True
        
    except Exception as e:
        log.error(f"场景初始化失败: {e}")
        return False

def init_websocket_service():
    """初始化WebSocket服务"""
    try:
        log.info("正在启动WebSocket服务...")
        
        # 获取WebSocket实例并启动
        websocket_service = CommonWebsocket()
        websocket_service.start()
        
        # 等待服务启动
        time.sleep(2)
        
        if websocket_service.is_running():
            log.info("WebSocket服务启动成功，监听端口: 18765")
        else:
            log.warning("WebSocket服务启动状态未知")
            
    except Exception as e:
        log.error(f"WebSocket服务启动失败: {e}")
        raise

def init_obs_connection():
    """初始化OBS连接"""
    try:
        log.info("正在初始化OBS连接...")
        obs = ObsInit()
        log.info("OBS连接初始化完成")
        return obs
    except Exception as e:
        log.error(f"OBS连接初始化失败: {e}")
        # OBS连接失败不应该阻止整个系统启动
        return None

def init_vtuber_components():
    """初始化VTuber相关组件"""
    try:
        log.info("正在初始化VTuber组件...")
        
        # 初始化动作操作器
        action_oper = ActionOper()
        log.info("动作操作器初始化完成")
        
        # 初始化表情操作器
        emote_oper = EmoteOper()
        log.info("表情操作器初始化完成")
        
        return action_oper, emote_oper
        
    except Exception as e:
        log.error(f"VTuber组件初始化失败: {e}")
        return None, None

def init_scene_status():
    """设置初始场景状态"""
    try:
        log.info("正在设置初始场景状态...")
        
        # 获取数据对象
        common_data = CommonData()
        vtuber_data = VtuberData()
        
        # 设置初始状态
        vtuber_data.value = "初始化完成"
        
        log.info("初始场景状态设置完成")
        
    except Exception as e:
        log.error(f"设置初始场景状态失败: {e}")

class SceneManager:
    """场景管理器"""
    
    def __init__(self):
        self.log = DefaultLog().getLogger()
        self.obs = None
        self.action_oper = None
        self.emote_oper = None
        self.common_data = CommonData()
        self.vtuber_data = VtuberData()
        self.websocket_service = CommonWebsocket()
        
        # 场景配置
        self.scenes = {
            "唱歌视频": "video",
            "波形": "visible_input", 
            "状态提示": "show_text",
            "表情": "emote",
            "伴奏": "bgm",
            "便衣": "now_clothes"
        }
    
    def initialize(self):
        """初始化场景管理器"""
        try:
            self.log.info("初始化场景管理器...")
            
            # 初始化各个组件
            self.obs = ObsInit()
            self.action_oper = ActionOper()
            self.emote_oper = EmoteOper()
            
            # 确保WebSocket服务运行
            if not self.websocket_service.is_running():
                self.websocket_service.start()
            
            self.log.info("场景管理器初始化完成")
            return True
            
        except Exception as e:
            self.log.error(f"场景管理器初始化失败: {e}")
            return False
    
    def set_scene_visible(self, scene_name, visible):
        """设置场景可见性"""
        try:
            if scene_name in self.scenes:
                # 这里应该调用OBS的API来设置场景可见性
                # 由于OBS API的具体实现可能不同，这里只是记录日志
                self.log.info(f"设置场景 '{scene_name}' 可见性: {visible}")
                return True
            else:
                self.log.warning(f"未知场景: {scene_name}")
                return False
        except Exception as e:
            self.log.error(f"设置场景可见性失败: {e}")
            return False
    
    def check_scene_time(self):
        """检查场景时间"""
        try:
            current_time = self.common_data.value
            if current_time:
                # 根据时间设置场景
                self.set_scene_visible("状态提示", True)
                self.set_scene_visible("波形", True)
                
                if self.vtuber_data.value != "STOP":
                    self.set_scene_visible("表情", True)
                    self.set_scene_visible("便衣", True)
                    
        except Exception as e:
            self.log.error(f"检查场景时间失败: {e}")
    
    def control_video(self, action="STOP"):
        """控制视频播放"""
        try:
            if action == "STOP":
                self.set_scene_visible("唱歌视频", False)
                self.set_scene_visible("伴奏", False)
                self.vtuber_data.value = "STOP"
            else:
                self.set_scene_visible("唱歌视频", True)
                self.set_scene_visible("伴奏", True)
                self.vtuber_data.value = "播放"
                
            self.log.info(f"视频控制: {action}")
            
        except Exception as e:
            self.log.error(f"视频控制失败: {e}")

# 全局场景管理器实例
scene_manager = SceneManager()

def get_scene_manager():
    """获取场景管理器实例"""
    return scene_manager

def get_websocket_service():
    """获取WebSocket服务实例"""
    return CommonWebsocket()
