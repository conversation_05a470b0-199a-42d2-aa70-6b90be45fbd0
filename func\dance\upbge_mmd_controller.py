# UPBGE MMD模型控制器
# 这个脚本需要在UPBGE中运行，用于控制MMD模型播放VMD动作

import bge
from bge import logic, types
import os
import json

class MMDController:
    """MMD模型控制器"""
    
    def __init__(self, mmd_object_name="MMD_Model"):
        self.mmd_object_name = mmd_object_name
        self.scene = logic.getCurrentScene()
        self.mmd_object = None
        self.current_animation = None
        self.animation_data = {}
        
        # 尝试获取MMD对象
        self.find_mmd_object()
        
    def find_mmd_object(self):
        """查找场景中的MMD模型对象"""
        if self.mmd_object_name in self.scene.objects:
            self.mmd_object = self.scene.objects[self.mmd_object_name]
            print(f"找到MMD对象: {self.mmd_object_name}")
        else:
            print(f"警告: 未找到MMD对象 '{self.mmd_object_name}'")
            # 尝试查找任何可能的MMD对象
            for obj_name in self.scene.objects:
                if "mmd" in obj_name.lower() or "model" in obj_name.lower():
                    self.mmd_object = self.scene.objects[obj_name]
                    print(f"找到可能的MMD对象: {obj_name}")
                    break
                    
    def load_vmd_animation(self, vmd_file_path):
        """加载VMD动画文件"""
        if not self.mmd_object:
            print("错误: 没有找到MMD对象")
            return False
            
        if not os.path.exists(vmd_file_path):
            print(f"错误: VMD文件不存在: {vmd_file_path}")
            return False
            
        try:
            # 这里需要根据你使用的MMD插件来实现具体的加载逻辑
            # 以下是一些可能的实现方式：
            
            # 方式1: 如果使用bpy导入的MMD模型
            # import bpy
            # bpy.ops.mmd_tools.import_vmd(filepath=vmd_file_path)
            
            # 方式2: 如果有自定义的MMD加载器
            # self.mmd_object['vmd_file'] = vmd_file_path
            # self.mmd_object['load_vmd'] = True
            
            # 方式3: 使用属性设置
            if hasattr(self.mmd_object, 'game'):
                self.mmd_object.game.properties['vmd_file'] = vmd_file_path
                self.mmd_object.game.properties['action'] = 'load_vmd'
                
            self.current_animation = {
                'file': vmd_file_path,
                'name': os.path.basename(vmd_file_path),
                'status': 'loaded'
            }
            
            print(f"成功加载VMD文件: {vmd_file_path}")
            return True
            
        except Exception as e:
            print(f"加载VMD文件时出错: {e}")
            return False
            
    def play_animation(self):
        """播放当前加载的动画"""
        if not self.mmd_object or not self.current_animation:
            print("错误: 没有MMD对象或动画未加载")
            return False
            
        try:
            # 方式1: 使用action actuator
            if hasattr(self.mmd_object, 'actuators'):
                for actuator in self.mmd_object.actuators:
                    if actuator.type == bge.logic.KX_ACT_ACTION:
                        actuator.activate()
                        break
                        
            # 方式2: 使用属性控制
            if hasattr(self.mmd_object, 'game'):
                self.mmd_object.game.properties['action'] = 'play'
                
            # 方式3: 直接调用播放方法（如果有自定义方法）
            # self.mmd_object.play_animation()
            
            self.current_animation['status'] = 'playing'
            print(f"开始播放动画: {self.current_animation['name']}")
            return True
            
        except Exception as e:
            print(f"播放动画时出错: {e}")
            return False
            
    def stop_animation(self):
        """停止当前动画"""
        if not self.mmd_object:
            return False
            
        try:
            # 停止所有action actuator
            if hasattr(self.mmd_object, 'actuators'):
                for actuator in self.mmd_object.actuators:
                    if actuator.type == bge.logic.KX_ACT_ACTION:
                        actuator.deactivate()
                        
            # 使用属性控制停止
            if hasattr(self.mmd_object, 'game'):
                self.mmd_object.game.properties['action'] = 'stop'
                
            if self.current_animation:
                self.current_animation['status'] = 'stopped'
                
            print("动画已停止")
            return True
            
        except Exception as e:
            print(f"停止动画时出错: {e}")
            return False
            
    def get_animation_status(self):
        """获取动画状态"""
        if not self.current_animation:
            return "no_animation"
            
        return self.current_animation.get('status', 'unknown')
        
    def set_animation_speed(self, speed=1.0):
        """设置动画播放速度"""
        if not self.mmd_object:
            return False
            
        try:
            if hasattr(self.mmd_object, 'game'):
                self.mmd_object.game.properties['animation_speed'] = speed
                
            print(f"设置动画速度: {speed}")
            return True
            
        except Exception as e:
            print(f"设置动画速度时出错: {e}")
            return False

# UPBGE脚本主函数
def main():
    """主函数 - 在UPBGE中运行"""
    
    # 获取游戏逻辑控制器
    controller = logic.getCurrentController()
    
    # 初始化MMD控制器（只初始化一次）
    if not hasattr(logic, 'mmd_controller'):
        logic.mmd_controller = MMDController()
        print("MMD控制器已初始化")
        
    # 检查是否有加载VMD的请求
    mmd_ctrl = logic.mmd_controller
    
    # 检查属性变化来执行相应动作
    if mmd_ctrl.mmd_object and hasattr(mmd_ctrl.mmd_object, 'game'):
        props = mmd_ctrl.mmd_object.game.properties
        
        # 处理加载VMD请求
        if props.get('action') == 'load_vmd' and 'vmd_file' in props:
            vmd_file = props['vmd_file']
            success = mmd_ctrl.load_vmd_animation(vmd_file)
            
            if success:
                props['action'] = 'loaded'
            else:
                props['action'] = 'error'
                
        # 处理播放请求
        elif props.get('action') == 'play':
            mmd_ctrl.play_animation()
            props['action'] = 'playing'
            
        # 处理停止请求
        elif props.get('action') == 'stop':
            mmd_ctrl.stop_animation()
            props['action'] = 'stopped'

# 传感器触发函数
def on_vmd_load(controller):
    """当收到VMD加载指令时调用"""
    main()

def on_animation_control(controller):
    """当收到动画控制指令时调用"""
    main()

# 如果作为独立脚本运行（用于测试）
if __name__ == "__main__":
    print("这个脚本需要在UPBGE环境中运行")
    print("请将相关函数添加到你的UPBGE项目中") 