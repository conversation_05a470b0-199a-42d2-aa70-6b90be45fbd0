from typing import Dict, Optional, List, Tuple

from src.common.logger import get_logger
from src.config.api_ada_configs import TaskConfig
from src.chat.message_receive.chat_stream import ChatStream, get_chat_manager
from src.chat.replyer.default_generator import DefaultReplyer

logger = get_logger("ReplyerManager")


class ReplyerManager:
    def __init__(self):
        self._repliers: Dict[str, DefaultReplyer] = {}

    def get_replyer(
        self,
        chat_stream: Optional[ChatStream] = None,
        chat_id: Optional[str] = None,
        model_set_with_weight: Optional[List[Tuple[TaskConfig, float]]] = None,
        request_type: str = "replyer",
    ) -> Optional[DefaultReplyer]:
        """
        获取或创建回复器实例。

        model_configs 仅在首次为某个 chat_id/stream_id 创建实例时有效。
        后续调用将返回已缓存的实例，忽略 model_configs 参数。
        """
        # ========== 调试信息：ReplyerManager.get_replyer 方法入口 ==========
        logger.info("&" * 50)
        logger.info("[DEBUG] ReplyerManager.get_replyer 开始执行")
        logger.info(f"[DEBUG] 输入参数:")
        logger.info(f"  - chat_stream: {'存在' if chat_stream else '不存在'}")
        if chat_stream:
            logger.info(f"    - stream_id: {chat_stream.stream_id}")
            logger.info(f"    - platform: {chat_stream.platform}")
        logger.info(f"  - chat_id: {chat_id}")
        logger.info(f"  - model_set_with_weight: {len(model_set_with_weight) if model_set_with_weight else 0} 个模型")
        logger.info(f"  - request_type: {request_type}")
        logger.info(f"[DEBUG] 管理器状态:")
        logger.info(f"  - 已缓存的回复器数量: {len(self._repliers)}")
        logger.info(f"  - 缓存的 stream_id 列表: {list(self._repliers.keys())}")

        # ========== 调试信息：确定 stream_id ==========
        logger.info("[DEBUG] 步骤1: 确定 stream_id")
        stream_id = chat_stream.stream_id if chat_stream else chat_id
        logger.info(f"[DEBUG] 确定的 stream_id: {stream_id}")

        if not stream_id:
            logger.warning("[DEBUG] stream_id 为空，无法获取回复器")
            logger.warning("[ReplyerManager] 缺少 stream_id，无法获取回复器。")
            return None

        # ========== 调试信息：检查缓存 ==========
        logger.info("[DEBUG] 步骤2: 检查缓存")
        logger.info(f"[DEBUG] 检查 stream_id '{stream_id}' 是否在缓存中")

        # 如果已有缓存实例，直接返回
        if stream_id in self._repliers:
            logger.info(f"[DEBUG] 找到缓存的回复器实例")
            cached_replyer = self._repliers[stream_id]
            logger.info(f"[DEBUG] 缓存的回复器信息:")
            logger.info(f"  - 类型: {type(cached_replyer).__name__}")
            logger.info(f"  - chat_stream.stream_id: {cached_replyer.chat_stream.stream_id}")
            logger.info(f"  - request_type: {cached_replyer.request_type}")
            logger.debug(f"[ReplyerManager] 为 stream_id '{stream_id}' 返回已存在的回复器实例。")
            logger.info("[DEBUG] ReplyerManager.get_replyer 执行完成 (返回缓存)")
            logger.info("&" * 50)
            return cached_replyer

        # ========== 调试信息：创建新实例 ==========
        logger.info("[DEBUG] 步骤3: 创建新的回复器实例")
        logger.debug(f"[ReplyerManager] 为 stream_id '{stream_id}' 创建新的回复器实例并缓存。")

        # ========== 调试信息：获取目标流 ==========
        logger.info("[DEBUG] 步骤4: 获取目标聊天流")
        target_stream = chat_stream
        logger.info(f"[DEBUG] 初始 target_stream: {'存在' if target_stream else '不存在'}")

        if not target_stream:
            logger.info("[DEBUG] target_stream 为空，尝试从 chat_manager 获取")
            if chat_manager := get_chat_manager():
                logger.info(f"[DEBUG] 成功获取 chat_manager: {type(chat_manager).__name__}")
                target_stream = chat_manager.get_stream(stream_id)
                logger.info(f"[DEBUG] 从 chat_manager 获取的 target_stream: {'存在' if target_stream else '不存在'}")
            else:
                logger.warning("[DEBUG] 无法获取 chat_manager")

        if not target_stream:
            logger.warning(f"[DEBUG] 无法获取 stream_id='{stream_id}' 的聊天流")
            logger.warning(f"[ReplyerManager] 未找到 stream_id='{stream_id}' 的聊天流，无法创建回复器。")
            return None

        logger.info(f"[DEBUG] 成功获取目标聊天流:")
        logger.info(f"  - stream_id: {target_stream.stream_id}")
        logger.info(f"  - platform: {target_stream.platform}")
        logger.info(f"  - user_info: {target_stream.user_info.user_nickname if target_stream.user_info else 'None'}")

        # ========== 调试信息：创建 DefaultReplyer ==========
        logger.info("[DEBUG] 步骤5: 创建 DefaultReplyer 实例")
        logger.info(f"[DEBUG] DefaultReplyer 构造参数:")
        logger.info(f"  - chat_stream: {target_stream.stream_id}")
        logger.info(f"  - model_set_with_weight: {len(model_set_with_weight) if model_set_with_weight else 0} 个模型")
        logger.info(f"  - request_type: {request_type}")

        # model_configs 只在此时（初始化时）生效
        replyer = DefaultReplyer(
            chat_stream=target_stream,
            model_set_with_weight=model_set_with_weight,  # 可以是None，此时使用默认模型
            request_type=request_type,
        )

        logger.info(f"[DEBUG] DefaultReplyer 创建成功:")
        logger.info(f"  - 类型: {type(replyer).__name__}")
        logger.info(f"  - chat_stream.stream_id: {replyer.chat_stream.stream_id}")
        logger.info(f"  - model_set 数量: {len(replyer.model_set) if replyer.model_set else 0}")
        logger.info(f"  - request_type: {replyer.request_type}")

        # ========== 调试信息：缓存实例 ==========
        logger.info("[DEBUG] 步骤6: 缓存回复器实例")
        self._repliers[stream_id] = replyer
        logger.info(f"[DEBUG] 回复器已缓存，当前缓存数量: {len(self._repliers)}")

        logger.info("[DEBUG] ReplyerManager.get_replyer 执行完成 (创建新实例)")
        logger.info("&" * 50)
        return replyer


# 创建一个全局实例
replyer_manager = ReplyerManager()
