<!DOCTYPE html>
<html lang="zh">
	<head>
		<meta charset="utf-8">
		<meta http-equiv="X-UA-Compatible" content="IE=edge">
		<meta name="viewport" content="width=device-width,initial-scale=1">
		<meta name="referrer" content="no-referrer">
		<link rel="icon" href="/favicon.ico">
		<title>黑色透明回复框</title>
		<style>
            /* Transparent background */
            body {
                background-color: transparent !important;
            }
			#chat {
				width: 1200px; height:200px; overflow: hidden; 
				background-color: rgba(0, 0, 0, 0.7);
				padding: 10px; 
                /* -webkit-border-radius: 15px;  */
                border: solid 1px #ffffff; 
            }
			#content {
                width: 1200px; height:200px;/* width: auto; height:auto; */
            }
			#content .area{
			    color: white; line-height: 43px; 
				font-size: 32px; font-weight: bold; word-wrap: break-word; overflow:hidden;
				letter-spacing:5px; font-family:"微软雅黑";
			}
			#content .area .end{
			    animation: blink 1s linear infinite;
			}
			@keyframes blink {
				0% {
					opacity: 0;
				}
				50% {
					opacity: 1;
				}
				100% {
					opacity: 0;
				}
			}
        </style>
		<script type="text/javascript" src="jquery-3.7.1.min.js"></script>
        <script type="text/javascript" src="vue.3.5.13.js"></script>
	</head>
	<body>
		<div id="chat">
			  <div id="content">
				<div class="area">
					<span class="font"></span><span class="end"></span>
				</div>
			  </div>
		</div>
		<div id="tip"></div>
        <script>
            const {createApp, reactive, onMounted} = Vue;

            const app = createApp({
                methods: {

                },
                setup() {
                    const info = reactive({});

                    const fetchInfo = async () => {
                        try {
                            let ws = new WebSocket("ws://localhost:18765");
                            ws.onopen = function () {
                                console.log("Connected!--------chatui--------");
                            };
                            ws.onmessage = function (event) {
                                console.log("Received message:", event.data);
                                jsonstr = JSON.parse(event.data);
                                Object.assign(info, jsonstr);

                                if (info["type"] != "聊天回复")
                                    return;
                                console.log(info);

                                traceid = info["traceid"];
                                text = info["text"];
                                chatStatus = info["chatStatus"];
                                text = text.replaceAll("<br/>", "\r\n");
                                var i = 0; // 当前要显示的字符索引
                                attrname = $('#content div.area .font').attr("class");
                                //判断是新traceid：更改文字输出的class为traceid追踪
                                if (attrname != "font " + traceid) {
                                    $("#content div.area .font").text("");
                                    $('#content div.area .font').attr('class', "font " + traceid);
                                }
                                new_attrname = "#content div.area .font." + traceid;
                                $("#content div.area .end").text("_");

                                function showChar() {
                                    if (i < text.length) {
                                        fontnum = $(new_attrname).text().length;
                                        //超过字数清空
                                        if (fontnum % 125 == 0)
                                            $(new_attrname).text("");
                                        //尾部追加文字
                                        $(new_attrname).append(text[i]);
                                        i++; // 更新索引值
                                        setTimeout(showChar, 60); // 每次间隔xx调用自身，模拟打字效果
                                    } else {
                                        console.log('完成'); // 所有字符都已经输出完毕时触发此行代码
                                        if (chatStatus == "end") {
                                            $("#content div.area .end").text("");
                                            //$("#chat").hide();
                                        }

                                    }
                                }

                                showChar(); // 开始输出第一个字符
                            };

                            ws.onclose = function (event) {
                                console.log('WebSocket连接已关闭');
                                setTimeout(reconnect, 5000);
                            };

                            function reconnect() {
                                if (ws !== null && (ws.readyState === WebSocket.CLOSED || ws.readyState === WebSocket.CLOSING)) {
                                    console.log("尝试重连WebSocket...");
                                    fetchInfo();
                                }
                            }


                        } catch (error) {
                            console.error('Failed to fetch info:', error);
                        }
                    };

                    onMounted(() => {
                        fetchInfo();
                    });


                    return {
                        info,
                    };
                }
            }).mount('#chat');
        </script>
	</body>
</html>