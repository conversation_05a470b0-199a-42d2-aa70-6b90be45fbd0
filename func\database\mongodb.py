import os
from pymongo import MongoClient
import subprocess
from func.tools.singleton_mode import singleton
from func.log.default_log import DefaultLog
# from func.tools.decorator_mode import class_switch
from func.gobal.data import MongodbData

log = DefaultLog().getLogger()

@singleton
class Mongodb:  # mongodb://localhost:27017
    def __init__(self, username=None, password=None, url="mongodb://", dbname="yinmei"):
        """
        初始化MongoDB连接
        :param username: 用户名
        :param password: 密码
        :param url: mongodb连接url
        :param dbname: 数据库名称
        """
        self.status = False
        # if username and password:
        #     self.url = f"{url}{username}:{password}@{MongodbData.dbname}"
        #     # self.url = f"{url}{username}:{password}:27017"
        # else:
        #     self.url = f"{url}{MongodbData.dbname}"

        self.url = "mongodb://127.0.0.1:27017/"

        try:
            # # 检查是否已启动 MongoDB 服务，如果没有启动，则启动 MongoDB 服务
            if not check_mongo_service():
                startdb()

            print(self.url)    
            # run_cmd(mgpath + r"bin\\mongod.exe --dbpath=" + mgpath + r"\\db --port 27017")
            self.conn = MongoClient(self.url)
            self.db = self.conn[dbname]
            if self.conn:
                self.status = True
                log.info("MongoDB连接成功")
        except Exception as e:
            print(e)
            log.error(f"MongoDB连接失败: {str(e)}")
            self.status = False

    def get_db(self):
        """
        获取数据库连接
        :return: 数据库连接对象
        """
        if self.status:
            return self.db
        return None

def startdb():
    # run_cmd('net start MongoDB')
    print("MongoDB服务未启动---------------")
    mgpath0 = "H:\\soft\\mongodb-windows-x86_64-8.0.6\\" #os.getcwd()
    mgpath = "start " + mgpath0 + r"bin\\mongod.exe --dbpath=" + mgpath0 + r"\\db --port 27017"
    print(mgpath)
    run_cmd(mgpath)

def check_mongo_service() -> bool:
    """
    检查 MongoDB 服务是否正在运行
    Returns:
        bool: 如果服务正在运行，则返回 True；否则返回 False
    """
    try:
        # 在 Windows 上，可以使用 'net start MongoDB' 命令检查服务状态
        # 但这实际上不会告诉我们服务是否“正在运行”，只会告诉我们是否尝试启动它
        # 因此，我们改用检查端口 27017 是否开放的方法
        result = subprocess.run(['netstat', '-ano', '|', 'findstr', ':27017'], 
                                stdout=subprocess.PIPE,
                                stderr=subprocess.PIPE, text=True)
        # print(result.returncode)
        if result.returncode == 1:
            # 如果找到端口 27017，则认为服务正在运行
            return True
        else:
            return False
    except Exception as e:
        print(f"检查 MongoDB 服务时发生错误: {e}")
        return False

def run_cmd(command: str, open_new_window: bool = True):
    """
    运行 cmd 命令
    Args:
        command (str): 指定要运行的命令
        open_new_window (bool): 指定是否新建一个 cmd 窗口运行
    """
    if open_new_window:
        command = "start " + command
    subprocess.Popen(command, shell=True)