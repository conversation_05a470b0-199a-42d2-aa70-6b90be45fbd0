J:\ai\�������\tt2\draw\draw_core.c     draw_core.py    __init__        draw_core.DrawCore.__init__     check_draw      draw_core.DrawCore.check_draw   draw    draw_core.DrawCore.draw checkpoint      progress        draw_core.DrawCore.progress nsfw        draw_prompt_do  draw_core.DrawCore.draw_prompt_do       draw_prompt     draw_core.DrawCore.draw_prompt  http_draw       draw_core.DrawCore.http_draw    inner_draw      draw_core.DrawCore.inner_draw   msg_deal        draw_core.DrawCore.msg_deal     draw_core       Interpreter change detected - this module can only be loaded into one interpreter per process.  name    __loader__  loader      __file__    origin      __package__ parent      __path__        submodule_search_locations      Module 'draw_core' has already been imported. Re-initialisation is not supported.       builtins        cython_runtime  __builtins__    init draw_core  name '%U' is not defined        %s() got multiple values for keyword argument '%U'      %.200s() keywords must be strings       %s() got an unexpected keyword argument '%U'    exactly s       %.200s() takes %.8s %zd positional argument%.1s (%zd given)      while calling a Python object  NULL result without error in PyObject_Call      join() result is too long for a Python string   strings are too large to concat local variable '%s' referenced before assignment        too many values to unpack (expected %zd)        need more than %zd value%.1s to unpack  '%.200s' object is unsliceable  cannot fit '%.200s' into an index-sized integer '%.200s' object is not subscriptable    cannot import name %S   _cython_3_0_11  Shared Cython type %.200s is not a type object  Shared Cython type %.200s has the wrong size, try recompiling   keywords must be strings        __name__ must be set to a string object __qualname__ must be set to a string object     function's dictionary may not be deleted        setting function's dictionary to a non-dict     __defaults__ must be set to a tuple object      changes to cyfunction.__defaults__ will not currently affect the values used in function calls  __kwdefaults__ must be set to a dict object     changes to cyfunction.__kwdefaults__ will not currently affect the values used in function calls        __annotations__ must be set to a dict object    func_doc        __doc__ func_name       __name__        __qualname__    func_dict       __dict__        func_globals    __globals__     func_closure    __closure__     func_code       __code__        func_defaults   __defaults__    __kwdefaults__  __annotations__ _is_coroutine   __module__      __reduce__      Bad call flags for CyFunction   <cyfunction %U at %p>   %.200s() takes no arguments (%zd given) %.200s() takes exactly one argument (%zd given) %.200s() takes no keyword arguments     unbound method %.200S() needs an argument       %.200s() needs an argument      _cython_3_0_11.cython_function_or_method        %s (%s:%d)      does not match          compile time Python version %d.%d of module '%.100s' %s runtime version %d.%d   requests        func.score.oper_score   check_draw      progress_limit  jsonPrompt      绘画扩展提示词:  progress_thread DrawQueueList   headers ttsCore indexUid    画画  info        http绘画接口处理："      key_verify      draw_thread     show_image      draw_prompt_do  exception   绘画  *   width   r       isExtend        nsfw_fun    filter      default imgb64  img     SecretData      cline_in_traceback  count   height      threading       DrawCore.http_draw      func.tools.singleton_mode   open        baseModel        V�   drawUrl outputTxt       refiner_checkpoint      DrawCore.msg_deal       queries DPM++ SDE Karras        has_field       func.tts.tts_core       highlightPreTag __prepare__     realvisxlV30Turbo_v30TurboBakedvae      》完成       TranslateCore   【draw】发生了异常：    time    b64decode       CommonData      【鉴黄】发生了异常：  current_image   func.nsfw.nsfw_core     txt2img func.gobal.data ,       DrawCore.draw_prompt_do secretData  verify      __main__        progress        has_string_reg_list hits    ： __qualname__    Image   round   ^   draw        __module__      hits_temp       绘画图片    save    negative_prompt BytesIO self    DrawCore.draw_prompt    username        /sdapi/v1/progress      DrawCore.draw   LANCZOS __dict__        �U�   images_v6               Bearer 102312c2b83ea0ef9ac32e7858f742721bbfd7319a957272e746f84fd1e974af _initializing   httpProxies io  prompt  drawname        》输出进度：              https://civitai.com/api/trpc/image.getGenerationData?input={"json":{"id":   q   状态提示    ((      Ai_Name aspectRatio facets  nsfw    target      NsfwCore    [   url     draw_prompt信息回复异常：        ：绘画发现一张黄图《  result  range   %发现黄图-nsfw: random      DrawCore.inner_draw switch      正在绘图《 socre_thread    createdAtUnix   inner_draw      physical_save_folder    _is_coroutine   super   __spec__        msg_deal    %   》，  post    start   》进度       ]绘画提示：        generationProcess   append              0123456789abcdef0123456789ABCDEF    ?   drawData        func.tools.string_util  steps   __import__  PIL __ais-highlight__       info_response   》输出进度：100%  query   log             00010203040506070809101112131415161718192021222324252627282930313233343536373839404142434445464748495051525354555657585960616263646566676869707172737475767778798081828384858687888990919293949596979899        __doc__ __class_getitem__       画了一张画《      %鉴黄失败，图片不明确跳出   ObsInit info_meta       __set_name__    func.obs.obs_init       response        info_url    status      》没找到绘画扩展提示词       oper_score  uid func.log.default_log    is_drawing      timestamp   error   meta        DrawCore        nsfwData        nsfw_limit      TTsCore highlightPostTag    type        __name__        DefaultLog      __init_subclass__   resize      filterEn        nsfw_progress_limit     hr_checkpoint_name  p   》,进度      绘画生成错误:     绘图《   limit   }}  _       assistant_tts_say       https://meilisearch-v1-9.civitai.com/multi-search       your_score      NsfwData    get sampler_index   payload channel 回复  J:\ai\吟美打包\tt2\draw\draw_core.py    seed        nsfwLevel=1 num attributesToHighlight   %绿色图片-nsfw:     timeout data    sampler show_text   images  text        __test__        func.tools.decorator_mode       "绘画《      DrawData    json        draw_core       ,输出进度图    )), ,进度跳过   e       commonData      DrawCore.progress   key i       toolNames   base64      DrawCore.__init__       ]你的积分小于     DrawCore.check_draw     StringUtil      Authorization   is_contain      is_index_contain_string uface   draw_prompt     >>条数： put user.username   cfg_scale   .jpg    empty       ：绘画鉴黄失败《    flag        techniqueNames  Thread  绘画进度    results .       tagNames        __metaclass__   randrange   offset      /sdapi/v1/txt2img       getLogger       》，禁止执行      operScore       抽取: drawcontent obs args    proxies http_draw       __init__        jsonEnd traceid 启动绘画:   nsfwCore        jsonStr strip   画画参数： asyncio.coroutines      draw_json   name    get_ws      checkpoint  sleep   score       queryExtract    user_name   isNone  id  >       translate       singleton   《 __/ais-highlight__              00010203040506071011121314151617202122232425262730313233343536374041424344454647505152535455565760616263646566677071727374757677    <lora:      ，不能进行绘画，请通过聊天和点赞赚取积分，查询积分请输入"我的积分"  》 drawName        cfgScale        negativePrompt  C站提示词:  get_score   path        func.translate.translate_core   translateCore   OperScore