# This file was autogenerated by uv via the following command:
#    uv pip compile requirements.txt -o requirements.lock
aenum==3.1.16
    # via reportportal-client
aiohappyeyeballs==2.6.1
    # via aiohttp
aiohttp==3.12.14
    # via
    #   -r requirements.txt
    #   maim-message
    #   reportportal-client
aiosignal==1.4.0
    # via aiohttp
annotated-types==0.7.0
    # via pydantic
anyio==4.9.0
    # via
    #   httpx
    #   openai
    #   starlette
apscheduler==3.11.0
    # via -r requirements.txt
attrs==25.3.0
    # via
    #   aiohttp
    #   jsonlines
certifi==2025.7.9
    # via
    #   httpcore
    #   httpx
    #   reportportal-client
    #   requests
cffi==1.17.1
    # via cryptography
charset-normalizer==3.4.2
    # via requests
click==8.2.1
    # via uvicorn
colorama==0.4.6
    # via
    #   -r requirements.txt
    #   click
    #   tqdm
contourpy==1.3.2
    # via matplotlib
cryptography==45.0.5
    # via
    #   -r requirements.txt
    #   maim-message
customtkinter==5.2.2
    # via -r requirements.txt
cycler==0.12.1
    # via matplotlib
darkdetect==0.8.0
    # via customtkinter
distro==1.9.0
    # via openai
dnspython==2.7.0
    # via pymongo
dotenv==0.9.9
    # via -r requirements.txt
faiss-cpu==1.11.0
    # via -r requirements.txt
fastapi==0.116.0
    # via
    #   -r requirements.txt
    #   maim-message
    #   strawberry-graphql
fonttools==4.58.5
    # via matplotlib
frozenlist==1.7.0
    # via
    #   aiohttp
    #   aiosignal
graphql-core==3.2.6
    # via strawberry-graphql
h11==0.16.0
    # via
    #   httpcore
    #   uvicorn
httpcore==1.0.9
    # via httpx
httpx==0.28.1
    # via openai
idna==3.10
    # via
    #   anyio
    #   httpx
    #   requests
    #   yarl
igraph==0.11.9
    # via python-igraph
jieba==0.42.1
    # via -r requirements.txt
jiter==0.10.0
    # via openai
joblib==1.5.1
    # via scikit-learn
json-repair==0.47.6
    # via -r requirements.txt
jsonlines==4.0.0
    # via -r requirements.txt
kiwisolver==1.4.8
    # via matplotlib
maim-message==0.3.8
    # via -r requirements.txt
markdown-it-py==3.0.0
    # via rich
matplotlib==3.10.3
    # via
    #   -r requirements.txt
    #   seaborn
mdurl==0.1.2
    # via markdown-it-py
multidict==6.6.3
    # via
    #   aiohttp
    #   yarl
networkx==3.5
    # via -r requirements.txt
numpy==2.3.1
    # via
    #   -r requirements.txt
    #   contourpy
    #   faiss-cpu
    #   matplotlib
    #   pandas
    #   scikit-learn
    #   scipy
    #   seaborn
openai==1.95.0
    # via -r requirements.txt
packaging==25.0
    # via
    #   -r requirements.txt
    #   customtkinter
    #   faiss-cpu
    #   matplotlib
    #   strawberry-graphql
pandas==2.3.1
    # via
    #   -r requirements.txt
    #   seaborn
peewee==3.18.2
    # via -r requirements.txt
pillow==11.3.0
    # via
    #   -r requirements.txt
    #   matplotlib
propcache==0.3.2
    # via
    #   aiohttp
    #   yarl
psutil==7.0.0
    # via -r requirements.txt
pyarrow==20.0.0
    # via -r requirements.txt
pycparser==2.22
    # via cffi
pydantic==2.11.7
    # via
    #   -r requirements.txt
    #   fastapi
    #   maim-message
    #   openai
pydantic-core==2.33.2
    # via pydantic
pygments==2.19.2
    # via rich
pymongo==4.13.2
    # via -r requirements.txt
pyparsing==3.2.3
    # via matplotlib
pypinyin==0.54.0
    # via -r requirements.txt
python-dateutil==2.9.0.post0
    # via
    #   -r requirements.txt
    #   matplotlib
    #   pandas
    #   strawberry-graphql
python-dotenv==1.1.1
    # via
    #   -r requirements.txt
    #   dotenv
python-igraph==0.11.9
    # via -r requirements.txt
python-multipart==0.0.20
    # via strawberry-graphql
pytz==2025.2
    # via pandas
quick-algo==0.1.3
    # via -r requirements.txt
reportportal-client==5.6.5
    # via -r requirements.txt
requests==2.32.4
    # via
    #   -r requirements.txt
    #   reportportal-client
rich==14.0.0
    # via -r requirements.txt
ruff==0.12.2
    # via -r requirements.txt
scikit-learn==1.7.0
    # via -r requirements.txt
scipy==1.16.0
    # via
    #   -r requirements.txt
    #   scikit-learn
seaborn==0.13.2
    # via -r requirements.txt
setuptools==80.9.0
    # via -r requirements.txt
six==1.17.0
    # via python-dateutil
sniffio==1.3.1
    # via
    #   anyio
    #   openai
starlette==0.46.2
    # via fastapi
strawberry-graphql==0.275.5
    # via -r requirements.txt
structlog==25.4.0
    # via -r requirements.txt
texttable==1.7.0
    # via igraph
threadpoolctl==3.6.0
    # via scikit-learn
toml==0.10.2
    # via -r requirements.txt
tomli==2.2.1
    # via -r requirements.txt
tomli-w==1.2.0
    # via -r requirements.txt
tomlkit==0.13.3
    # via -r requirements.txt
tqdm==4.67.1
    # via
    #   -r requirements.txt
    #   openai
typing-extensions==4.14.1
    # via
    #   fastapi
    #   openai
    #   pydantic
    #   pydantic-core
    #   strawberry-graphql
    #   typing-inspection
typing-inspection==0.4.1
    # via pydantic
tzdata==2025.2
    # via
    #   pandas
    #   tzlocal
tzlocal==5.3.1
    # via apscheduler
urllib3==2.5.0
    # via
    #   -r requirements.txt
    #   requests
uvicorn==0.35.0
    # via
    #   -r requirements.txt
    #   maim-message
websockets==15.0.1
    # via
    #   -r requirements.txt
    #   maim-message
yarl==1.20.1
    # via aiohttp
