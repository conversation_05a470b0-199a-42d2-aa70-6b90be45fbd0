#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
异步事件循环管理器
解决多个事件循环冲突的问题
"""

import asyncio
import threading
import time
from typing import Optional, Callable, Any
import concurrent.futures

class AsyncEventLoopManager:
    """
    全局异步事件循环管理器
    确保整个应用使用同一个事件循环，避免WebSocket连接问题
    """
    
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if hasattr(self, '_initialized'):
            return
        
        self._loop = None
        self._loop_thread = None
        self._running = False
        self._executor = concurrent.futures.ThreadPoolExecutor(max_workers=4)
        self._initialized = True
        
        print("🔧 异步事件循环管理器已初始化")
    
    def start_loop(self):
        """启动全局事件循环（在独立线程中）"""
        if self._running:
            return True
        
        try:
            self._loop_thread = threading.Thread(
                target=self._run_loop,
                daemon=True,
                name="AsyncEventLoop"
            )
            self._loop_thread.start()
            
            # 等待循环启动
            timeout = 10
            start_time = time.time()
            while not self._running and (time.time() - start_time) < timeout:
                time.sleep(0.1)
            
            if self._running:
                print("✅ 全局异步事件循环已启动")
                return True
            else:
                print("❌ 启动异步事件循环超时")
                return False
                
        except Exception as e:
            print(f"❌ 启动异步事件循环失败: {e}")
            return False
    
    def _run_loop(self):
        """在专用线程中运行事件循环"""
        try:
            self._loop = asyncio.new_event_loop()
            asyncio.set_event_loop(self._loop)
            self._running = True
            
            print("🔄 异步事件循环开始运行...")
            self._loop.run_forever()
            
        except Exception as e:
            print(f"❌ 事件循环运行错误: {e}")
        finally:
            self._running = False
            print("⏹️ 异步事件循环已停止")
    
    def stop_loop(self):
        """停止事件循环"""
        if not self._running:
            return
        
        try:
            if self._loop and self._loop.is_running():
                self._loop.call_soon_threadsafe(self._loop.stop)
            
            if self._loop_thread and self._loop_thread.is_alive():
                self._loop_thread.join(timeout=5)
            
            self._running = False
            print("✅ 异步事件循环已停止")
            
        except Exception as e:
            print(f"⚠️ 停止事件循环时出错: {e}")
    
    def run_async(self, coro, timeout: float = 30.0):
        """在全局事件循环中运行协程"""
        if not self._running:
            if not self.start_loop():
                raise RuntimeError("无法启动事件循环")
        
        try:
            future = asyncio.run_coroutine_threadsafe(coro, self._loop)
            result = future.result(timeout=timeout)
            return result
            
        except concurrent.futures.TimeoutError:
            print(f"❌ 异步操作超时 ({timeout}秒)")
            raise TimeoutError(f"异步操作超时: {timeout}秒")
        except Exception as e:
            print(f"❌ 异步操作失败: {e}")
            raise
    
    def run_async_safe(self, coro, timeout: float = 30.0, default_value=None):
        """安全运行协程，出错时返回默认值"""
        try:
            return self.run_async(coro, timeout)
        except Exception as e:
            print(f"⚠️ 异步操作失败，返回默认值: {e}")
            return default_value
    
    def schedule_callback(self, callback: Callable, *args, **kwargs):
        """在事件循环中调度回调函数"""
        if not self._running:
            return False
        
        try:
            def safe_callback():
                try:
                    callback(*args, **kwargs)
                except Exception as e:
                    print(f"⚠️ 回调函数执行失败: {e}")
            
            self._loop.call_soon_threadsafe(safe_callback)
            return True
            
        except Exception as e:
            print(f"❌ 调度回调失败: {e}")
            return False
    
    def create_task_safe(self, coro):
        """安全创建异步任务"""
        if not self._running:
            if not self.start_loop():
                return None
        
        try:
            def create_task():
                return self._loop.create_task(coro)
            
            future = asyncio.run_coroutine_threadsafe(
                asyncio.coroutine(lambda: create_task())(), 
                self._loop
            )
            return future.result(timeout=5)
            
        except Exception as e:
            print(f"❌ 创建异步任务失败: {e}")
            return None
    
    def is_running(self) -> bool:
        """检查事件循环是否运行中"""
        return self._running and self._loop and self._loop.is_running()
    
    def get_loop(self) -> Optional[asyncio.AbstractEventLoop]:
        """获取事件循环（仅在同一线程中使用）"""
        return self._loop if self._running else None

# 全局实例
loop_manager = AsyncEventLoopManager()

def run_async_global(coro, timeout: float = 30.0):
    """全局异步运行函数"""
    return loop_manager.run_async(coro, timeout)

def run_async_safe_global(coro, timeout: float = 30.0, default_value=None):
    """全局安全异步运行函数"""
    return loop_manager.run_async_safe(coro, timeout, default_value)

def ensure_event_loop():
    """确保全局事件循环已启动"""
    return loop_manager.start_loop()

def stop_global_event_loop():
    """停止全局事件循环"""
    loop_manager.stop_loop()

# 自动启动事件循环
ensure_event_loop() 