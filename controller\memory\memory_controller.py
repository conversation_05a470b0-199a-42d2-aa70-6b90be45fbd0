from flask import request, jsonify
from controller.baseview import BaseView
from func.memory.memory_core import MemoryCore

class MemorySearch(BaseView):
    def post(self):
        data = request.get_json()
        memory_core = MemoryCore()
        result = memory_core.memory_search(data)
        return jsonify(result)

class MemoryResetInputdata(BaseView):
    def post(self):
        data = request.get_json()
        memory_core = MemoryCore()
        result = memory_core.memory_reset_inputdata(data)
        return jsonify(result)