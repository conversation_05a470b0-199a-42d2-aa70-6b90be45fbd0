J:\ai\�������\ai-yinmei\func\analysis\analysis_core.c  analysis_core.py        __init__        analysis_core.AnalysisCore.__init__     intent_analysis analysis_core.AnalysisCore.intent_analysis      relation_query  analysis_core.AnalysisCore.relation_query       analysis_core   Interpreter change detected - this module can only be loaded into one interpreter per process.  name    __loader__  loader      __file__    origin      __package__ parent      __path__        submodule_search_locations      Module 'analysis_core' has already been imported. Re-initialisation is not supported.   builtins        cython_runtime  __builtins__    init analysis_core      %s() got multiple values for keyword argument '%U'      %.200s() keywords must be strings       %s() got an unexpected keyword argument '%U'    exactly s       %.200s() takes %.8s %zd positional argument%.1s (%zd given)     name '%U' is not defined         while calling a Python object  NULL result without error in PyObject_Call      cannot import name %S   _cython_3_0_11  Shared Cython type %.200s is not a type object  Shared Cython type %.200s has the wrong size, try recompiling   keywords must be strings        __name__ must be set to a string object __qualname__ must be set to a string object     function's dictionary may not be deleted        setting function's dictionary to a non-dict     __defaults__ must be set to a tuple object      changes to cyfunction.__defaults__ will not currently affect the values used in function calls  __kwdefaults__ must be set to a dict object     changes to cyfunction.__kwdefaults__ will not currently affect the values used in function calls        __annotations__ must be set to a dict object    func_doc        __doc__ func_name       __name__        __qualname__    func_dict       __dict__        func_globals    __globals__     func_closure    __closure__     func_code       __code__        func_defaults   __defaults__    __kwdefaults__  __annotations__ _is_coroutine   __module__      __reduce__      Bad call flags for CyFunction   <cyfunction %U at %p>   %.200s() takes no arguments (%zd given) %.200s() takes exactly one argument (%zd given) %.200s() takes no keyword arguments     unbound method %.200S() needs an argument       %.200s() needs an argument      _cython_3_0_11.cython_function_or_method        %s (%s:%d)      does not match          compile time Python version %d.%d of module '%.100s' %s runtime version %d.%d   requests    *   AnalysisCore.relation_query .   headers key_verify      exception       default SecretData      cline_in_traceback      graphdbData     func.tools.singleton_mode       0� �   GraphdbData     __prepare__     /relation_query J:\ai\吟美打包\ai-yinmei\func\analysis\analysis_core.py 聊天      AnalysisCore.__init__   func.gobal.data jsonData        intent_analysis secretData  verify      __main__        __qualname__    __module__      relation_query  self    __dict__        �� �   _initializing   AnalysisCore    url     AnalysisCore.intent_analysis    switch  _is_coroutine   super   __spec__    post        application/json        analysis_core   AnalysisData    __import__      /intent_classify    log __doc__ datalist        __set_name__    status  func.log.default_log    __name__        扩展思维    DefaultLog      __init_subclass__       Content-Type    ?   num timeout data    text    __test__        func.tools.decorator_mode   json        意图分析    key     analysisData    __metaclass__   intent_analysis异常   getLogger       server_url      __init__        asyncio.coroutines  name        singleton       classify        relation_query异常